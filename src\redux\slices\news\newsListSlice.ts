import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { INews } from "../../../types/news";
import { request } from "../../../utils/request";
import { NewPosition } from "@/constants/Const";

interface NewsState {
  list: any;
  newsList: INews[];
  bannerHome: INews[];
  bannerProduct: INews[];
  bannerAffiliate: INews[];
  bannerAccount: INews[];
  userNewsList: INews[];
  bannerWithBranchShopConfig: INews[];
  bannerHomeShopConfig: INews[];
  isLoading: boolean;
  error: string | null;
  listArticleMap: { [key: string]: INews[] };
  isLoadingMap: { [key: string]: boolean };
}

const initialState: NewsState = {
  list: [],
  newsList: [],
  bannerHome: [],
  bannerProduct: [],
  bannerAffiliate: [],
  bannerAccount: [],
  userNewsList: [],
  bannerWithBranchShopConfig: [],
  bannerHomeShopConfig: [],
  isLoading: true,
  error: null,
  listArticleMap: {},
  isLoadingMap: {},
};

export const getNewsList = createAsyncThunk("newsList/getNewsList", async () => {
  const response: any = await request(
    "get",
    `/api/user/articleuser/listarticle?position=${NewPosition.Other}&skip=0&limit=99`
  );
  return response;
});

export const getBannerHome1 = createAsyncThunk("newsList/getBannerHome1", async () => {
  const response: any = await request(
    "get",
    `/api/user/articleuser/listarticle?position=${NewPosition.BannerHome1}&skip=0&limit=99`
  );
  return response;
});

export const getBannerProduct = createAsyncThunk("newsList/getBannerProduct", async () => {
  const response: any = await request(
    "get",
    `/api/user/articleuser/listarticle?position=${NewPosition.BannerProduct}&skip=0&limit=99`
  );
  return response;
});
export const getBannerAffiliate = createAsyncThunk("newsList/getBannerAffiliate", async () => {
  const response: any = await request(
    "get",
    `/api/user/articleuser/listarticle?position=${NewPosition.BannerAffiliate}&skip=0&limit=99`
  );
  return response;
});
export const getBannerAccount = createAsyncThunk("newsList/getBannerAccount", async () => {
  const response: any = await request(
    "get",
    `/api/user/articleuser/listarticle?position=${NewPosition.BannerAccount}&skip=0&limit=99`
  );
  return response;
});

export const getListNewsUser = createAsyncThunk(
  "newsList/getListNewsUser",
  async (shopId: string) => {
    const response: any = await request(
      "get",
      `/api/user/articleuser/listarticle?ShopId=${shopId}&skip=0&limit=99`
    );
    return response;
  }
);

export const getListArticleUserByArticleIds = createAsyncThunk(
  "newsList/getListArticleUserByArticleIds",
  async (data: any) => {
    const response: any = await request(
      "post",
      `/api/user/articleuser/listarticlebyarticleids?skip=0&limit=99`,
      data
    );
    return response;
  }
);

export const getListArticleBannerBranchShopConfig = createAsyncThunk(
  "newsList/getListArticleBannerBranchShopConfig",
  async (data: any) => {
    const response: any = await request(
      "post",
      `/api/user/articleuser/listarticlebyarticleids?skip=0&limit=99`,
      data
    );
    return response;
  }
);

export const getListArticleBannerHomeShopConfig = createAsyncThunk(
  "newsList/getListArticleBannerHomeShopConfig",
  async (data: any) => {
    const response: any = await request(
      "post",
      `/api/user/articleuser/listarticlebyarticleids?skip=0&limit=99`,
      data
    );
    return response;
  }
);

export const getNewsListByParams = createAsyncThunk(
  "newsList/getNewsListByParams",
  async (params: {
    position: string;
    shopId: string;
    articleCategoryId: string;
    skip?: number;
    limit?: number;
  }) => {
    const { position, shopId, articleCategoryId, skip = 0, limit = 99 } = params;
    const response: any = await request(
      "get",
      `/api/user/articleuser/listarticle?position=${position}&skip=${skip}&limit=${limit}&shopId=${shopId}&articleCategoryId=${articleCategoryId}`
    );
    return response;
  }
);

export const fetchListArticleMap = createAsyncThunk(
  "newsList/fetchListArticleMap",
  async (params: { shopId: string; articleCategoryId?: string; key: string }, { dispatch }) => {
    const { shopId, articleCategoryId, key } = params;
    let res;
    if (articleCategoryId) {
      res = await dispatch(
        getNewsListByParams({
          position: "Other",
          shopId,
          articleCategoryId,
          skip: 0,
          limit: 99,
        })
      );
    } else {
      res = await dispatch(getListNewsUser(shopId));
    }
    return { key, data: res?.payload?.data || [] };
  }
);

const newsSlice = createSlice({
  name: "newsList",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getNewsList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getNewsList.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.newsList = payload.data;
        state.isLoading = false;
      })
      .addCase(getNewsList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getBannerHome1.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getBannerHome1.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.bannerHome = payload.data;
        state.isLoading = false;
      })
      .addCase(getBannerHome1.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(getListNewsUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getListNewsUser.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.userNewsList = payload.data;
        state.isLoading = false;
      })
      .addCase(getListNewsUser.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getListArticleBannerBranchShopConfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getListArticleBannerBranchShopConfig.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.bannerWithBranchShopConfig = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getListArticleBannerBranchShopConfig.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getListArticleBannerHomeShopConfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getListArticleBannerHomeShopConfig.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.bannerHomeShopConfig = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getListArticleBannerHomeShopConfig.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getNewsListByParams.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getNewsListByParams.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.list = payload.data;
        state.isLoading = false;
      })
      .addCase(getNewsListByParams.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(fetchListArticleMap.pending, (state, action) => {
        const key = action.meta.arg.key;
        state.isLoadingMap[key] = true;
      })
      .addCase(fetchListArticleMap.fulfilled, (state, action) => {
        const { key, data } = action.payload;
        state.listArticleMap[key] = data;
        state.isLoadingMap[key] = false;
      })
      .addCase(fetchListArticleMap.rejected, (state, action) => {
        const key = action.meta.arg.key;
        state.isLoadingMap[key] = false;
      });
  },
});

export default newsSlice.reducer;
