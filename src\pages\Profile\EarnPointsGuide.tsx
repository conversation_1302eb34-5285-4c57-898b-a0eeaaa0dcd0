import ShareFriendIcon from "@/components/icon/ShareFriendIcon";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import LoginPopup from "@/components/LoginPopup";
import PopUpShareLink from "@/components/UI/PopUpShareLink";
import { Platform } from "@/config";
import { StorageKeys } from "@/constants/storageKeys";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { authZalo, getUser, getUserZalo } from "@/redux/slices/authen/authSlice";
import { fetchConfigPoint } from "@/redux/slices/membershiplevel/membershiplevelSlice";
import type { AppDispatch, RootState } from "@/redux/store";
import { mapError } from "@/utils/common";
import { getItem } from "@/utils/storage";
import { Box, Button, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

export default function EarnPointsGuide() {
  const navigate = useNavigate();
  const { color } = useConfigApp();
  const appConfig = useConfigApp();
  const dispatch = useDispatch<AppDispatch>();
  const { showAlert } = useAlert();
  const calcCartAfterLogin = useCalcCartAfterLogin();
  const [loading, setLoading] = useState(false);
  const [openLoginPopup, setOpenLoginPopup] = useState(false);
  const [isOpenShareLink, setIsOpenShareLink] = useState(false);

  const shopId = useSelector((state: RootState) => state.appInfo.shopId);
  const configPoint = useSelector((state: RootState) => state.membershiplevel.configPoint);
  const user = useSelector((state: RootState) => state.auth.user);

  const onClickRegister = async () => {
    setLoading(true);
    const refCode = await getItem(StorageKeys.RefCode);
    const res = await dispatch(authZalo(refCode)).unwrap();
    if (res && res.idFor) {
      if (Platform === "zalo") await dispatch(getUserZalo());
      await dispatch(getUser());
      await calcCartAfterLogin();
      showAlert({
        icon: "✅",
        title: "Kích hoạt tài khoản thành công",
      });
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoading(false);
  };

  const onClickToActiveProfile = () => {
    showAlert({
      title: appConfig.shopName,
      content: "Vui lòng kích hoạt tài khoản để tiếp tục!",
      icon: "⚠️",
      buttons: [
        {
          title: "Đóng",
          action: () => {},
        },
        {
          title: "Kích hoạt",
          action: () => {
            Platform === "zalo" ? onClickRegister() : setOpenLoginPopup(true);
          },
        },
      ],
    });
  };

  useEffect(() => {
    if (shopId) {
      dispatch(fetchConfigPoint({ shopId }));
    }
  }, [shopId, dispatch, user]);

  if (!configPoint) {
    return (
      <>
        <LoginPopup
          open={openLoginPopup}
          onClose={() => setOpenLoginPopup(false)}
          onClickRegister={onClickRegister}
          loading={loading}
        />
        <FrameContainerFull title="Cách kiếm điểm">
          <Box
            sx={{
              minHeight: "100vh",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Typography>Đang tải dữ liệu...</Typography>
          </Box>
        </FrameContainerFull>
      </>
    );
  }

  const registerPoint = configPoint?.earnPoint?.registerJson?.rule ?? 0;
  const sharePoint = configPoint?.earnPoint?.shareJson?.rule ?? 0;
  const shareMinSpent = configPoint?.earnPoint?.shareJson?.isPurchase?.minSpent ?? 0;
  const birthdayPoint = 100;

  // Các điều kiện hiển thị box
  const showRegister = configPoint?.earnPoint?.registerJson?.status === true;
  const showOrder = configPoint?.earnPoint?.orderJson?.status === true;
  const showShare = configPoint?.earnPoint?.shareJson?.status === true;

  return (
    <>
      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={loading}
      />
      <PopUpShareLink isOpen={isOpenShareLink} setIsOpen={setIsOpenShareLink} />
      <FrameContainerFull title="Cách kiếm điểm">
        <Box
          sx={{
            bgcolor: appConfig.container?.backgroundColor,
            minHeight: "100vh",
            height: "100dvh",
            display: "flex",
            flexDirection: "column",
            p: 0,
            overflow: "hidden",
          }}
        >
          <Box sx={{ flex: 1, overflowY: "auto", p: 2, pt: 1, pb: 8 }}>
            {/* Thưởng khi đăng ký */}
            {showRegister && (
              <Box
                sx={{
                  bgcolor: "#fff",
                  borderRadius: 3,
                  p: 2,
                  mb: 2,
                  display: "flex",
                  alignItems: "center",
                  boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
                  minHeight: 110,
                }}
              >
                <Box
                  sx={{
                    width: 65,
                    height: 65,
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    mr: 3,
                    flexShrink: 0,
                  }}
                >
                  <img
                    src="/images/thuongdangky.png"
                    alt="Thưởng khi đăng ký"
                    style={{
                      width: 65,
                      height: 65,
                      objectFit: "contain",
                      display: "block",
                      margin: "auto",
                    }}
                    onError={(e) => {
                      e.currentTarget.onerror = null;
                      e.currentTarget.src = "/images/icon_accumulate_point.svg";
                    }}
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography sx={{ fontWeight: 700, fontSize: 16, color: color.primary, mb: 0.5 }}>
                    Thưởng khi đăng ký
                  </Typography>
                  <Typography sx={{ fontSize: 14, color: "#161616" }}>
                    Bạn nhận được <b style={{ color: color.primary }}>{registerPoint}</b> điểm khi
                    tạo tài khoản mới
                  </Typography>
                </Box>
              </Box>
            )}
            {/* Tích điểm trên đơn hàng */}
            {showOrder && (
              <Box
                sx={{
                  bgcolor: "#fff",
                  borderRadius: 3,
                  p: 2,
                  mb: 2,
                  display: "flex",
                  alignItems: "center",
                  boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
                  minHeight: 110,
                }}
              >
                <Box
                  sx={{
                    width: 65,
                    height: 65,
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    mr: 3,
                    flexShrink: 0,
                  }}
                >
                  <img
                    src="/images/tichdiem.png"
                    alt="Tích điểm trên đơn hàng"
                    style={{
                      width: 65,
                      height: 65,
                      objectFit: "contain",
                      display: "block",
                      margin: "auto",
                    }}
                    onError={(e) => {
                      e.currentTarget.onerror = null;
                      e.currentTarget.src = "/images/icon_accumulate_point.svg";
                    }}
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography sx={{ fontWeight: 700, fontSize: 16, color: color.primary, mb: 0.5 }}>
                    Tích điểm trên đơn hàng
                  </Typography>
                  <Typography sx={{ fontSize: 14, color: "#161616" }}>
                    Hoàn thành đơn hàng và nhận điểm thưởng theo tỉ lệ của chương trình thẻ thành
                    viên
                  </Typography>
                </Box>
              </Box>
            )}
            {/* Thêm bạn thêm vui */}
            {showShare && (
              <Box
                sx={{
                  bgcolor: "#fff",
                  borderRadius: 3,
                  p: 2,
                  mb: 2,
                  display: "flex",
                  alignItems: "center",
                  boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
                  minHeight: 110,
                }}
              >
                <Box
                  sx={{
                    width: 65,
                    height: 65,
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    mr: 3,
                    flexShrink: 0,
                  }}
                >
                  <img
                    src="/images/thembam.png"
                    alt="Thêm bạn thêm vui"
                    style={{
                      width: 65,
                      height: 65,
                      objectFit: "contain",
                      display: "block",
                      margin: "auto",
                    }}
                    onError={(e) => {
                      e.currentTarget.onerror = null;
                      e.currentTarget.src = "/images/icon_accumulate_point.svg";
                    }}
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography sx={{ fontWeight: 700, fontSize: 16, color: color.primary, mb: 0.5 }}>
                    Thêm bạn thêm vui
                  </Typography>
                  <Typography sx={{ fontSize: 14, color: "#161616" }}>
                    Nhận <b style={{ color: color.primary }}>{sharePoint}</b> điểm khi giới thiệu
                    bạn bè vào app tạo tài khoản thành công và mua sắm tối thiểu{" "}
                    <b style={{ color: color.primary }}>{shareMinSpent.toLocaleString()}</b> VNĐ
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
                    <Box sx={{ mr: 1, display: "flex" }}>
                      <ShareFriendIcon width={17} height={20} fill={color.primary} />
                    </Box>
                    <Button
                      variant="text"
                      size="small"
                      sx={{
                        color: color.primary,
                        fontWeight: 500,
                        fontSize: 16,
                        textTransform: "none",
                        textDecoration: "underline",
                        minWidth: 0,
                        p: 0,
                      }}
                      onClick={() => setIsOpenShareLink(true)}
                    >
                      Chia sẻ ngay
                    </Button>
                  </Box>
                </Box>
              </Box>
            )}
            {/* Nhận điểm qua QR tích điểm */}
            <Box
              sx={{
                bgcolor: "#fff",
                borderRadius: 3,
                p: 2,
                mb: 2,
                display: "flex",
                alignItems: "center",
                boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
                minHeight: 110,
              }}
            >
              <Box
                sx={{
                  width: 65,
                  height: 65,
                  borderRadius: "50%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  mr: 3,
                  flexShrink: 0,
                }}
              >
                <img
                  src="/images/nhandiem.png"
                  alt="Nhận điểm qua QR tích điểm"
                  style={{
                    width: 65,
                    height: 65,
                    objectFit: "contain",
                    display: "block",
                    margin: "auto",
                  }}
                  onError={(e) => {
                    e.currentTarget.onerror = null;
                    e.currentTarget.src = "/images/icon_accumulate_point.svg";
                  }}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography sx={{ fontWeight: 700, fontSize: 16, color: color.primary, mb: 0.5 }}>
                  Nhận điểm qua QR tích điểm
                </Typography>
                <Typography sx={{ fontSize: 14, color: "#161616" }}>
                  Nhận điểm thưởng theo chương trình QR tích điểm được ban hành tuỳ từng thời điểm
                </Typography>
              </Box>
            </Box>
            {/* Thưởng mừng sinh nhật */}
            {/* <Box
              sx={{
                bgcolor: "#fff",
                borderRadius: 3,
                p: 2,
                mb: 2,
                display: "flex",
                alignItems: "center",
                boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
                minHeight: 110,
              }}
            >
              <Box
                sx={{
                  width: 65,
                  height: 65,
                  borderRadius: "50%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  mr: 3,
                  flexShrink: 0,
                }}
              >
                <img
                  src="/images/thuongsinhnhat.png"
                  alt="Thưởng mừng sinh nhật"
                  style={{
                    width: 65,
                    height: 65,
                    objectFit: "contain",
                    display: "block",
                    margin: "auto",
                  }}
                  onError={(e) => {
                    e.currentTarget.onerror = null;
                    e.currentTarget.src = "/images/icon_accumulate_point.svg";
                  }}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography sx={{ fontWeight: 700, fontSize: 16, color: color.primary, mb: 0.5 }}>
                  Thưởng mừng sinh nhật
                </Typography>
                <Typography sx={{ fontSize: 14, color: "#161616" }}>
                  Nhận <b style={{ color: color.primary }}>{birthdayPoint}</b> điểm vào ngày sinh
                  nhật
                </Typography>
                <Button
                  variant="outlined"
                  size="medium"
                  sx={{
                    color: color.primary,
                    borderColor: color.primary,
                    borderRadius: 99,
                    fontWeight: 500,
                    fontSize: 16,
                    px: 1.5,
                    py: 0.5,
                    textTransform: "none",
                    lineHeight: 1.2,
                    mt: 1,
                  }}
                >
                  Cập nhật ngày sinh
                </Button>
              </Box>
            </Box> */}
          </Box>
        </Box>
      </FrameContainerFull>
    </>
  );
}
