import {
  <PERSON><PERSON>,
  Box,
  Button,
  Di<PERSON>r,
  <PERSON>rid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import React, { useEffect, useState } from "react";

import { COLOR, COLORS } from "../../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";

export const ProductComment = () => {
  const { color } = useConfigApp();
  return (
    <Box style={styles.commentHeader}>
      <Box style={styles.commentHeaderTop}>
        <Box>
          <Typography
            fontSize={17}
            fontWeight={500}
            style={styles.commentHeaderTitle}
          >
            Đ<PERSON><PERSON>ản Phẩm
          </Typography>
          <Box style={styles.commentHeaderRatingInfo} mt={1}>
            <Rating value={5} precision={0.1} readOnly size="small" />
            <Typography
              variant="body1"
              style={{
                ...styles.ratingValue,
                color: color.primary,
              }}
            >
              5/5
            </Typography>
            <Typography variant="body2" style={styles.ratingCount}>
              (800 đánh giá)
            </Typography>
          </Box>
        </Box>
        <Link underline="none" style={styles.seeAllLink}>
          Xem tất cả &gt;
        </Link>
      </Box>
      <Box style={styles.commentContainer}>
        {/* User Info */}
        <Box style={styles.userInfoContainer}>
          <Avatar
            alt="Hong Hea In"
            src="/path/to/profile-pic.jpg"
            style={styles.avatar}
          />
          <Box>
            <Box style={styles.userRatingInfo}>
              <Box>
                <Typography
                  fontSize={13}
                  color={COLOR.text.product_user}
                  style={styles.userName}
                >
                  Hong Hea In
                </Typography>
                <Rating value={4} readOnly size="small" />
              </Box>
              <Typography fontSize={12} style={styles.commentTime}>
                12:44 | 10-12-2024
              </Typography>
            </Box>
            <Typography fontSize={12} style={styles.productType}>
              Phân loại: NEW 942
            </Typography>
            {/* Comment */}
            <Typography
              fontSize={14}
              color={COLOR.text.product_comment}
              style={styles.commentText}
            >
              Son đẹp lắm ạ dùng rất thích luôn. Mọi người nên mua nhé màu sắc
              đa dạng phong phú. son dùng mịn môi ko nhanh trôi lắm , giá phải
              chăng . nhưng mà sale thì rẻ lắm nha mọi người ơi!!!! 🥰🥰🥰
            </Typography>

            {/* Image placeholders */}
            <Grid container spacing={2}>
              {Array.from({ length: 3 }).map((_, index) => (
                <Grid item xs={4} key={index}>
                  <Box style={styles.imagePlaceholder} />
                </Grid>
              ))}
            </Grid>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

const styles: Record<string, React.CSSProperties> = {
  headerContainer: {
    display: "flex",
    alignItems: "baseline",
    justifyContent: "space-between",
  },
  rowContainer: {
    display: "flex",
    alignItems: "center",
    gap: 4,
  },
  contentContainer: {
    background: "#fff",
    paddingInline: 16,
    paddingBlock: 8,
  },
  finalPriceText: {
    color: COLORS.primary1,
    fontWeight: 700,
    fontStyle: "italic",
    fontSize: 20,
  },
  discountPercentText: {
    color: COLORS.primary1,
    fontStyle: "italic",
    marginInline: 4,
  },
  priceText: {
    color: COLORS.neutral4,
    display: "inline-block",
    fontStyle: "italic",
    paddingInline: 2,
  },
  ratingInfo: {
    alignItems: "center",
  },
  commentHeader: {
    background: "#fff",
    marginTop: 6,
  },
  commentHeaderTop: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: "16px",
    borderBottom: "1px solid #ddd",
  },
  commentHeaderTitle: {
    fontWeight: "bold",
  },
  commentHeaderRatingInfo: {
    display: "flex",
    alignItems: "center",
    gap: 5,
  },
  ratingValue: {
    fontSize: 13,
    fontWeight: 500,
  },
  ratingCount: {
    color: "#C6C6C6",
    fontSize: 13,
    fontWeight: 500,
    lineHeight: 1.5,
  },
  seeAllLink: {
    fontSize: "14px",
    color: "#B8B8B8",
  },
  commentContainer: {
    display: "flex",
    flexDirection: "column",
    gap: 2,
    padding: 16,
    borderTop: "1px solid #ddd",
    width: "100%",
  },
  userInfoContainer: {
    display: "flex",
    alignItems: "start",
    gap: 10,
  },
  avatar: {
    width: 30,
    height: 30,
    marginTop: 8,
  },
  userName: {
    fontWeight: "bold",
    lineHeight: 1.5,
  },
  userRatingInfo: {
    display: "flex",
    alignItems: "start",
    justifyContent: "space-between",
    width: "100%",
  },
  productType: {
    color: "#7D7D7D",
    fontSize: 12,
    fontWeight: 400,
    marginBottom: 10,
  },
  commentTime: {
    color: "#ACACAC",
    fontSize: 12,
    fontWeight: 400,
  },
  commentText: {
    fontSize: 14,
  },
  imagePlaceholder: {
    width: "100%",
    paddingTop: "100%",
    backgroundColor: "#eee",
    border: "1px solid #ddd",
    borderRadius: 1,
  },
  relatedProductsTitle: {
    fontWeight: "bold",
    marginBottom: 10,
  },
  salePriceText: {
    fontSize: 26,
    fontWeight: 700,
  },
  discountPrice: {
    textDecoration: "line-through",
    fontSize: 16,
    fontWeight: 500,
    color: "#C7C7C7",
  },
  discountText: {
    paddingBlock: 2,
    paddingInline: 5,
    color: "#FFF",
    fontSize: 12,
    fontWeight: 700,
    borderRadius: 10,
  },
  soldText: {
    color: "#626262",
    fontSize: 11,
    fontWeight: 500,
  },
  nameText: {
    color: "#252525",
    fontSize: 17,
    fontWeight: 500,
  },
  priceContainer: {
    display: "flex",
    alignItems: "baseline",
    gap: 4,
  },
};
