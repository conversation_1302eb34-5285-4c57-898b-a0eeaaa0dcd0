import { GetUserInfoReturns } from "zmp-sdk";
import { AffiliationStatus } from "./affiliation";

export interface IUserInfo {
  id: number;
  name: string;
  avatar?: string;
}

export interface IUserReferCommission {
  direct: number;
  indirect: number;
}

export interface IUser {
  id: string;
  userId: string;
  shopId: string;
  phoneNumber: string;
  email: string;
  username: string;
  firstname: string;
  lastname: string;
  fullname: string;
  birthdate: Date;
  avatar?: string;
  gender: string;
  provinceId: string;
  provinceName: string;
  districtId: string;
  districtName: string;
  wardId: string;
  wardName: string;
  address: string;
  location: object;
  language: string;
  notes: string;
  status: string;
  created: string;
  updated: string;
  point: number;
  addressId?: number;
  avatarUrl?: string;
  zaloId?: string;
  zaloIdByOA?: string;
  balance?: string | null;
  blocked?: boolean;
  confirmed?: boolean;
  createdAt?: string;
  district?: string | null;
  dob?: string | null;
  emailAddress?: string | null;
  fParent?: number;
  firstOrderAt?: string | null;
  firstOrderCreatedAt?: string | null;
  level?: number;
  mySale?: string | null;
  myTeamSale?: string | null;
  name?: string;
  numberOfSubAccount?: number | null;
  phone?: string;
  provider?: string;
  province?: string | null;
  referCode?: string;
  sChildren?: string | null;
  subBalance?: string | null;
  taxCode?: string | null;
  identifiCode?: string | null;
  updatedAt?: string;
  updatedReferCode?: boolean;
  verified?: boolean;
  ward?: string | null;
  isZaloOA?: boolean;
  hideCampaignIds?: string;
  bonusPoint?: number | null;
  cashbackPercent?: number | null;
  extra?: { showPopupNewLevel?: boolean };
  referralCode?: string; //Mã giới thiệu
  referrerCode?: string; //Mã người giới thiệu
  affiliationStatus?: AffiliationStatus | null;
  bankAccountName?: string | null;
  bankName?: string | null;
  bankAccountNumber?: string | null;
  identityCardNumber?: string | null;
  commission?: {
    sale: IUserReferCommission;
    refer: IUserReferCommission;
    totalCommission: number;
    withdrawalCommission: number;
    managerCommission: number;
    sharedCommission: number;
    pendingCommission: number;
  };
  bankInfo?: {
    owner: string;
    bank: string;
    accountNumber: string;
    branch: string;
    identyfiCode: string;
    taxCode: string;
  };
  membershipLevel?: {
    levelName?: string;
    logo?: string;
  };
  idFor?: string;
}

export type UserZalo = GetUserInfoReturns["userInfo"];
