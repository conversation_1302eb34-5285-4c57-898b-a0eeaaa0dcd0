import React from "react";
import { formatPrice } from "@/utils/formatPrice";
import { COLOR, COLORS, commonStyle } from "@/constants/themes";
import { Box, Button, FormControlLabel, Radio, Stack, Typography } from "@mui/material";
import { VoucherText, VoucherType } from "@/constants/Const";
import { useNavigate } from "@/utils/component-util";
import SmallCheckIcon from "../icon/SmallCheckIcon";
import { VoucherDto } from "@/redux/slices/voucher/voucherSlice";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Router } from "@/constants/Route";

export enum BtnCategory {
  BTN,
  RADIO,
  NONE,
}

export default function VoucherAction({
  item,
  category,
  eventAction,
  value,
  isChecked,
  isMyVoucher,
  isDisabled,
}: {
  item: VoucherDto;
  category?: BtnCategory;
  eventAction: (event: any) => void;
  value?: string;
  isChecked?: boolean;
  isMyVoucher?: boolean;
  isDisabled?: boolean;
}) {
  const { color, bgColor, textColor } = useConfigApp();
  const navigate = useNavigate();
  const onNavigateToProduction = () => {
    navigate(Router.menu);
  };

  if (category === BtnCategory.BTN) {
    return (
      <Button
        // variant="contained"
        // size="large"
        onClick={eventAction}
        className={`btn-voucher ${isMyVoucher ? "disabled" : ""}`}
        // style={{
        //   ...styles.buttonVoucher,
        //   background: color.primary,
        //   color: COLOR.text.white,
        //   height: 30,
        //   fontSize: 12,
        //   padding: 8,
        // }}
        style={{
          // ...styles.buttonVoucher,
          // background: color.primary,
          color: color.primary,
          height: 30,
          fontSize: 12,
          // padding: 8,
        }}
      >
        {value}
      </Button>
    );
  } else if (category === BtnCategory.RADIO) {
    return (
      <Box
        sx={{
          opacity: isDisabled ? 0.5 : 1,
          pointerEvents: isDisabled ? "none" : "auto",
          cursor: isDisabled ? "not-allowed" : "pointer",
        }}
      >
        <FormControlLabel
          value={item.voucherId.toString()}
          disabled={isDisabled}
          control={
            <Radio
              sx={{
                opacity: isDisabled ? 0.5 : 1,
                pointerEvents: isDisabled ? "none" : "auto",
                cursor: isDisabled ? "not-allowed" : "pointer",
              }}
              disabled={isDisabled}
              checkedIcon={<SmallCheckIcon fillColor={color.primary} />}
            />
          }
          label=""
          labelPlacement="start"
          onClick={eventAction}
          checked={isChecked}
        />
      </Box>
    );
  } else {
    return null;
  }
}

const styles: Record<string, React.CSSProperties> = {
  buttonVoucher: {
    fontSize: 15,
    fontWeight: 600,
    borderRadius: "16px",
    height: 30,
    minWidth: 80,
    boxShadow: "none",
    whiteSpace: "nowrap",
  },
};
