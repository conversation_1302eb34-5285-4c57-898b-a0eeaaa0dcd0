import React from "react";

export interface IconCustomProps {
  fillColor?: string;
  secondaryColor?: string;
  className?: string;
}

const ShoppingCartIcon: React.FC<IconCustomProps> = ({ fillColor, secondaryColor }) => {
  return (
    <svg width="20" height="22" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M1.25 1H3.125L6.25 16M6.25 16H16.25M6.25 16C5.58696 16 4.95107 16.2634 4.48223 16.7322C4.01339 17.2011 3.75 17.837 3.75 18.5C3.75 19.163 4.01339 19.7989 4.48223 20.2678C4.95107 20.7366 5.58696 21 6.25 21C6.91304 21 7.54893 20.7366 8.01777 20.2678C8.48661 19.7989 8.75 19.163 8.75 18.5C8.75 17.837 8.48661 17.2011 8.01777 16.7322C7.54893 16.2634 6.91304 16 6.25 16ZM16.25 16C15.587 16 14.9511 16.2634 14.4822 16.7322C14.0134 17.2011 13.75 17.837 13.75 18.5C13.75 19.163 14.0134 19.7989 14.4822 20.2678C14.9511 20.7366 15.587 21 16.25 21C16.913 21 17.5489 20.7366 18.0178 20.2678C18.4866 19.7989 18.75 19.163 18.75 18.5C18.75 17.837 18.4866 17.2011 18.0178 16.7322C17.5489 16.2634 16.913 16 16.25 16ZM5.625 12.25H17.1875L18.75 4.75H4.14"
        stroke={fillColor}
        strokeWidth="1.33"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ShoppingCartIcon;
