export interface IBranch {
  id?: number;
  name?: number;
  address?: string;
  timeOpen?: string;
  timeClose?: string;
  hotline?: string;
  website?: string;
}

export interface IBranchItem {
  id?: number;
  branchId: string;
  shopId: string;
  branchName: string;
  phoneNumber: string;
  image?: string;
  address: string;
  houseNumber?: string;
  provinceId: string;
  provinceName: string;
  districtId: string;
  districtName: string;
  wardId: string;
  wardName: string;
}
