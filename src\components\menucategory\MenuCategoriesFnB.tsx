import ArrowRightIcon from "@/components/icon/ArrowRightIcon";
import MenuCategoryItemFnB from "@/components/menucategory/MenuCategoryItemFnB";
import MenuCategoryItemRetail from "./MenuCategoryItemRetail";
import { Router } from "@/constants/Route";
import { COLOR, COLORS, commonStyle } from "@/constants/themes";
import "@/css/hideScrollbar.css";
import { useConfigApp } from "@/hooks/useConfigApp";
import { AppDispatch, RootState } from "@/redux/store";
import { IMenu } from "@/types/menu";
import { useNavigate } from "@/utils/component-util";
import { Box, Button, Skeleton, Stack, Typography, useTheme } from "@mui/material";
import React from "react";
import "react-horizontal-scrolling-menu/dist/styles.css";
import { useDispatch, useSelector } from "react-redux";
import Slider from "react-slick";
import BtnAction from "../common/BtnAction";

interface MenuCategoriesProps {
  title?: string;
  listMenu: IMenu[];
  onClickMenu: (item: IMenu) => void;
  subBtn?: string;
  onClickSubBtn?: () => void;
  itemOption?: number;
  item: {
    style?: {
      radius?: number;
      title?: string;
      category?: string;
      fontSize?: number;
      fontWeight?: number;
      itemInRow?: number;
    };
  };
}

export default function MenuCategoriesFnB({
  title,
  listMenu,
  onClickMenu,
  subBtn,
  onClickSubBtn,
  itemOption,
  item,
}: MenuCategoriesProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const appConfig = useConfigApp();

  const itemWidth = `${100 / Math.max(item.style?.itemInRow ?? 1, 1)}%`;
  const numberOfItems = listMenu.length;
  const slidesToShow = Math.min(item.style?.itemInRow || 5, numberOfItems);

  const onNavigateToProduction = () => {
    navigate(Router.menu);
  };
  const { shopInfo } = useSelector((state: RootState) => state.appInfo);
  const settings = {
    infinite: numberOfItems > slidesToShow,
    speed: 500,
    slidesToShow,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    dotsClass: "slick-dots custom-dots",
    responsive: [
      {
        breakpoint: 376,
        settings: {
          slidesToShow: Math.min(item.style?.itemInRow || 5, numberOfItems),
          slidesToScroll: 1,
        },
      },
    ],
  };

  function renderContent() {
    const isLoading = listMenu.length === 0;
    if (isLoading) {
      return [...Array(5)].map((_, idx) => (
        <Box
          key={idx}
          sx={{
            width: item.style?.category === "slice" ? "auto" : itemWidth,
            display: "flex !important",
            justifyContent: "center",
          }}
        >
          <Box pt={1}>
            <Skeleton variant="rectangular" width={62} height={62} sx={{ borderRadius: 2 }} />
            <Skeleton variant="text" width={60} height={20} sx={{ mt: 1 }} />
          </Box>
        </Box>
      ));
    }
    return listMenu?.map((menuItem) => (
      <Box
        key={menuItem.id}
        sx={{
          width: item.style?.category === "slice" ? "auto" : itemWidth,
          display: "flex !important",
          justifyContent: "center",
        }}
      >
        <Button
          className="btn-menu-containerssss"
          onClick={() => onClickMenu(menuItem)}
          style={{ paddingInline: 0 }}
        >
          {itemOption === 1 && <MenuCategoryItemFnB item={menuItem} />}
          {itemOption === 2 && (
            <MenuCategoryItemRetail
              item={menuItem}
              shopInfo={{ ...shopInfo, businessType: shopInfo.businessType || "" }}
              style={item.style || {}}
            />
          )}
        </Button>
      </Box>
    ));
  }

  function renderTitle() {
    return (
      title && (
        <Box sx={styles.productionTitleContainer}>
          <Typography color={COLOR.text.title_primary} style={{ ...commonStyle.headline20 }}>
            {title}
          </Typography>
          {subBtn && (
            <Button
              style={{ ...styles.seeMoreBtn, color: COLOR.text.color1 }}
              onClick={onClickSubBtn ? () => onClickSubBtn() : () => null}
            >
              {subBtn}
              <Stack style={styles.seeMoreIcon}>
                <ArrowRightIcon fillColor="gray" />
              </Stack>
            </Button>
          )}
          <BtnAction
            text={`Tất cả `}
            icon={<ArrowRightIcon fillColor={"#878787"} />}
            eventAction={onNavigateToProduction}
          />
        </Box>
      )
    );
  }

  function renderSlider() {
    return item.style?.category === "slice" ? (
      <Slider {...settings}>{renderContent()}</Slider>
    ) : (
      <Stack sx={{ flexDirection: "row", flexWrap: "wrap", alignItems: "start" }}>
        {renderContent()}
      </Stack>
    );
  }

  return (
    <Box
      style={{
        ...styles.container,
        background: `unset`,
        marginTop: "-10px",
        padding: "10px 8px 0px",
      }}
    >
      {/* {renderTitle()} */}
      {renderSlider()}
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    paddingBlock: "5px",
    position: "relative",
    marginTop: 5,
  },
  productionTitleContainer: {
    display: "flex",
    paddingBottom: 1,
    paddingRight: "16px",
    marginTop: 0,
    justifyContent: "space-between",
    alignItems: "center",
    marginLeft: 2,
  },
  homeTabContainerTop: {
    display: "flex",
    paddingTop: 3,
    alignItems: "flex-start",
  },
  seeMoreBtn: {
    display: "flex",
    alignItems: "center",
    fontSize: 15,
    fontWeight: 500,
    color: COLORS.textColor.primary,
  },
  seeMoreIcon: {
    marginLeft: 4,
    width: 12,
    height: 12,
  },
};
