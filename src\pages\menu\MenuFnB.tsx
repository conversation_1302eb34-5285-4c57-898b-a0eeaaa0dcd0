import ProductOneColumn from "@/components/products/ProductOneColumn";
import ProductTwoColumn from "@/components/products/ProductTwoColumn";
import { Icon } from "@/constants/Assets";
import { useConfigApp } from "@/hooks/useConfigApp";
import type { IProductCategory } from "@/types/product";
import { ShopCategory } from "@/types/shop";
import ClearIcon from "@mui/icons-material/Clear";
import {
  Box,
  FormControl,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import Tooltip from "@mui/material/Tooltip";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { useDebouncedCallback } from "use-debounce";
import SearchIcon from "../../components/icon/SearchIcon";
import FrameContainerFull from "../../components/layout/ContainerFluid";
import {
  clearSearchCondition,
  getProductListByCategory,
  resetSortBy,
  setSearchCondition,
  setSortBy,
} from "../../redux/slices/product/productListSlice";
import type { AppDispatch, RootState } from "../../redux/store";

export default function MenuV2() {
  const dispatch = useDispatch<AppDispatch>();
  const { color } = useConfigApp();
  const appConfig = useConfigApp();
  const categoryRefs = useRef({});
  const { container } = useConfigApp();
  const [showClearIcon, setShowClearIcon] = useState("none");
  const [searchKey, setSearchKey] = useState("");
  const { searchCondition, sortBy } = useSelector((state: RootState) => state.productList);
  const [sortByPrice, setSortByPrice] = useState<"default" | "asc" | "desc">("default");
  const { shopId, shopInfo } = useSelector((state: RootState) => state.appInfo);

  const [subCategories, setSubCategories] = useState<IProductCategory[]>([]);
  const [selectedParentCategory, setSelectedParentCategory] = useState<string | null>(null);
  const { productCategoryList } = useSelector((state: RootState) => state.product);

  const [selectedChildCategory, setSelectedChildCategory] = useState<string | null>(null);
  const parentCategories = productCategoryList.filter((category) => category.parentId === null);

  const containerStyleItem = container as any;
  const containerBgColor = containerStyleItem?.backgroundColor || "";
  const containerBgImage = containerStyleItem?.backgroundImage || "";

  const containerStyle = {
    paddingInline: 0,
    backgroundColor: containerBgImage ? undefined : containerBgColor,
    backgroundImage: containerBgImage ? `url(${containerBgImage})` : undefined,
    backgroundSize: containerBgImage ? "cover" : undefined,
    backgroundRepeat: containerBgImage ? "no-repeat" : undefined,
    backgroundPosition: containerBgImage ? "center" : undefined,
  };

  const location = useLocation();

  // Các hằng số cho infinite scroll
  const TABS_PER_LOAD = 5;
  const LOAD_DELAY = 300;

  // State quản lý infinite scroll cho danh mục cha
  const [displayedParentCategories, setDisplayedParentCategories] = useState<IProductCategory[]>(
    []
  );
  const [isLoadingMoreParents, setIsLoadingMoreParents] = useState(false);
  const parentCategoriesRef = useRef<HTMLDivElement>(null);

  // State quản lý infinite scroll cho danh mục con
  const [displayedChildCategories, setDisplayedChildCategories] = useState<IProductCategory[]>([]);
  const [isLoadingMoreChildren, setIsLoadingMoreChildren] = useState(false);
  const childCategoriesRef = useRef<HTMLDivElement>(null);

  // Khi danh sách danh mục cha thay đổi, load ban đầu 5 tab
  useEffect(() => {
    const allParentCategories = productCategoryList.filter(
      (category) => category.parentId === null
    );
    setDisplayedParentCategories(allParentCategories.slice(0, TABS_PER_LOAD));
  }, [productCategoryList]);

  // Khi danh mục con (subCategories) thay đổi, load ban đầu 5 tab
  useEffect(() => {
    setDisplayedChildCategories(subCategories.slice(0, TABS_PER_LOAD));
  }, [subCategories]);

  useEffect(() => {
    if (shopId) {
      dispatch(getProductListByCategory({ ...searchCondition, shopId }));
      if (searchCondition.categoryId && !searchCondition.parentId) {
        const subCategories = productCategoryList.filter(
          (category) => category.parentId === (searchCondition.categoryId || undefined)
        );
        setSubCategories(subCategories);
        setSelectedParentCategory(searchCondition.categoryId);
        setSelectedChildCategory(null); // set lại danh mục con
      }
    }
  }, [searchCondition, shopId]);

  useEffect(() => {
    return () => {
      getProductList(null);
      dispatch(resetSortBy());
    };
  }, []);

  useEffect(() => {
    if (shopId && location.state?.categoryId) {
      const categoryId = location.state.categoryId;
      setSelectedParentCategory(categoryId);
      setSelectedChildCategory(null);
      const subCats = productCategoryList.filter((cat) => cat.parentId === categoryId);
      setSubCategories(subCats);
      dispatch(
        setSearchCondition({
          ...searchCondition,
          categoryId,
          parentId: undefined,
        })
      );
      dispatch(
        getProductListByCategory({
          ...searchCondition,
          categoryId,
          parentId: undefined,
          shopId,
        })
      );
      // Scroll to center the selected parent category tab
      setTimeout(() => {
        if (categoryRefs.current && categoryRefs.current[categoryId]) {
          categoryRefs.current[categoryId].scrollIntoView({
            behavior: "smooth",
            inline: "center",
            block: "nearest",
          });
        }
      }, 300);
      window.history.replaceState({}, document.title);
    }
  }, [shopId, productCategoryList]);

  const debounced = useDebouncedCallback((value) => {
    dispatch(
      setSearchCondition({
        ...searchCondition,
        search: value,
      })
    );
  }, 1000);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setSearchKey(event.target.value);
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    debounced(event.target.value);
  };
  const handleAllChildCategories = () => {
    setSelectedChildCategory(null);
    // const updatedSearchCondition = {
    //   ...searchCondition,
    //   categoryId: selectedParentCategory || null,
    //   parentId: selectedParentCategory || undefined,
    // };
    // dispatch(setSearchCondition(updatedSearchCondition));
    // dispatch(getProductListByCategory(updatedSearchCondition))
  };
  const handleClick = (): void => {
    setSearchKey("");
    debounced("");
    setShowClearIcon("none");
  };
  const handleParentCategory = (parentId: string | null) => {
    if (searchCondition.parentId !== parentId) {
      const subCats = productCategoryList.filter((category) => category.parentId === parentId);
      setSubCategories(subCats);
      setSelectedParentCategory(parentId);

      dispatch(
        setSearchCondition({
          ...searchCondition,
          categoryId: null,
          parentId: parentId || undefined,
        })
      );

      dispatch(
        getProductListByCategory({
          ...searchCondition,
          categoryId: null,
          parentId: parentId || undefined,
        })
      );
    }
  };
  const handleChildCategory = (categoryId: string) => {
    setSelectedChildCategory(categoryId);
    // const updatedSearchCondition = {
    //   ...searchCondition,
    //   categoryId: categoryId,
    //   parentId: selectedParentCategory || undefined,
    // };
    // dispatch(setSearchCondition(updatedSearchCondition));
    // dispatch(getProductListByCategory(updatedSearchCondition));
  };
  const getProductList = (cateId: string | null, isParent = false) => {
    if (cateId === null) {
      if (selectedParentCategory) {
        const subCats = productCategoryList.filter(
          (category) => category.parentId === selectedParentCategory
        );
        setSubCategories(subCats);
      } else {
        setSubCategories([]);
      }

      if (
        searchCondition.categoryId !== null ||
        searchCondition.parentId !== selectedParentCategory
      ) {
        dispatch(
          setSearchCondition({
            ...searchCondition,
            categoryId: null,
            parentId: selectedParentCategory || undefined,
          })
        );
      }
    } else if (isParent) {
      const subCats = productCategoryList.filter(
        (category) => category.parentId === (cateId || undefined)
      );
      setSubCategories(subCats);
      setSelectedParentCategory(cateId);
      setSelectedChildCategory(null);

      if (searchCondition.categoryId !== cateId || searchCondition.parentId !== undefined) {
        dispatch(
          setSearchCondition({
            ...searchCondition,
            categoryId: cateId,
            parentId: undefined,
          })
        );
      }
    } else {
      const parentCategory = productCategoryList.find(
        (category) => category.categoryId === cateId
      )?.parentId;
      setSelectedParentCategory(parentCategory || null);

      if (searchCondition.categoryId !== cateId || searchCondition.parentId !== parentCategory) {
        dispatch(
          setSearchCondition({
            ...searchCondition,
            categoryId: cateId,
            parentId: parentCategory || "root",
          })
        );
      }
    }
  };
  const handleSortByPrice = () => {
    let newSortOrder: "default" | "asc" | "desc";

    switch (sortBy?.order) {
      case "default":
        newSortOrder = "asc";
        break;
      case "asc":
        newSortOrder = "desc";
        break;
      case "desc":
      default:
        newSortOrder = "default";
        break;
    }
    // dispatch(setSortBy({ ...sortBy, order: newSortOrder }));
  };

  // Handler cho cuộn danh mục cha (infinite scroll, 5 tab mỗi lần)
  const handleParentScroll = useCallback(() => {
    if (!parentCategoriesRef.current || isLoadingMoreParents) return;

    const container = parentCategoriesRef.current;
    const isAtEnd = container.scrollLeft + container.clientWidth >= container.scrollWidth - 20;

    if (isAtEnd) {
      setIsLoadingMoreParents(true);
      setTimeout(() => {
        const allParentCategories = productCategoryList.filter(
          (category) => category.parentId === null
        );
        const currentCount = displayedParentCategories.length;
        const nextItems = allParentCategories.slice(currentCount, currentCount + TABS_PER_LOAD);
        if (nextItems.length > 0) {
          setDisplayedParentCategories((prev) => [...prev, ...nextItems]);
        }
        setIsLoadingMoreParents(false);
      }, LOAD_DELAY);
    }
  }, [isLoadingMoreParents, displayedParentCategories, productCategoryList]);

  // Handler cho cuộn danh mục con (infinite scroll, 5 tab mỗi lần)
  const handleChildScroll = useCallback(() => {
    if (!childCategoriesRef.current || isLoadingMoreChildren) return;

    const container = childCategoriesRef.current;
    const isAtEnd = container.scrollLeft + container.clientWidth >= container.scrollWidth - 20;

    if (isAtEnd) {
      setIsLoadingMoreChildren(true);
      setTimeout(() => {
        const currentCount = displayedChildCategories.length;
        const nextItems = subCategories.slice(currentCount, currentCount + TABS_PER_LOAD);
        if (nextItems.length > 0) {
          setDisplayedChildCategories((prev) => [...prev, ...nextItems]);
        }
        setIsLoadingMoreChildren(false);
      }, LOAD_DELAY);
    }
  }, [isLoadingMoreChildren, displayedChildCategories, subCategories]);

  // Gắn sự kiện scroll cho danh mục cha
  useEffect(() => {
    const container = parentCategoriesRef.current;
    if (container) {
      container.addEventListener("scroll", handleParentScroll);
      return () => container.removeEventListener("scroll", handleParentScroll);
    }
  }, [handleParentScroll]);

  // Gắn sự kiện scroll cho danh mục con
  useEffect(() => {
    const container = childCategoriesRef.current;
    if (container) {
      container.addEventListener("scroll", handleChildScroll);
      return () => container.removeEventListener("scroll", handleChildScroll);
    }
  }, [handleChildScroll]);

  const listRef = useRef<HTMLUListElement | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!listRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - listRef.current.offsetLeft);
    setScrollLeft(listRef.current.scrollLeft);
  };

  const handleMouseLeave = () => setIsDragging(false);
  const handleMouseUp = () => setIsDragging(false);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !listRef.current) return;
    e.preventDefault();
    const x = e.pageX - listRef.current.offsetLeft;
    const walk = (x - startX) * 1.5; // tốc độ kéo
    listRef.current.scrollLeft = scrollLeft - walk;
  };

  const menuTitle =
    Array.isArray(container?.navbar) && container.navbar[2]?.text
      ? container.navbar[2].text
      : "Sản phẩm";

  return (
    <FrameContainerFull title={menuTitle} overrideStyle={{ height: "100vh", ...containerStyle }}>
      <Box
        height={
          "calc(100vh - 120px - var(--zaui-safe-area-inset-bottom, 0px) - var(--zaui-safe-area-inset-top, 0px))"
        }
      >
        {(() => {
          switch (shopInfo.businessType) {
            case ShopCategory.FB:
              return (
                <>
                  {/* Thanh tìm kiếm chỉ xuất hiện nếu là shop FB */}
                  <Box
                    display="flex"
                    alignItems="center"
                    gap={2}
                    bgcolor="#ffffff00"
                    padding={"10px"}
                    height={"70px"}
                    width="100%"
                  >
                    <Box
                      display="flex"
                      justifyContent="end"
                      alignItems="center"
                      gap={1}
                      paddingBlock={"10px"}
                      marginTop={"0px"}
                      style={{ cursor: "pointer" }}
                      height={"50px"}
                      onClick={handleSortByPrice}
                      sx={{
                        border: `2px solid ${color.primary}`,
                        borderRadius: "10px",
                        padding: "10px",
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        fill="none"
                        stroke={`${color.primary}`}
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="feather feather-filter"
                      >
                        <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3" />
                      </svg>
                      {/* <Typography style={{ color: "#7A7A7A", fontSize: 14 }}>Giá</Typography> */}
                      {sortBy?.field === "price" && sortBy.order === "asc" && (
                        <img src={Icon.arrow_drop_up} alt="Sort Ascending" />
                      )}
                      {sortBy?.field === "price" && sortBy.order === "desc" && (
                        <img src={Icon.arrow_drop_down} alt="Sort Descending" />
                      )}
                      {sortBy?.field === "price" && sortBy.order === "default" && (
                        <img src={Icon.sort_default} alt="Sort Default" />
                      )}
                    </Box>
                    <FormControl
                      fullWidth={true}
                      style={{
                        boxShadow: "0px 0px 5px 1px #00000033",
                        borderRadius: 10,
                        // overflow: "hidden",
                      }}
                    >
                      <Stack sx={styles.iconSearchStyle}>
                        <SearchIcon fillColor={color.primary} />
                      </Stack>
                      <TextField
                        style={{ ...styles.searchStyle, borderRadius: 5 }}
                        className="input-search"
                        placeholder="Tìm kiếm sản phẩm"
                        size="small"
                        variant="outlined"
                        onChange={handleChange}
                        value={searchKey}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment
                              position="end"
                              style={{ display: showClearIcon }}
                              onClick={handleClick}
                            >
                              <ClearIcon />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </FormControl>
                  </Box>

                  {/* Phần giao diện chính của shop FB */}
                  <Box
                    display="flex"
                    flexDirection="column"
                    width="100%"
                    height={"103%"}
                    marginBottom={"35px"}
                    bgcolor={"#ffffff00"}
                    overflow={"hidden"}
                    mt={1}
                  >
                    {/* Phần danh mục */}
                    <Box
                      width="100%"
                      minWidth="100px"
                      bgcolor={"#ffffff00"}
                      height={"auto"}
                      style={{
                        paddingLeft: "16px",
                        paddingRight: "16px",
                      }}
                    >
                      <List
                        ref={listRef}
                        onMouseDown={handleMouseDown}
                        onMouseLeave={handleMouseLeave}
                        onMouseUp={handleMouseUp}
                        onMouseMove={handleMouseMove}
                        style={{
                          padding: 0,
                          background: "#ffffff00",
                          display: "flex",
                          gap: "10px",
                          marginBottom: "25px",

                          overflowX: "auto",
                          cursor: isDragging ? "grabbing" : "grab",
                          userSelect: "none",
                          scrollbarWidth: "none", // Firefox
                          msOverflowStyle: "none", // IE
                        }}
                        sx={{
                          "&::-webkit-scrollbar": {
                            display: "none", // Chrome, Safari
                          },
                        }}
                      >
                        <ListItem
                          button
                          onClick={() => getProductList(null)}
                          style={{
                            paddingInline: 16,
                            paddingBlock: 12,
                            width: "max-content",
                            background: !searchCondition.categoryId ? color.primary : "#ffffff00",
                            borderRadius: !searchCondition.categoryId ? "30px" : "30px",
                            border: !searchCondition.categoryId
                              ? `2px solid ${color.primary}`
                              : `2px solid ${color.primary}`,
                            height: 40,
                          }}
                        >
                          <Typography
                            color={!searchCondition.categoryId ? "#fff" : "#000"}
                            fontWeight={!searchCondition.categoryId ? 700 : 400}
                            zIndex={2}
                            width={"max-content"}
                          >
                            Tất cả
                          </Typography>
                        </ListItem>

                        {productCategoryList.map((category, index) => {
                          const isFirstCategory = index === 0 && category;
                          const isActive = searchCondition.categoryId === category.categoryId;
                          const prevItemIsActive =
                            index > 0 &&
                            searchCondition.categoryId ===
                              productCategoryList[index - 1].categoryId;
                          const nextItemIsActive =
                            index < productCategoryList.length - 1 &&
                            searchCondition.categoryId ===
                              productCategoryList[index + 1].categoryId;

                          return (
                            <ListItem
                              key={category.categoryId}
                              button
                              onClick={() => getProductList(category.categoryId)}
                              style={{
                                paddingInline: 16,
                                height: 40,
                                background: isActive ? "#ffffff00" : "#ffffff00",
                                borderRadius: isActive ? "30px" : "30px",
                                border: isActive
                                  ? `2px solid ${color.primary}`
                                  : `2px solid ${color.primary}`,
                              }}
                            >
                              <Typography
                                color={isActive ? "#fff" : "#000"}
                                fontWeight={isActive ? 700 : 400}
                                zIndex={2}
                                width={"max-content"}
                                sx={styles.categoryMenuTitle}
                              >
                                {category.categoryName}
                              </Typography>
                            </ListItem>
                          );
                        })}

                        {/* Dùng item này để bo cong menu cuối */}
                        {/* <ListItem
                    style={{
                      paddingInline: 16,
                      paddingBlock: 12,
                      background: "#F5F5F5",
                      borderTopRightRadius:
                        productCategoryList.length === 0 ||
                        searchCondition.categoryId === productCategoryList.slice(-1)[0].categoryId
                          ? 10
                          : 0,
                    }}
                  /> */}
                      </List>
                    </Box>

                    {/* Phần sản phẩm */}
                    <Box
                      flex={1}
                      padding={"16px"}
                      paddingTop={"0px"}
                      bgcolor={"#ffffff00"}
                      marginBottom={"35px"}
                      style={{
                        height: "100%",
                      }}
                    >
                      {/* phần giá (shop FB) */}

                      {/* Updated product list container */}
                      <Box
                        height={
                          "calc(100vh - 120px - 70px - 40px - var(--zaui-safe-area-inset-bottom, 0px) - var(--zaui-safe-area-inset-top, 0px))"
                        }
                        width={"100%"}
                        overflow={"auto"} // Keep only one scrollbar
                        sx={{
                          marginBottom: "105px",
                          "&::-webkit-scrollbar": {
                            width: "2px",
                          },
                          "&::-webkit-scrollbar-track": {
                            background: "#f1f1f1",
                          },
                          "&::-webkit-scrollbar-thumb": {
                            background: "#888",
                            borderRadius: "10px",
                          },
                          "&::-webkit-scrollbar-thumb:hover": {
                            background: "#555",
                          },
                        }}
                      >
                        <ProductOneColumn />
                      </Box>
                    </Box>
                  </Box>
                </>
              );

            default:
              return (
                <>
                  <Box
                    width="100%"
                    bgcolor={appConfig.container.backgroundColor}
                    style={{ paddingLeft: "10px" }}
                  >
                    <List
                      component="div"
                      ref={parentCategoriesRef}
                      style={{
                        padding: 0,
                        display: "flex",
                        flexDirection: "row",
                        overflowX: "scroll",
                        overflowY: "hidden",
                        whiteSpace: "nowrap",
                        backgroundColor: appConfig.container.backgroundColor,
                      }}
                      sx={{
                        "&::-webkit-scrollbar": {
                          display: "none",
                        },
                        msOverflowStyle: "none",
                        scrollbarWidth: "none",
                      }}
                    >
                      {/* Tab "Tất cả" */}
                      <ListItem
                        button
                        ref={(el) => (categoryRefs.current["all"] = el)}
                        onClick={() => {
                          setSelectedParentCategory(null);
                          setSubCategories(
                            productCategoryList.filter((category) => category.parentId !== null)
                          );
                          dispatch(
                            setSearchCondition({
                              ...searchCondition,
                              categoryId: null,
                              parentId: undefined,
                            })
                          );

                          categoryRefs.current["all"]?.scrollIntoView({
                            behavior: "smooth",
                            inline: "center",
                            block: "nearest",
                          });
                        }}
                        style={{
                          marginLeft: "16px",
                          padding: "6px 8px",
                          margin: "0 4px",
                          width: "120px",
                          flexShrink: 0,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          textAlign: "center",
                          height: "40px",
                          background: selectedParentCategory === null ? "#ffffff00" : "#ffffff00",
                          borderBottom:
                            selectedParentCategory === null
                              ? `2px solid ${color.primary}`
                              : "2px solid transparent",
                        }}
                      >
                        <Typography
                          color={selectedParentCategory === null ? color.primary : "#626262"}
                          fontWeight={selectedParentCategory === null ? 700 : 400}
                        >
                          Tất cả
                        </Typography>
                      </ListItem>

                      {displayedParentCategories.map((category) => (
                        <ListItem
                          key={category.categoryId}
                          button
                          ref={(el) => (categoryRefs.current[category.categoryId] = el)}
                          onClick={() => {
                            getProductList(category.categoryId, true);

                            categoryRefs.current[category.categoryId]?.scrollIntoView({
                              behavior: "smooth",
                              inline: "center",
                              block: "nearest",
                            });
                          }}
                          style={{
                            padding: "6px 8px",
                            margin: "0 4px",
                            width: "120px",
                            flexShrink: 0,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            textAlign: "center",
                            height: "40px",
                            background:
                              selectedParentCategory === category.categoryId
                                ? "#ffffff00"
                                : "#ffffff00",
                            borderBottom:
                              selectedParentCategory === category.categoryId
                                ? `2px solid ${color.primary}`
                                : "2px solid transparent",
                          }}
                        >
                          <Typography
                            color={
                              selectedParentCategory === category.categoryId
                                ? color.primary
                                : "#626262"
                            }
                            fontWeight={selectedParentCategory === category.categoryId ? 700 : 400}
                            sx={{
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              whiteSpace: "nowrap",
                              maxWidth: "100%",
                            }}
                          >
                            {category.categoryName}
                          </Typography>
                        </ListItem>
                      ))}

                      {isLoadingMoreParents && (
                        <ListItem
                          style={{
                            padding: "6px 8px",
                            margin: "0 4px",
                            width: "120px",
                            flexShrink: 0,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            textAlign: "center",
                            height: "40px",
                          }}
                        >
                          {/* Loading spinner nếu cần */}
                        </ListItem>
                      )}
                    </List>
                  </Box>
                  <Box
                    display="flex"
                    alignItems="center"
                    gap={2}
                    bgcolor={appConfig.container.backgroundColor}
                    padding={"16px"}
                    height={"70px"}
                    width="100%"
                  >
                    <FormControl
                      fullWidth={true}
                      style={{
                        boxShadow: "0px 0px 5px 1px #00000033",
                        borderRadius: 5,
                      }}
                    >
                      <Stack sx={styles.iconSearchStyle}>
                        <SearchIcon fillColor={color.primary} />
                      </Stack>
                      <TextField
                        style={{ ...styles.searchStyle, borderRadius: 5 }}
                        className="input-search"
                        placeholder="Tìm kiếm sản phẩm"
                        size="small"
                        variant="outlined"
                        onChange={handleChange}
                        value={searchKey}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment
                              position="end"
                              style={{ display: showClearIcon }}
                              onClick={handleClick}
                            >
                              <ClearIcon />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </FormControl>
                  </Box>
                  {/* Hiển thị danh mục con nếu danh mục cha không phải "Tất cả" */}
                  {selectedParentCategory !== null && (
                    <Box
                      width="100%"
                      bgcolor={appConfig.container.backgroundColor}
                      style={{ paddingLeft: "16px" }}
                    >
                      <List
                        component="div"
                        ref={childCategoriesRef}
                        style={{
                          padding: 0,
                          display: "flex",
                          flexDirection: "row",
                          overflowX: "scroll",
                          overflowY: "hidden",
                          whiteSpace: "nowrap",
                          backgroundColor: `${appConfig.container.backgroundColor}`,
                        }}
                        sx={{
                          "&::-webkit-scrollbar": {
                            display: "none",
                          },
                          msOverflowStyle: "none",
                          scrollbarWidth: "none",
                        }}
                      >
                        {/* Tab "Tất cả" trong danh mục con */}
                        <ListItem
                          button
                          onClick={handleAllChildCategories}
                          style={{
                            marginLeft: "16px",
                            padding: "6px 8px",
                            margin: "0 4px",
                            marginBottom: "10px",
                            width: "120px",
                            flexShrink: 0,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            textAlign: "center",
                            height: "40px",
                            background:
                              selectedChildCategory === null ? color.primary : "#ffffff00",
                          }}
                        >
                          <Typography
                            color={selectedChildCategory === null ? "white" : "#626262"}
                            fontWeight={selectedChildCategory === null ? 700 : 400}
                          >
                            Tất cả
                          </Typography>
                        </ListItem>

                        {displayedChildCategories.map((category) => (
                          <ListItem
                            key={category.categoryId}
                            button
                            onClick={() => handleChildCategory(category.categoryId)}
                            style={{
                              padding: "6px 8px",
                              margin: "0 4px",
                              width: "120px",
                              flexShrink: 0,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              textAlign: "center",
                              height: "40px",
                              background:
                                selectedChildCategory === category.categoryId
                                  ? color.primary
                                  : "#ffffff00",
                            }}
                          >
                            <Typography
                              color={
                                selectedChildCategory === category.categoryId ? "white" : "#626262"
                              }
                              fontWeight={selectedChildCategory === category.categoryId ? 700 : 400}
                              sx={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                maxWidth: "100%",
                              }}
                            >
                              {category.categoryName}
                            </Typography>
                          </ListItem>
                        ))}

                        {isLoadingMoreChildren && (
                          <ListItem
                            style={{
                              padding: "6px 8px",
                              margin: "0 4px",
                              width: "120px",
                              flexShrink: 0,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              textAlign: "center",
                              height: "40px",
                            }}
                          ></ListItem>
                        )}
                      </List>
                    </Box>
                  )}
                  {/* Phần danh sách sản phẩm cho shop khác FB */}
                  <Box
                    display="flex"
                    flexDirection="column"
                    width="100%"
                    height={
                      selectedParentCategory !== null
                        ? "calc(100vh - 120px - 70px - 40px - var(--zaui-safe-area-inset-bottom, 0px) - var(--zaui-safe-area-inset-top, 0px))"
                        : "calc(100vh - 120px - 70px - var(--zaui-safe-area-inset-bottom, 0px) - var(--zaui-safe-area-inset-top, 0px))"
                    }
                    bgcolor={appConfig.container.backgroundColor}
                    style={{
                      overflow: "auto",
                      paddingBottom: "40px",
                    }}
                  >
                    <ProductTwoColumn />
                  </Box>
                </>
              );
          }
        })()}
      </Box>
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  searchStyle: {
    width: "100%",
    background: "#ffffff00",
  },
  iconSearchStyle: {
    top: 15,
    left: 15,
    position: "absolute",
    width: 20,
    zIndex: 1,
    height: 40,
  },
  categoryMenuTitle: {
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 2,
  },
};
