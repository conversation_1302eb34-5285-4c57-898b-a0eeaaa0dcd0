import React from "react";
import { requestUpdate<PERSON>alo } from "zmp-sdk/apis";
import PopupUpdateZalo from "./UI/PopupUpdateZalo";

const showUpdateZaloPopupRef: { current: (() => void) | null } = { current: null };

export function useZaloUpdatePopupProvider() {
  const [open, setOpen] = React.useState(false);
  React.useEffect(() => {
    showUpdateZaloPopupRef.current = () => setOpen(true);
    return () => {
      showUpdateZaloPopupRef.current = null;
    };
  }, []);
  const handleOk = React.useCallback(() => {
    setOpen(false);
    requestUpdateZalo();
  }, []);
  const handleClose = React.useCallback(() => setOpen(false), []);
  return { open, handleClose, handleOk };
}

const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { open, handleClose, handleOk } = useZaloUpdatePopupProvider();
  return (
    <>
      {children}
      <PopupUpdateZalo open={open} onClose={handleClose} onOk={handleOk} />
    </>
  );
};

export const showUpdateZaloPopup = () => {
  if (showUpdateZaloPopupRef.current) showUpdateZaloPopupRef.current();
};

export default AppProvider;