import { formatPrice } from "./formatPrice";

export type ExtendedTreeItemProps = {
  id: string;
  label: string;
  avatar?: string;
  moreInfo?: string;
  searchValue?: string;
  children: Array<ExtendedTreeItemProps>;
};

const convertDataToTree = (fParent: number, members: Array<any>) => {
  const data: Array<ExtendedTreeItemProps> = [];
  const membersData = members.filter((e) => e.fParent == fParent);
  if (membersData.length > 0) {
    for (let member of membersData) {
      const children = convertDataToTree(member.id, members);
      data.push({
        id: `${member.id}`,
        label: `${member.name ?? "N/A"}`,
        avatar: member.avatarUrl ?? "/images/avatar.png",
        moreInfo: `${member.phone} - <PERSON><PERSON>h số: ${formatPrice(
          parseInt(member.mySale ?? 0)
        )}`,
        searchValue: `F${member.fLevel}-${member.name ?? "N/A"}-${
          member.phone
        }`,
        children,
      });
    }
  }

  return data;
};

export { convertDataToTree };
