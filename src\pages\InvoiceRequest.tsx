import React from "react";
import * as yup from "yup";
import {
  Box,
  Stack,
  Typography,
  Input,
  Button,
  IconButton,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divide<PERSON>,
} from "@mui/material";
import ReceiptLongIcon from "@mui/icons-material/ReceiptLong";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { useNavigate } from "../utils/component-util";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  getBusinessInfoByTaxCode,
  getBusinessInfoBySlug,
  createTaxInvoice,
  getTaxInvoiceList,
  updateTaxInvoice,
} from "@/redux/slices/taxInvoice/taxInvoiceSlice";
import { useCart } from "@/hooks/useCart";
import { useLocation } from "react-router-dom";
import PermContactCalendarIcon from "@mui/icons-material/PermContactCalendar";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { useConfigApp } from "@/hooks/useConfigApp";
import ContainerFluid from "@/components/layout/ContainerFluid";

// Email validation schema
const emailSchema = yup.object({
  email: yup.string().required("Vui lòng nhập email").email("Email không hợp lệ"),
});

const validateEmail = (email: string) => {
  try {
    emailSchema.validateSync({ email });
    return "";
  } catch (err: any) {
    return err.message || "Email không hợp lệ";
  }
};

export default function InvoiceRequest() {
  const { color } = useConfigApp();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { user } = useSelector((state: RootState) => state.auth);
  const { cartPayment, setCartAndSave } = useCart();
  const location = useLocation();

  const [businessForm, setBusinessForm] = React.useState({
    taxCode: "",
    companyName: "",
    address: "",
    email: "",
    showBuyerName: true,
    saveToContacts: true,
  });
  const [personalForm, setPersonalForm] = React.useState({
    taxCode: "",
    personalName: "",
    companyName: "",
    address: "",
    email: "",
    showBuyerName: true,
    saveToContacts: true,
  });

  const [tab, setTab] = React.useState(0);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState<string | null>(null);

  const [contactList, setContactList] = React.useState<any[]>([]);
  const [openContact, setOpenContact] = React.useState(false);

  const onSuccess = location.state?.onSuccess;

  React.useEffect(() => {
    const taxInvoice = cartPayment?.taxInvoice;
    if (taxInvoice) {
      const type = taxInvoice.taxPayerType || "Business";
      if (type === "Business") {
        setTab(0);
        setBusinessForm((f) => ({
          ...f,
          taxCode: taxInvoice.taxCode || "",
          companyName: taxInvoice.name || "",
          address: taxInvoice.address || "",
          email: taxInvoice.email || "",
        }));
      } else if (type === "Individual") {
        setTab(1);
        setPersonalForm((f) => ({
          ...f,
          taxCode: taxInvoice.taxCode || "",
          personalName: taxInvoice.name || "",
          address: taxInvoice.address || "",
          email: taxInvoice.email || "",
        }));
      }
    }
  }, [cartPayment?.taxInvoice]);

  const handleTabChange = (event, newValue) => {
    setTab(newValue);
    setError(null);
    setSuccess(null);
  };

  const form = tab === 0 ? businessForm : personalForm;
  const setForm = tab === 0 ? setBusinessForm : setPersonalForm;
  const personalName = personalForm.personalName;

  const handleLookup = async () => {
    setError(null);
    setSuccess(null);
    setLoading(true);
    try {
      let res;
      if (tab === 0) {
        // Business
        res = await dispatch(getBusinessInfoByTaxCode(form.taxCode)).unwrap();
        const data = res?.data || res?.result;
        if (data) {
          setBusinessForm((f) => ({
            ...f,
            companyName: data.name || f.companyName,
            address: data.address || f.address,
            email: data.email || f.email,
          }));
          setSuccess("Tra cứu thành công!");
        } else {
          setError("Không tìm thấy thông tin");
        }
      } else {
        // Personal
        res = await dispatch(
          getBusinessInfoBySlug({ taxCode: form.taxCode, name: personalName })
        ).unwrap();
        const data = res?.data || res?.result;
        if (data) {
          setPersonalForm((f) => ({
            ...f,
            companyName: data.name || f.companyName,
            address: data.address || f.address,
            email: data.email || f.email,
          }));
          setSuccess("Tra cứu thành công!");
        } else {
          setError("Không tìm thấy thông tin");
        }
      }
    } catch (err: any) {
      setError(
        err?.message ||
          (tab === 0
            ? "Không tìm thấy thông tin thuế doanh nghiệp"
            : "Không tìm thấy thông tin thuế cá nhân")
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    setError(null);
    setSuccess(null);
    setLoading(true);
    let data: any = {};
    try {
      if (!shopId) throw new Error("Thiếu thông tin shop");
      let updatedCart;
      if (tab === 0) {
        data = {
          shopId,
          userId: user?.userId,
          taxCode: businessForm.taxCode,
          name: businessForm.companyName,
          address: businessForm.address,
          email: businessForm.email,
        };
        const existedConfig = contactList.find(
          (item) => item.taxCode === businessForm.taxCode && item.userId === user?.userId
        );
        if (businessForm.saveToContacts) {
          if (existedConfig) {
            await dispatch(
              updateTaxInvoice({
                ...data,
                taxInvoiceId: existedConfig.taxInvoiceId,
                saveToContacts: true,
                taxPayerType: "Business" as const,
                taxCode: existedConfig.taxCode,
              })
            ).unwrap();
          } else {
            // create
            await dispatch(
              createTaxInvoice({ ...data, saveToContacts: true, taxPayerType: "Business" as const })
            ).unwrap();
          }
        }
        updatedCart = {
          ...cartPayment,
          taxInvoice: {
            shopId,
            userId: user?.userId,
            taxCode: businessForm.taxCode,
            name: businessForm.companyName,
            address: businessForm.address,
            email: businessForm.email,
            taxPayerType: "Business" as const,
          },
        };
        await setCartAndSave(updatedCart);
        setSuccess(
          businessForm.saveToContacts
            ? existedConfig
              ? "Đã cập nhật thông tin hoá đơn trong danh bạ và đơn hàng thành công!"
              : "Lưu thông tin hoá đơn vào danh bạ và cập nhật đơn hàng thành công!"
            : "Đã thêm thông tin hoá đơn vào đơn hàng"
        );
      } else {
        data = {
          shopId,
          userId: user?.userId,
          taxCode: personalForm.taxCode,
          name: personalForm.personalName,
          address: personalForm.address,
          email: personalForm.email,
        };
        const existedConfig = contactList.find(
          (item) => item.taxCode === personalForm.taxCode && item.userId === user?.userId
        );
        if (personalForm.saveToContacts) {
          if (existedConfig) {
            // update: giữ nguyên taxCode cũ, chỉ update các field khác
            await dispatch(
              updateTaxInvoice({
                ...data,
                taxInvoiceId: existedConfig.taxInvoiceId,
                saveToContacts: true,
                taxPayerType: "Individual" as const,
                taxCode: existedConfig.taxCode, // giữ nguyên taxCode cũ
              })
            ).unwrap();
          } else {
            await dispatch(
              createTaxInvoice({
                ...data,
                saveToContacts: true,
                taxPayerType: "Individual" as const,
              })
            ).unwrap();
          }
        }
        updatedCart = {
          ...cartPayment,
          taxInvoice: {
            shopId,
            userId: user?.userId,
            taxCode: personalForm.taxCode,
            name: personalForm.personalName,
            address: personalForm.address,
            email: personalForm.email,
            taxPayerType: "Individual" as const,
          },
        };
        await setCartAndSave(updatedCart);
        setSuccess(
          personalForm.saveToContacts
            ? existedConfig
              ? "Đã cập nhật thông tin hoá đơn trong danh bạ và đơn hàng thành công!"
              : "Lưu thông tin hoá đơn vào danh bạ và cập nhật đơn hàng thành công!"
            : "Đã thêm thông tin hoá đơn vào đơn hàng"
        );
      }
      setTimeout(() => {
        if (typeof onSuccess === "function") {
          onSuccess();
        } else {
          navigate("/cartPayment");
        }
      }, 800);
    } catch (err: any) {
      setError(err?.message || "Lưu thông tin thất bại");
    } finally {
      setLoading(false);
    }
  };

  const handleOpenContact = async () => {
    setLoading(true);
    setError(null);
    try {
      if (!shopId || !user?.userId) throw new Error("Thiếu thông tin shop hoặc user");
      const res = await dispatch(getTaxInvoiceList({ shopId, userId: user.userId })).unwrap();
      const data = res?.data || res?.result;
      setContactList(Array.isArray(data) ? data : []);
      setOpenContact(true);
    } catch (err: any) {
      setError(err?.message || "Không lấy được danh bạ hoá đơn");
    } finally {
      setLoading(false);
    }
  };

  const handleSelectContact = (item: any) => {
    if (item.taxPayerType === "Business") {
      setTab(0);
      setBusinessForm((f) => ({
        ...f,
        taxCode: item.taxCode || "",
        companyName: item.name || "",
        address: item.address || "",
        email: item.email || "",
      }));
    } else if (item.taxPayerType === "Individual") {
      setTab(1);
      setPersonalForm((f) => ({
        ...f,
        taxCode: item.taxCode || "",
        personalName: item.name || "",
        address: item.address || "",
        email: item.email || "",
      }));
    }
    setOpenContact(false);
  };

  return (
    <ContainerFluid title="Xuất hoá đơn GTGT">
      <Box sx={{ background: "#fff", borderBottom: "1px solid #eee" }}>
        <Tabs value={tab} onChange={handleTabChange} centered>
          <Tab label="Doanh nghiệp" />
          <Tab label="Cá nhân" />
        </Tabs>
      </Box>
      {(tab === 0 || tab === 1) && (
        <Box sx={{ p: 2, maxWidth: 480, margin: "0 auto" }}>
          <Stack spacing={2}>
            <Stack
              direction="row"
              spacing={1}
              alignItems="center"
              justifyContent="space-between"
              sx={{ cursor: "pointer" }}
              onClick={handleOpenContact}
            >
              <Stack direction="row" spacing={1} alignItems="center">
                <PermContactCalendarIcon sx={{ color: color.primary }} />
                <Typography fontWeight={600} fontSize={16} color="primary">
                  Chọn từ danh bạ
                </Typography>
              </Stack>
              <ChevronRightIcon sx={{ fontSize: 28, color: color.primary }} />
            </Stack>
            <Divider />

            {tab === 1 && (
              <Input
                placeholder="Họ tên *"
                value={personalForm.personalName || ""}
                onChange={(e) => setPersonalForm((f) => ({ ...f, personalName: e.target.value }))}
                sx={{ background: "#fff", borderRadius: 2, px: 2, py: 1 }}
              />
            )}

            <Input
              placeholder="Mã số thuế *"
              value={form.taxCode}
              onChange={(e) => setForm((f) => ({ ...f, taxCode: e.target.value }))}
              sx={{ background: "#fff", borderRadius: 2, px: 2, py: 1 }}
              endAdornment={
                <Button
                  variant="outlined"
                  size="small"
                  sx={{ ml: 1, whiteSpace: "nowrap" }}
                  onClick={handleLookup}
                  disabled={loading || !form.taxCode || (tab === 1 && !personalForm.personalName)}
                >
                  {loading ? "Đang tra cứu..." : "Tra cứu"}
                </Button>
              }
            />
            {error && <Alert severity="error">{error}</Alert>}
            {success && <Alert severity="success">{success}</Alert>}
            <Typography variant="caption">
              {tab === 0 ? (
                <span>
                  Vui lòng nhập <b>Mã số thuế</b> để <b>Tra cứu</b> thông tin
                </span>
              ) : (
                <span>
                  Vui lòng nhập <b>Họ tên</b> và <b>Mã số thuế</b> để <b>Tra cứu</b> thông tin
                </span>
              )}
            </Typography>
            {tab === 0 && (
              <Input
                placeholder="Tên công ty *"
                value={form.companyName}
                onChange={(e) => setForm((f) => ({ ...f, companyName: e.target.value }))}
                sx={{ background: "#fff", borderRadius: 2, px: 2, py: 1 }}
              />
            )}
            {tab === 1 && (
              <Input
                placeholder="Họ tên *"
                value={personalForm.personalName}
                onChange={(e) => setPersonalForm((f) => ({ ...f, personalName: e.target.value }))}
                sx={{ background: "#fff", borderRadius: 2, px: 2, py: 1 }}
              />
            )}
            <Input
              placeholder="Địa chỉ *"
              value={form.address}
              onChange={(e) => setForm((f) => ({ ...f, address: e.target.value }))}
              sx={{ background: "#fff", borderRadius: 2, px: 2, py: 1 }}
            />
            <Input
              placeholder="Email nhận hoá đơn *"
              value={form.email}
              onChange={(e) => setForm((f) => ({ ...f, email: e.target.value }))}
              sx={{ background: "#fff", borderRadius: 2, px: 2, py: 1 }}
            />
            {validateEmail(form.email) && form.email && (
              <Typography color="error" fontSize={13} mt={-1} mb={1}>
                {validateEmail(form.email)}
              </Typography>
            )}
            <Stack direction="row" spacing={2}>
              <label style={{ display: "flex", alignItems: "center", gap: 4 }}>
                <input
                  type="checkbox"
                  checked={form.saveToContacts}
                  onChange={(e) => setForm((f) => ({ ...f, saveToContacts: e.target.checked }))}
                />
                Lưu thông tin vào danh bạ
              </label>
            </Stack>
            <Button
              sx={{
                background: color.primary,
                color: "#fff",
                borderRadius: 2,
                fontWeight: 700,
                py: 1.5,
                mt: 2,
                fontSize: 16,
                "&:hover": {
                  background: color.primary,
                  opacity: 0.9,
                },
              }}
              fullWidth
              onClick={handleSubmit}
              disabled={
                !!(
                  loading ||
                  !form.taxCode ||
                  !form.email ||
                  !form.address ||
                  (tab === 0 ? !form.companyName : !personalForm.personalName) ||
                  (form.email && validateEmail(form.email))
                )
              }
            >
              {loading ? "Đang lưu..." : "Xác nhận"}
            </Button>
          </Stack>
        </Box>
      )}
      {openContact && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            bgcolor: "rgba(0,0,0,0.2)",
            zIndex: 9999,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Box sx={{ bgcolor: "#fff", borderRadius: 2, p: 2, minWidth: 320, maxWidth: 400 }}>
            <Typography fontWeight={700} mb={2}>
              Danh bạ
            </Typography>
            <Stack spacing={1}>
              {contactList.length === 0 && <Typography>Không có dữ liệu</Typography>}
              {contactList.map((item, idx) => (
                <Box
                  key={idx}
                  sx={{
                    p: 1,
                    border: "1px solid #eee",
                    borderRadius: 1,
                    cursor: "pointer",
                    "&:hover": { bgcolor: "#f5f5f5" },
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                  onClick={() => handleSelectContact(item)}
                >
                  <input type="checkbox" style={{ marginRight: 8 }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography fontWeight={700}>
                      {item.taxCode} - {item.name}
                    </Typography>
                    <Typography fontSize={13} color="text.secondary">
                      Email: {item.email}
                    </Typography>
                    <Typography fontSize={13} color="text.secondary">
                      Địa chỉ: {item.address}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Stack>
            <Button fullWidth sx={{ mt: 2 }} onClick={() => setOpenContact(false)}>
              Đóng
            </Button>
          </Box>
        </Box>
      )}
    </ContainerFluid>
  );
}
