import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";
import BannerFnB from "./BannerFnB";
import BannerRetail from "./BannerRetail";

const componentMap = {
  FnB: BannerFnB,
  Retail: BannerRetail,
};

export default function Banner(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FnB");
  const Component = componentMap[businessType] || componentMap.FnB;
  return <Component {...props} />;
}
