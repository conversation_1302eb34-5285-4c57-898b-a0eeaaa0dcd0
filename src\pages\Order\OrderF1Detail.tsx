import { Stack, Typography } from "@mui/material";
import React from "react";
import FrameContainer from "../../components/layout/Container";
import { Icon } from "../../constants/Assets";
import { useLocation } from "react-router-dom";
import { formatPrice } from "../../utils/formatPrice";
import moment from "moment";
import { OrderStatusText } from "../../constants/Const";
import { COLORS, commonStyle } from "../../constants/themes";
import NoDataView from "../../components/UI/NoDataView";

export default function OrderF1Detail() {
  const { state } = useLocation();
  const orderDetail = state.order;

  if (!orderDetail) return <NoDataView content="Không có chi tiết đơn hàng" />;

  return (
    <FrameContainer title="Thông tin đơn hàng">
      <Stack>
        <Stack sx={styles.sectionContainer}>
          <Stack direction="row" sx={styles.headerSection}>
            <img src={Icon.ship} />
            <b>Đ<PERSON><PERSON> chỉ nhận hàng</b>
          </Stack>
          <Stack gap={2} paddingBottom={2}>
            <Stack direction="row" gap={2} alignItems={"center"}>
              <Stack gap={1} sx={{ color: COLORS.neutral4 }}>
                <Typography>{orderDetail?.receiver?.userName}</Typography>
                <Typography>{orderDetail?.receiver?.phone}</Typography>
                <Typography>
                  {orderDetail?.receiver?.addressObj?.wardText} ,{" "}
                  {orderDetail?.receiver?.addressObj?.districtText} ,{" "}
                  {orderDetail?.receiver?.addressObj?.provinceText}
                </Typography>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
        <Stack sx={styles.sectionContainer}>
          <Stack direction="row" sx={styles.headerSection}>
            <img src={Icon.package} />
            <b>Sản phẩm đặt mua</b>
          </Stack>
          <Stack gap={2}>
            {orderDetail?.orderData.items?.map((item) => (
              <Stack
                key={`${item.product.id}`}
                direction="row"
                gap={2}
                alignItems={"center"}
              >
                <img
                  width={60}
                  src={`${import.meta.env.VITE_API_URL}${
                    item?.product?.image?.data?.[0]?.attributes?.url
                  }`}
                />
                <Stack gap={1}>
                  <Typography style={{ color: COLORS.neutral2 }}>
                    {item?.product?.name}
                  </Typography>
                  <Typography
                    style={{
                      ...commonStyle.headline16,
                      color: COLORS.accent1,
                    }}
                  >
                    {formatPrice(item.product.price - item.product.discount)} x
                    {item.quantity}
                  </Typography>
                </Stack>
              </Stack>
            ))}
          </Stack>
        </Stack>
        <Stack sx={styles.sectionContainer}>
          <Stack direction="row" sx={styles.headerSection}>
            <img src={Icon.payment} />
            <b>Chi tiết thanh toán</b>
          </Stack>
          <Stack gap={2}>
            <Stack gap={1} alignItems={"center"}>
              <Stack direction={"row"} sx={styles.contentContainer}>
                <Typography style={{ color: COLORS.neutral4 }}>
                  Tổng tiền hàng
                </Typography>
                <Typography>
                  {orderDetail !== null
                    ? formatPrice(
                        Number(orderDetail.finalPrice) +
                          orderDetail.discountValue
                      )
                    : 0}
                </Typography>
              </Stack>
              <Stack direction={"row"} sx={styles.contentContainer}>
                <Typography style={{ color: COLORS.neutral4 }}>
                  Phí vận chuyển
                </Typography>
                <Typography>
                  {formatPrice(orderDetail?.orderData?.ship || 0)}
                </Typography>
              </Stack>
              <Stack direction={"row"} sx={styles.contentContainer}>
                <Typography style={{ color: COLORS.neutral4 }}>
                  Giảm giá phí vận chuyển
                </Typography>
                <Typography>0đ</Typography>
              </Stack>
              <Stack direction={"row"} sx={styles.contentContainer}>
                <Typography style={{ color: COLORS.neutral4 }}>
                  Voucher giảm giá
                </Typography>
                <Typography>
                  {" "}
                  {formatPrice(orderDetail?.discountValue)}
                </Typography>
              </Stack>
              <Stack direction={"row"} sx={styles.contentContainer}>
                <Typography style={styles.priceText}>Thành tiền</Typography>
                <Typography style={styles.priceText}>
                  {" "}
                  {formatPrice(orderDetail?.finalPrice)}
                </Typography>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
        <Stack sx={styles.sectionContainer}>
          <Stack gap={1} alignItems={"center"}>
            <Stack direction={"row"} sx={styles.contentContainer}>
              <Typography>Mã đơn hàng</Typography>
              <Typography>{orderDetail?.orderId}</Typography>
            </Stack>
            <Stack direction={"row"} sx={styles.contentContainer}>
              <Typography>Thời gian đặt hàng</Typography>
              <Typography>
                {moment(orderDetail?.createdAt).format("DD-MM-YYYY HH:mm")}
              </Typography>
            </Stack>
            <Stack direction={"row"} sx={styles.contentContainer}>
              <Typography>Trạng thái đơn hàng</Typography>
              <Typography>
                {OrderStatusText[orderDetail?.orderStatus || 1]}
              </Typography>
            </Stack>
          </Stack>
        </Stack>
      </Stack>
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  sectionContainer: {
    borderRadius: 4,
    background: "#fff",
    padding: 2,
    marginBlock: 1,
  },
  headerSection: {
    paddingBottom: 2,
    color: COLORS.accent1,
    gap: 2,
  },
  contentContainer: {
    justifyContent: "space-between",
    width: "100%",
  },
  priceText: {
    fontWeight: 700,
    color: COLORS.accent1,
  },
  noteText: {
    textAlign: "center",
    color: COLORS.neutral7,
    paddingBlock: 16,
  },
  chatBtn: {
    borderRadius: 99,
    color: COLORS.accent1,
    borderColor: COLORS.accent1,
    paddingBlock: 8,
  },
};
