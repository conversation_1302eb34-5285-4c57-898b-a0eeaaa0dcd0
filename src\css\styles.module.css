body, html, #root, .pageContent {
  overflow-x: hidden !important;
  max-width: 100vw !important;
}

.pageContent {
  padding-top: calc(var(--zaui-safe-area-inset-top, 0px) + 60px);
  padding-bottom: calc(var(--zaui-safe-area-inset-bottom, 0px) + 60px);
  min-height: 100vh;
}

.toast {
  margin-top: calc(var(--zaui-safe-area-inset-top, 0px));
}

.topSafe {
  padding-top: calc(var(--zaui-safe-area-inset-top, 0px) + 60px);
}

.bottomSafe {
  padding-bottom: calc(var(--zaui-safe-area-inset-bottom, 16px));
}

.bottomPayElement {
  padding-bottom: calc(var(--zaui-safe-area-inset-bottom, 16px) + 12px);
  width: 100vw;
  max-width: 450px !important;
  position: fixed;
  bottom: 0px;
  z-index: 1000;
  height: auto;
  border-top: 1px solid rgb(223, 223, 223);
  padding-top: 12px;
  background: rgb(255, 255, 255);
}

.bottomCartElement {
  /* padding-bottom: calc(var(--zaui-safe-area-inset-bottom, 16px)); */
  width: 100vw;
  max-width: 450px !important;
  position: fixed;
  bottom: 0px;
  z-index: 1000;
  height: auto;
  border-top: 1px solid rgb(223, 223, 223);
  background: rgb(255, 255, 255);
}

.bottomElementPreventScroll {
  padding-bottom: calc(var(--zaui-safe-area-inset-bottom, 16px));
  width: 100vw;
  max-width: 450px !important;
  position: fixed;
  bottom: 0px;
  z-index: 1000;
  align-items: center;
  justify-content: center;
}
