import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Cir<PERSON><PERSON><PERSON>ress, Divider, <PERSON>ack, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import FrameContainer from "../../components/layout/Container";
import { Icon } from "../../constants/Assets";
import { ZaloIcon } from "../../constants/IconSvg";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getOrderDetail, cancelOrder } from "../../redux/slices/order/orderSlice";
import { AppDispatch, RootState } from "../../redux/store";
import { IProduct } from "../../types/product";
import { formatPrice } from "../../utils/formatPrice";
import moment from "moment";
import {
  MethodText,
  OrderStatus,
  OrderStatusText,
  TypePay,
  TypePayText,
} from "../../constants/Const";
import { useTheme } from "@mui/material/styles";
import { useAlert } from "../../redux/slices/alert/useAlert";
import { useNavigate } from "../../utils/component-util";
import { openChat } from "../../utils/openChat";
import { COLORS, commonStyle } from "../../constants/themes";
import NoDataView from "../../components/UI/NoDataView";
import ShipIcon from "../../components/icon/ShipIcon";
import HistoryOrderIcon from "../../components/icon/HistoryOrderIcon";
import PaymentIcon from "../../components/icon/PaymentIcon";
import WarningIcon from "../../components/icon/WarningIcon";
import CartInfoIcon from "@/components/icon/CartInfoIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getItemOptionByIds } from "@/components/products/PopupChangeInfoATC";
import { showToast } from "@/utils/common";
import { Router } from "@/constants/Route";
import { Platform } from "@/config";
import { useCart } from "@/hooks/useCart";
import { ITaxInvoiceConfiguration } from "@/types/taxInvoice";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ReceiptLongIcon from "@mui/icons-material/ReceiptLong";
import SpeakerNotesOutlinedIcon from "@mui/icons-material/SpeakerNotesOutlined";

export default function OrderDetail() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const params = useParams();
  const theme = useTheme();
  const { color, ...appConfig } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const { orderDetail } = useSelector((state: RootState) => state.order);
  const { listPaymentMethod } = useSelector((state: RootState) => state.paymentMethod);
  const [loading, setLoading] = useState(false);
  const [loadingPayment, setLoadingPayment] = useState(false);
  const [showInvoice, setShowInvoice] = useState(false);
  const dataBranch = useSelector((state: RootState) => state.branch);
  const { createOrderZalo } = useCart();
  const { showAlert } = useAlert();
  const canRetryPayment =
    orderDetail?.statusPay === "NotPaid" &&
    orderDetail?.statusOrder === "Pending" &&
    [TypePay.TRANSFER, TypePay.VNPAY].includes(orderDetail?.typePay);
  useEffect(() => {
    if (user) {
      getOrderDetailFromId();
    }
  }, [params?.id, user]);

  const getOrderDetailFromId = async () => {
    if (!params?.id) return;
    setLoading(true);
    await dispatch(getOrderDetail(params.id));
    setLoading(false);
  };

  const numOfProducts = useMemo(() => {
    if (!Array.isArray(orderDetail?.listItems) || !orderDetail?.listItems.length) return 0;
    return orderDetail?.listItems.reduce((acc, item) => acc + item.quantity, 0);
  }, [orderDetail]);

  const onCancelOrder = async () => {
    if (!params.id) return;

    const res = await dispatch(cancelOrder(params.id)).unwrap();

    if (res.orderId) {
      showToast({
        content: "Hủy đơn thành công",
        type: "success",
      });
      navigate("/order");
      return;
    }
    showToast({
      content: res?.detail,
      type: "error",
    });
    // xử lý API hủy đơn hàng

    // hiển thị thông báo hủy đơn thành công
  };

  const onClickCancelButton = () => {
    showAlert({
      icon: <WarningIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
      title: "Xác nhận hủy đơn hàng",
      content: "Bạn có chắc chắn muốn hủy đơn hàng không?",
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "Xác nhận",
          action: onCancelOrder,
        },
      ],
    });
  };

  if (!orderDetail) return <NoDataView content="Không có chi tiết đơn hàng" />;

  const totalPrice = orderDetail.listItems.reduce(
    (total, item) => total + (item?.price || 0) * item.quantity,
    0
  );
  const transportPrice = orderDetail?.voucherTransportPrice || 0;
  const promotionPrice = orderDetail?.voucherPromotionPrice || 0;
  const pointPrice = orderDetail?.pointPrice || 0;

  const addressBranch = dataBranch?.list.filter(
    (item) => item.branchId === orderDetail.branchId
  )[0];

  const handleClickPayment = async () => {
    setLoadingPayment(true);
    if (orderDetail?.typePay === TypePay.TRANSFER) {
      navigate(Router.bankTransferV2.index, {
        state: { order: orderDetail, link: `/order/${orderDetail?.orderId}` },
      });
    }

    if (orderDetail.typePay === TypePay.VNPAY && Platform === "zalo") {
      const resZalo = await createOrderZalo(orderDetail);
      getOrderDetailFromId();
    }
    setLoadingPayment(false);
  };

  return (
    <FrameContainer title="Thông tin đơn hàng">
      {loading ? (
        <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
          <CircularProgress />
        </Stack>
      ) : (
        <Stack>
          <Stack sx={styles.sectionContainer}>
            <Stack
              direction="row"
              sx={{
                ...styles.headerSection,
                color: color.primary,
                marginBottom: 2,
              }}
            >
              <ShipIcon fillColor={color.primary} />
              <Stack>
                <b>Địa chỉ nhận hàng</b>
              </Stack>
            </Stack>
            <Divider />
            <Stack gap={2}>
              <Stack direction="row" gap={2} alignItems={"center"}>
                <Stack gap={1} marginTop={1} color={"#959595"}>
                  <Box>
                    {orderDetail?.statusDelivery === "InShop" ? (
                      <>
                        <Typography sx={{ fontWeight: 500 }}>
                          {addressBranch?.branchName}
                        </Typography>
                        <Typography sx={{ marginTop: 0.5 }}>
                          {addressBranch?.address ??
                            `${addressBranch?.wardName}, ${addressBranch?.districtName}, ${addressBranch?.provinceName}`}
                        </Typography>
                      </>
                    ) : (
                      <>
                        <Typography>{orderDetail?.userShippingAddress?.fullName}</Typography>
                        <Typography>{orderDetail?.userShippingAddress?.phoneNumber}</Typography>
                        <Typography>
                          {`${
                            orderDetail?.userShippingAddress?.address
                              ? orderDetail.userShippingAddress.address + ", "
                              : ""
                          }${orderDetail?.userShippingAddress?.wardName}, ${
                            orderDetail?.userShippingAddress?.districtName
                          }, ${orderDetail?.userShippingAddress?.provinceName}`}
                        </Typography>
                      </>
                    )}
                  </Box>
                </Stack>
              </Stack>
            </Stack>
          </Stack>
          <Stack sx={styles.sectionContainer}>
            <Stack
              direction="row"
              sx={{
                ...styles.headerSection,
                color: color.primary,
                marginBottom: 2,
              }}
            >
              <HistoryOrderIcon fillColor={color.primary} />
              <b>Sản phẩm đặt mua</b>
            </Stack>
            <Divider />
            <Stack gap={2} marginTop={2}>
              {orderDetail?.listItems?.map((item, index) => {
                const imageItemOrder = item.images[0].link;
                // const variantItemOrder = item?.product?.selectVariant?.attribute
                //   ?.map(
                //     (attr) =>
                //       `${attr.variant?.data?.attributes?.name}: ${attr.value}`,
                //   )
                //   .join(", ");
                // const priceItemOrder = formatPrice(
                //   item.product.selectVariant
                //     ? item.product.selectVariant.price -
                //         (item.product.selectVariant.discount || 0)
                //     : item.product.price - (item.product.discount || 0),
                // );
                return (
                  <Stack
                    key={`${item.itemsId}${index}`}
                    direction="row"
                    gap={2}
                    alignItems={"start"}
                  >
                    <img
                      style={styles.imageProduct}
                      src={imageItemOrder}
                      onError={(e) => {
                        e.currentTarget.onerror = null;
                        e.currentTarget.src = appConfig?.shopLogo?.link || "";
                      }}
                    />
                    <Stack
                      gap={0.5}
                      flexGrow={1}
                      direction="row"
                      justifyContent="space-between"
                      minWidth={0}
                    >
                      <Box sx={{ maxWidth: "100%", width: "100%", minWidth: 0 }}>
                        <Typography
                          style={{
                            color: COLORS.neutral2,
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                            whiteSpace: "normal",
                          }}
                        >
                          {item?.itemsName}
                        </Typography>
                        {item.isVariant && (
                          <Typography
                            noWrap
                            fontSize="12px"
                            style={{
                              color: COLORS.neutral4,
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              display: "-webkit-box",
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: "vertical",
                              whiteSpace: "normal",
                            }}
                          >
                            {[item.variantValueOne, item.variantValueTwo, item.variantValueThree]
                              .filter(Boolean)
                              .join(", ")}
                          </Typography>
                        )}
                        {Array.isArray(item.extraOptions) && item.extraOptions.length > 0 && (
                          <ItemOptionsInfo item={item} />
                        )}
                        {item.note && (
                          <Typography
                            fontSize="12px"
                            sx={{
                              color: COLORS.neutral4,
                            }}
                          >
                            Ghi chú: {item.note}
                          </Typography>
                        )}
                      </Box>

                      <Typography
                        style={{
                          ...commonStyle.headline16,
                          color: "#7D7D7D",
                          textAlign: "end",
                        }}
                      >
                        x{item.quantity}
                      </Typography>
                    </Stack>
                  </Stack>
                );
              })}
            </Stack>
          </Stack>
          <Stack sx={styles.sectionContainer}>
            <Stack
              direction="row"
              sx={{
                ...styles.headerSection,
                color: color.primary,
                marginBottom: 2,
              }}
            >
              <CartInfoIcon fillColor={color.primary} />
              <b>Chi tiết thanh toán</b>
            </Stack>
            <Divider />
            <Stack gap={2} marginTop={2}>
              <Stack gap={1} alignItems={"center"}>
                <Stack direction={"row"} sx={styles.contentContainer}>
                  <Typography style={{ color: COLORS.neutral4 }}>Tổng tiền hàng</Typography>
                  <Typography>{orderDetail !== null ? formatPrice(totalPrice) : 0}</Typography>
                </Stack>
                <Stack direction={"row"} sx={styles.contentContainer}>
                  <Typography style={{ color: COLORS.neutral4 }}>Phí vận chuyển</Typography>
                  <Typography>{formatPrice(orderDetail?.transportPrice || 0)}</Typography>
                </Stack>
                <Stack direction={"row"} sx={styles.contentContainer}>
                  <Typography style={{ color: COLORS.neutral4 }}>
                    Giảm giá phí vận chuyển
                  </Typography>
                  <Typography>
                    {transportPrice > 0
                      ? `-${formatPrice(transportPrice)}`
                      : formatPrice(transportPrice)}
                  </Typography>
                </Stack>
                <Stack direction={"row"} sx={styles.contentContainer}>
                  <Typography style={{ color: COLORS.neutral4 }}>Voucher giảm giá</Typography>
                  <Typography>
                    {promotionPrice > 0
                      ? `-${formatPrice(promotionPrice)}`
                      : formatPrice(promotionPrice)}
                  </Typography>
                </Stack>
                <Stack direction={"row"} sx={styles.contentContainer}>
                  <Typography style={{ color: COLORS.neutral4 }}>
                    Giảm giá điểm thưởng tích lũy
                  </Typography>
                  <Typography>
                    {pointPrice > 0 ? `-${formatPrice(pointPrice)}` : formatPrice(pointPrice)}
                  </Typography>
                </Stack>
                <Stack direction={"row"} sx={styles.contentContainer}>
                  <Typography style={{ color: COLORS.neutral4 }}>Thuế</Typography>
                  <Typography>{formatPrice(orderDetail?.totalTaxAmount || 0)}</Typography>
                </Stack>
                <Stack direction={"row"} sx={styles.contentContainer}>
                  <Typography sx={{ ...styles.priceText, color: color.primary }}>
                    Thành tiền
                  </Typography>
                  <Typography sx={{ ...styles.priceText, color: color.primary }}>
                    {" "}
                    {formatPrice(orderDetail?.totalAfterTax)}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>
          </Stack>
          {orderDetail?.notes && (
            <Stack sx={styles.sectionContainer}>
              <Stack
                direction="row"
                sx={{ ...styles.headerSection, color: color.primary, marginBottom: 2 }}
              >
                <SpeakerNotesOutlinedIcon style={{ color: color.primary }} />
                <b>Lời nhắn</b>
              </Stack>
              <Divider />
              <Typography
                sx={{
                  color: COLORS.neutral4,
                  fontSize: 15,
                  mt: 2,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  display: "-webkit-box",
                  WebkitBoxOrient: "vertical",
                  whiteSpace: "normal",
                }}
              >
                {orderDetail.notes}
              </Typography>
            </Stack>
          )}
          <Stack sx={styles.sectionContainer}>
            <Stack
              direction="row"
              sx={{
                ...styles.headerSection,
                color: color.primary,
                marginBottom: 2,
                alignItems: "center",
              }}
            >
              <ReceiptLongIcon />
              <b style={{ marginLeft: 8 }}>Xuất hoá đơn GTGT</b>
              <Box sx={{ flex: 1 }} />
            </Stack>
            <Divider />
            {orderDetail && (orderDetail.taxInvoice as ITaxInvoiceConfiguration) ? (
              <Stack gap={1} marginTop={2}>
                <Typography>
                  {orderDetail.taxInvoice?.taxPayerType === "Individual"
                    ? "Tên khách hàng"
                    : "Tên công ty"}
                  : {orderDetail.taxInvoice?.name}
                </Typography>
                <Typography>Mã số thuế: {orderDetail.taxInvoice?.taxCode}</Typography>
                <Typography>Email: {orderDetail.taxInvoice?.email}</Typography>
                <Typography>Địa chỉ: {orderDetail.taxInvoice?.address}</Typography>
              </Stack>
            ) : (
              <Typography marginTop={2} color={COLORS.neutral4}>
                Không có thông tin hoá đơn GTGT
              </Typography>
            )}
          </Stack>
          <Stack sx={styles.sectionContainer}>
            <Stack
              direction="row"
              sx={{
                ...styles.headerSection,
                color: color.primary,
                marginBottom: 2,
              }}
            >
              <PaymentIcon fillColor={color.primary} />
              <b>Phương thức thanh toán</b>
            </Stack>
            <Divider />
            <Stack gap={2} marginTop={2}>
              <Stack gap={1} alignItems={"center"}>
                <Stack direction={"row"} justifyContent="space-between" width={"100%"}>
                  <Typography color={"#757575"}>
                    {TypePayText[orderDetail.typePay.toLocaleLowerCase()]}
                  </Typography>
                </Stack>
                {canRetryPayment && (
                  <Stack direction={"row"} justifyContent="space-between" width={"100%"}>
                    <Button
                      style={{
                        borderRadius: 5,
                        color: color.primary,
                        border: "1px solid " + color.primary,
                      }}
                      onClick={handleClickPayment}
                    >
                      Thanh toán lại
                    </Button>
                  </Stack>
                )}
                <Backdrop
                  sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
                  open={loadingPayment}
                >
                  <CircularProgress color="inherit" />
                </Backdrop>
              </Stack>
            </Stack>
          </Stack>
          <Stack sx={styles.sectionContainer}>
            <Stack gap={1} alignItems={"center"}>
              <Stack direction={"row"} sx={styles.contentContainer}>
                <Typography>Mã đơn hàng</Typography>
                <Typography>{orderDetail?.orderNo}</Typography>
              </Stack>
              <Stack direction={"row"} sx={styles.contentContainer}>
                <Typography>Thời gian đặt hàng</Typography>
                <Typography>{moment(orderDetail?.created).format("DD-MM-YYYY HH:mm")}</Typography>
              </Stack>
              <Stack direction={"row"} sx={styles.contentContainer}>
                <Typography>Trạng thái đơn hàng</Typography>
                <Typography>{OrderStatusText[orderDetail?.statusOrder]}</Typography>
              </Stack>
              <Stack paddingInline={4} gap={1} width={"100%"}>
                <Typography style={styles.noteText}>
                  Hãy chat với chúng tôi nếu bạn muốn thay đổi thông tin đơn hàng bạn nhé
                </Typography>
                <Button
                  style={{
                    ...styles.chatBtn,
                    color: "white",
                    background: color.primary,
                  }}
                  onClick={() => openChat(appConfig.oaId || "")}
                  fullWidth
                  startIcon={<ZaloIcon />}
                >
                  Chat với&nbsp;
                  <span
                    style={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      maxWidth: "120px",
                      display: "inline-block",
                      verticalAlign: "bottom",
                    }}
                    title={appConfig?.shopName}
                  >
                    {appConfig?.shopName}
                  </span>
                  &nbsp;nhé
                </Button>
                {orderDetail?.statusOrder == "Pending" && (
                  <Button
                    style={{
                      borderRadius: 5,
                      color: color.primary,
                      border: "1px solid " + color.primary,
                    }}
                    onClick={onClickCancelButton}
                  >
                    Huỷ đơn hàng
                  </Button>
                )}
              </Stack>
            </Stack>
          </Stack>
        </Stack>
      )}
    </FrameContainer>
  );
}

const ItemOptionsInfo = ({ item }) => {
  const [itemOptionGroups, setItemOptionGroups] = useState<any[]>([]);
  const fetchAndSetItemOptions = async (itemOptionIds) => {
    const response = await getItemOptionByIds(itemOptionIds);

    if (response?.data) {
      setItemOptionGroups(response?.data.data);
    }
  };

  useEffect(() => {
    if (Array.isArray(item.extraOptions) && item.extraOptions.length > 0) {
      fetchAndSetItemOptions(item.extraOptions);
    }
  }, [item?.extraOptions]);

  return (
    <Box>
      {itemOptionGroups.length > 0 && (
        <Typography
          fontSize="12px"
          sx={{
            color: COLORS.neutral4,
            overflow: "hidden",
            textOverflow: "ellipsis",
            display: "-webkit-box",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
            whiteSpace: "normal",
          }}
        >
          {itemOptionGroups
            .map(
              (itemGroup) =>
                `${itemGroup.name}: ${itemGroup.itemOptions
                  .map((itemOption) => itemOption.name)
                  .join(", ")}`
            )
            .join("; ")}
        </Typography>
      )}
    </Box>
  );
};

const styles: Record<string, React.CSSProperties> = {
  sectionContainer: {
    borderRadius: 1,
    background: "#fff",
    padding: 2,
    marginBlock: 1,
  },
  headerSection: {
    color: COLORS.accent1,
    gap: 2,
  },
  contentContainer: {
    justifyContent: "space-between",
    width: "100%",
  },
  priceText: {
    fontWeight: 700,
    color: COLORS.accent1,
  },
  noteText: {
    textAlign: "center",
    color: COLORS.neutral7,
    paddingBlock: 16,
  },
  chatBtn: {
    fontSize: 12,
    borderRadius: 5,
    color: COLORS.accent1,
    borderColor: COLORS.accent1,
    paddingBlock: 8,
  },
  imageProduct: {
    borderRadius: 5,
    width: 80,
    height: 80,
    minWidth: 80,
    objectFit: "cover",
  },
};
