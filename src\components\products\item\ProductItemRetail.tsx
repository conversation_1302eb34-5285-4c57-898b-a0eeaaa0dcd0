import { Icon } from "@/constants/Assets";
import { COLOR, COLORS } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { IProduct } from "@/types/product";
import { useNavigate } from "@/utils/component-util";
import { formatPrice } from "@/utils/formatPrice";
import { Box, Stack, Typography } from "@mui/material";
import React from "react";
import PopupIconAddToCart from "../../UI/PopupIconAddToCart";
interface ProductItemRetailProps {
  item: IProduct;
  discountType?: string;
}

function formatVariantSummaryByName(listVariant: any[]) {
  const nameValuePairs = [
    ["variantNameOne", "variantValueOne"],
    ["variantNameTwo", "variantValueTwo"],
    ["variantNameThree", "variantValueThree"],
  ];

  const variantMap = new Map<string, Set<string>>();

  listVariant.forEach((variant) => {
    nameValuePairs.forEach(([nameKey, valueKey]) => {
      const name = variant[nameKey];
      const value = variant[valueKey];

      if (name && value) {
        if (!variantMap.has(name)) {
          variantMap.set(name, new Set());
        }
        variantMap.get(name)!.add(value);
      }
    });
  });

  const result = Array.from(variantMap.entries())
    .map(([name, values]) => `${name.toLowerCase()}: ${Array.from(values).join(", ")}`)
    .join("; ");

  return result;
}

const ProductItemRetail: React.FC<ProductItemRetailProps> = (props) => {
  const { item, discountType } = props;
  const navigate = useNavigate();
  const { color } = useConfigApp();
  const appConfig = useConfigApp();

  const onNavigateToDetail = () => {
    navigate(`/product-detail/${item.itemsCode}`);
  };

  const handleError = (e) => {
    e.target.src = Icon.errorImage; // Đặt URL ảnh thay thế
  };

  let URLImage = item?.images && item?.images.length > 0 ? item?.images[0]?.link : "";

  const salePrice = item?.price || item?.listVariant[0].price || 0;
  const priceReal = item?.priceReal || item?.listVariant[0].priceReal || 0;

  const rawDiscount =
    priceReal > salePrice && priceReal > 0 ? ((priceReal - salePrice) / priceReal) * 100 : 0;

  const discountPercent =
    rawDiscount < 1 && rawDiscount > 0 ? 1 : rawDiscount > 99 ? 99 : Math.round(rawDiscount);

  return (
    <Box style={{ ...styles.container, backgroundColor: COLORS.bgColor.fourth }}>
      {/* {priceReal !== salePrice && discountPercent > 0 && (
        <Typography style={{ ...styles.discountText, background: color.primary }}>
          -{discountPercent}%
        </Typography>
      )} */}
      <img
        style={styles.imageProduct}
        src={URLImage}
        alt={item?.itemsName || ""}
        onClick={onNavigateToDetail}
        onError={handleError}
      />
      <Box style={styles.contentContainer}>
        <Box onClick={onNavigateToDetail}>
          <Box style={styles.productTitle}>{item?.itemsName}</Box>
          <Typography style={styles.variantSummaryText}>
            {item.isVariant && formatVariantSummaryByName(item?.listVariant)}
          </Typography>
          {/* <Typography style={styles.soldText}>{`Đã bán ${formatNumberToK(item?.sold)}`}</Typography> */}
        </Box>
        {/* Block giá sale + badge */}
        <Box sx={{ minHeight: 28, display: "flex", alignItems: "center", gap: 1 }}>
          <Typography
            style={{
              ...styles.salePriceText,
              color: color.primary,
              fontSize: 18,
              fontWeight: 700,
              whiteSpace: "nowrap",
            }}
          >
            {formatPrice(salePrice, " VNĐ")}
          </Typography>
          {discountPercent > 0 && (
            <Box
              sx={{
                background: "#FF424E",
                color: "#fff",
                fontWeight: 600,
                fontSize: 14,
                borderRadius: "12px",
                px: 0.2,
                py: 0.2,
                display: "inline-block",
                lineHeight: 1.1,
              }}
            >
              -{discountPercent}%
            </Box>
          )}
        </Box>

        {/* Block giá gốc, luôn giữ 1 dòng, kể cả không có giảm giá */}
        <Box sx={{ minHeight: 18, display: "flex", alignItems: "center" }}>
          {priceReal !== salePrice ? (
            <Typography
              style={{
                color: "#B7B7B7",
                fontSize: 14,
                textDecoration: "line-through",
                fontWeight: 400,
                textAlign: "left",
                width: "fit-content",
                whiteSpace: "nowrap",
              }}
            >
              {formatPrice(priceReal, " VNĐ")}
            </Typography>
          ) : (
            <span style={{ visibility: "hidden", fontSize: 14 }}>_</span>
          )}
        </Box>

        <Box
          sx={{
            ...styles.discountPriceTextContainer,
          }}
        >
          {/* <Box
            style={{ ...styles.rateContainer }}
            display={"flex"}
            flexWrap={"wrap-reverse"}
            className="rate-container"
          >
            <RatingStars rating={4.9} />
          </Box> */}
          {item.quantity > 0 &&
          item &&
          item?.listVariant.length > 0 &&
          item.listVariant[0].price > 0 ? (
            <Stack justifyContent="center" direction="row" width="100%">
              <PopupIconAddToCart
                product={item}
                buttonStyle={{
                  border: `1px solid ${appConfig.color.primary}`,
                  padding: "4px 16px",
                  borderRadius: 100,
                  fontSize: 12,
                  color: appConfig.color.primary,
                  whiteSpace: "nowrap",
                }}
                IconComponent={"Chọn sản phẩm"}
              />
            </Stack>
          ) : (
            <Typography style={{ ...styles, color: color.primary }}>Liên hệ</Typography>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default ProductItemRetail;

const styles: Record<string, React.CSSProperties> = {
  container: {
    backgroundColor: COLOR.bg.product_item,
    borderRadius: 5,
    cursor: "pointer",
    position: "relative",
  },
  discountText: {
    position: "absolute",
    top: 10,
    left: 10,
    paddingBlock: 2,
    paddingInline: 7,
    textAlign: "center",
    color: COLOR.text.white,
    backgroundColor: COLOR.bg.product_discount,
    fontSize: 12,
    fontWeight: 700,
    borderRadius: 10,
    lineHeight: "17px",
  },
  discountPricePlaceholder: {
    height: 16,
    visibility: "hidden",
    display: "inline-block",
  },
  imageProduct: {
    borderRadius: 5,

    width: "100%",
    aspectRatio: 1,
    objectFit: "cover",
  },
  contentContainer: {
    paddingInline: 10,
    paddingBlock: 10,
    display: "flex",
    flexDirection: "column",
  },
  priceContainer: {
    display: "flex",
    alignItems: "baseline",
    gap: 4,
    flexWrap: "wrap",
    minHeight: 45,
  },
  productTitle: {
    color: COLOR.text.product_item,
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 1,
    cursor: "pointer",
    fontWeight: 500,
    lineHeight: "17px",
    letterSpacing: "0.14px",
    fontSize: 14,
    // minHeight: 36,
  },
  productPriceText: {
    display: "flex",
    color: COLORS.primary1,
    fontWeight: 700,
    fontStyle: "normal",
    fontSize: 14,
    alignItems: "center",
  },
  discountPriceTextContainer: {
    display: "flex",
    fontWeight: 700,
    alignItems: "center",
    justifyContent: "space-between",
    color: COLORS.primary1,
  },
  addBtn: {
    alignSelf: "flex-end",
    padding: 0,
    minWidth: 20,
  },
  discountPrice: {
    textDecoration: "line-through",
    fontSize: 11,
    fontWeight: 400,
    color: COLOR.text.product_price,
  },
  salePriceText: {
    fontSize: 16,
    fontWeight: 700,
  },
  rateContainer: {
    display: "flex",
    alignItems: "baseline",
    gap: 2,
  },
  rateText: {
    background: "#E6E6E6",
    color: "#747474",
    fontStyle: "normal",
    paddingInline: 2,
    paddingBlock: 2,
    borderRadius: 3,
    display: "flex",
    alignItems: "baseline",
    gap: 1,
  },
  soldText: {
    color: COLOR.text.product_sold,
    fontSize: 11,
    fontWeight: 400,
    fontStyle: "normal",
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 2,
    minHeight: 16,
  },
  variantSummaryText: {
    color: COLOR.text.product_sold,
    fontSize: 8,
    fontWeight: 400,
    fontStyle: "normal",
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 1,
    minHeight: 12,
  },
};
