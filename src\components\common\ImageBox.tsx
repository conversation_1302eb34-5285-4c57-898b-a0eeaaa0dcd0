import React, { memo } from "react";
import { Box } from "@mui/material";

const ImageBox = ({ url, width, ratio, borderRadius}) => {
  return (
    <>
      <Box>
        <img style={{...styles.imageContainer, width, aspectRatio: ratio, borderRadius}} src={url} />
      </Box>
    </>
  );
};

export default memo(ImageBox);

const styles: Record<string, React.CSSProperties> = {
  imageContainer: {
    display: "flex",
    overflow: "hidden",
    backgroundSize: "cover",
    backgroundPosition: "center",
  }
};
