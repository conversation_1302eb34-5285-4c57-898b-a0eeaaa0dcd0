import React, { memo } from "react";
import { Grid } from "@mui/material";

import { INews } from "../../types/news";
import NewsItem from "./NewsItem";

interface ListNewsProps {
  list?: INews[];
  titleFromParent?: string;
}

const ListNews = ({ list = [], titleFromParent }: ListNewsProps) => {
  if (!list?.length) return null;

  return (
    <Grid container spacing={1} paddingBlock={1}>
      {list.map((news: INews, index) => (
        <NewsItem news={news} key={String(index)} titleFromParent={titleFromParent} />
      ))}
    </Grid>
  );
};

export default memo(ListNews);
