import { atom, selector } from "recoil";
import {
  authorize,
  getLocation,
  getPhoneNumber,
  getSetting,
  getUserInfo,
  getAccessToken,
} from "zmp-sdk";
import { useSetRecoilState } from "recoil";

export const authorizedState = selector({
  key: "authorized",
  get: async () => {
    const { authSetting } = await getSetting({});
    if (!authSetting["scope.userInfo"]) {
      await authorize({ scopes: [] });
    }
  },
});

export const userState = selector({
  key: "user",
  get: async ({ get }) => {
    get(authorizedState);
    const { userInfo } = await getUserInfo({ avatarType: "small" });
    return userInfo;
  },
});

export const phoneSuccessDataState = atom({
  key: "phoneSuccessData",
  default: null, // Giá trị mặc định là null
});

function updatePhoneSuccessData(data) {
  const setPhoneSuccessData = useSetRecoilState(phoneSuccessDataState);
  setPhoneSuccessData(data);
}

// Tạo một atom để lưu trữ giá trị data từ hàm success
export const accessTokenSuccess = atom({
  key: "accessTokenSuccess",
  default: null, // Giá trị mặc định là null
});

function updateAccessTokenSuccess(token) {
  const setAccessTokenSuccess = useSetRecoilState(accessTokenSuccess);
  setAccessTokenSuccess(token);
}

// Hàm mới để lấy AccessToken
export const accessTokenState = selector({
  key: "accessToken",
  get: async () => {
    const token = await getAccessToken(); // Gọi API để lấy AccessToken
    updateAccessTokenSuccess(token);
    return token;
  },
});
