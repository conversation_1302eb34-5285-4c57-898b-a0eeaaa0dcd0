/* chrome and chromium based */
.react-horizontal-scrolling-menu--scroll-container::-webkit-scrollbar {
  display: none;
}

.react-horizontal-scrolling-menu--scroll-container {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  /* width: 100%; */
}

.react-horizontal-scrolling-menu--item {
  width: calc(calc(100vw - 40px) / 5);
}

@media (min-width: 450px) {
  .react-horizontal-scrolling-menu--item {
    width: calc(calc(450px - 40px) / 5) !important;
  }
}
