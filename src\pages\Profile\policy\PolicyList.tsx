import { But<PERSON>, <PERSON>ack, Typography } from "@mui/material";
import { default as React, useEffect, useState } from "react";
import FrameContainer from "../../../components/layout/Container";
import { useNavigate } from "../../../utils/component-util";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../redux/store";
import { COLORS } from "../../../constants/themes";
import { RightChevron } from "../../../constants/IconSvg";

export default function PolicyList() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { shopInfo } = useSelector((state: RootState) => state.appInfo);
  const [shopPolicy, setShopPolicy] = useState<any>();

  const fetchShopPolicy = async () => {
    if (shopId) {
      const listPolicyString = shopInfo?.shopPolicy;
      const listPolicy = JSON.parse(listPolicyString);
      setShopPolicy(listPolicy);
    }
  };
  useEffect(() => {
    if (shopInfo && shopInfo.shopPolicy) {
      fetchShopPolicy();
    }
  }, [shopId, shopInfo]);

  return (
    <FrameContainer title="Điều khoản và hỏi đáp">
      <Stack sx={styles.container}>
        {shopPolicy &&
          shopPolicy.map((policy: any, i) => (
            <Stack key={`policy-${i}`} style={styles.itemContainer}>
              <Button
                sx={styles.content}
                onClick={() => {
                  navigate(`/profile/policy/${policy.id}`, { state: { policy } });
                }}
              >
                <Typography style={styles.title}>{policy?.title}</Typography>
                <RightChevron width={11} height={19} />
              </Button>
            </Stack>
          ))}
      </Stack>
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    marginTop: 1.25,
    marginBottom: 4,
    borderRadius: "5px",
    background: COLORS.white,
    padding: "0 10px",
    minHeight: "50px",
  },
  itemContainer: {
    padding: "15px 10px",
    borderBottom: `1px solid #EBEBEB`,
  },
  content: {
    padding: 0,
    display: "flex",
    flexDirection: "row",
    gap: 3,
    justifyContent: "space-between",
  },
  title: {
    fontSize: "15px",
    fontWeight: 500,
    color: COLORS.neutral2,
    textAlign: "left",
  },
};
