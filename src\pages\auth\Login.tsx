import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Icon<PERSON>utton,
  InputAdornment,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { AppDispatch, RootState } from "@/redux/store";
import { useDispatch, useSelector } from "react-redux";
import { loginForWeb, getUser } from "@/redux/slices/authen/authSlice";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import FrameContainerWeb from "@/components/layout/Layout";
import { showToast } from "@/utils/common";
import { useConfigApp } from "@/hooks/useConfigApp";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { Visibility, VisibilityOff } from "@mui/icons-material";

const validationSchema = yup.object({
  userName: yup
    .string()
    .required("<PERSON>ui lòng nhập số điện thoại")
    .min(10, "Tối thiểu 10 ký tự")
    .max(13, "Tối đa 13 ký tự"),
  phone: yup
    .string()
    .required("Vui lòng nhập mật khẩu")
    .min(8, "Mật khẩu phải dài từ 8 đến 20 ký tự")
    .max(20, "Mật khẩu phải dài từ 8 đến 20 ký tự"),
});

export default function Login() {
  const appConfig = useConfigApp();
  const {
    control,
    handleSubmit,
    register,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      userName: "", // <- đảm bảo không undefined
      phone: "", // <- tương tự
    },
  });
  const userName = watch("userName");
  const phone = watch("phone");
  const isMainFieldsValid = !!userName && !errors.userName && !!phone && !errors.phone;
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { token } = useSelector((state: RootState) => state.auth);
  useEffect(() => {
    if (token) {
      navigate("/");
    }
  }, [token]);
  const calcCartAfterLogin = useCalcCartAfterLogin();
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const onSubmit = async (data) => {
    const res = await dispatch(
      loginForWeb({
        userPhoneZalo: data.userName,
        password: data.phone,
      })
    ).unwrap();
    if (res) {
      showToast({
        content: "Đăng nhập tài khoản thành công",
        type: "success",
      });
      await dispatch(getUser());
      await calcCartAfterLogin();
      navigate(Router.homepage);
    } else {
      showToast({
        content: "Đăng nhập tài khoản thất bại",
        type: "error",
      });
    }
  };

  return (
    <FrameContainerWeb>
      <Stack
        direction="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        paddingInline={2}
        marginTop={-15}
      >
        <Box width={"100%"}>
          <Container maxWidth="lg">
            <Stack direction="row" justifyContent="center" alignItems="center">
              <span style={{ fontWeight: 700, fontSize: 24 }}>Đăng nhập</span>
            </Stack>
          </Container>
        </Box>
        <Box width={"100%"} mt={5}>
          <Container maxWidth="sm">
            <form onSubmit={handleSubmit(onSubmit)}>
              <Stack gap={3} mb={5}>
                <Controller
                  name="userName"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      // required
                      id="standard-required"
                      label="Số điện thoại *"
                      type="tel"
                      variant="standard"
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
                <Controller
                  name="phone"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      type={showPassword ? "text" : "password"}
                      fullWidth
                      label="Mật khẩu *"
                      variant="standard"
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton onClick={() => setShowPassword((prev) => !prev)} edge="end">
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Stack>
              <Button
                type="submit"
                style={{
                  color: isMainFieldsValid ? "#fff" : appConfig.color.accent,
                  width: "100%",
                  height: 55,
                  fontSize: "16px",
                  fontWeight: 600,
                  lineHeight: "19.36px",
                  border: "1px solid transparent",
                  borderRadius: 5,
                  padding: "2px",
                  backgroundColor: appConfig.color.primary,
                }}
                variant="contained"
              >
                Đăng nhập
              </Button>
              <Typography align="center" color="textSecondary" sx={{ mt: 2, fontSize: 13 }}>
                Bạn chưa có tài khoản?{" "}
                <Typography
                  ml={1}
                  fontWeight={700}
                  component="span"
                  style={{
                    cursor: "pointer",
                    textDecoration: "underline",
                    color: appConfig.color.primary,
                  }}
                  onClick={() => navigate(`${Router.register}`)}
                >
                  Đăng ký
                </Typography>
              </Typography>
            </form>
          </Container>
        </Box>
      </Stack>
    </FrameContainerWeb>
  );
}
