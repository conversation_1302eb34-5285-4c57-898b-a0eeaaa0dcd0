import { AppCategoryValue } from "@/constants/Const";
import { useConfigApp } from "@/hooks/useConfigApp";
import Login from "@/pages/auth/Login";
import OtpPage from "@/pages/auth/Otp";
import Register from "@/pages/auth/Register";
import BankTransfer from "@/pages/BankTransfer";
import BankTransferV2 from "@/pages/BankTransferV2";
import Branch from "@/pages/Branch/Branch";
import CartPayment from "@/pages/CartPayment";
import CheckoutResult from "@/pages/CheckoutResult";
import CollabHistoryPage from "@/pages/Collab/CollabHistory";
import CollabMemberDetail from "@/pages/Collab/CollabMemberDetail";
import CollabMembers from "@/pages/Collab/CollabMembers";
import CollabOrders from "@/pages/Collab/CollabOrders";
import UserCommission from "@/pages/Collab/UserCommission";
import EnterCode from "@/pages/EnterCode";
import HomePage from "@/pages/Home/index";
import InvoiceRequest from "@/pages/InvoiceRequest";
import Notification from "@/pages/Notification";
import Order from "@/pages/Order";
import OrderDetail from "@/pages/Order/OrderDetail";
import OrderF1Detail from "@/pages/Order/OrderF1Detail";
import PaymentInfomation from "@/pages/PaymentInfomation";
import Point from "@/pages/Point";
import PostDetail from "@/pages/Post/PostDetail";
import Posts from "@/pages/Post/Posts";
import Product from "@/pages/Product";
import ProductCategoryPage from "@/pages/ProductCategoryPage";
import ProductDetail from "@/pages/ProductDetail";
import Address from "@/pages/Profile/Address";
import EarnPointsGuide from "@/pages/Profile/EarnPointsGuide";
import Info from "@/pages/Profile/Info";
import MemberShip from "@/pages/Profile/MemberShip";
import MembershipBenefit from "@/pages/Profile/MembershipBenefit";
import MembershipDetail from "@/pages/Profile/MembershipDetail";
import PaymentInfo from "@/pages/Profile/Payment";
import PointHistory from "@/pages/Profile/PointHistory";
import PolicyDetail from "@/pages/Profile/policy/PolicyDetail";
import PolicyList from "@/pages/Profile/policy/PolicyList";
import Profile from "@/pages/Profile/Profile";
import RegisterPartner from "@/pages/Profile/RegisterPartner";
import ReferPage from "@/pages/Refer";
import SearchProduct from "@/pages/Search/SearchProduct";
import TeamListPage from "@/pages/TeamList";
import PointCodeDetail from "@/pages/voucher/PointCodeDetail";
import Voucher from "@/pages/voucher/Voucher";
import VoucherDetail from "@/pages/voucher/VoucherDetail";
import VoucherEnterCode from "@/pages/voucher/VoucherEnterCode";
import VoucherTabsPage from "@/pages/voucher/VoucherTabsPage";
import { ThemeProvider } from "@mui/material";
import React from "react";
import { Toaster } from "react-hot-toast";
import { Provider } from "react-redux";
import { Route, Routes, useLocation } from "react-router-dom";
import { RecoilRoot } from "recoil";
import { PersistGate } from "redux-persist/integration/react";
import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";
import { App, SnackbarProvider } from "zmp-ui";
import { Router } from "../constants/Route";
import { useAppTheme } from "../constants/themes";
import initData from "../hooks/initData";
import Menu from "../pages/menu/index";
import store, { persistor } from "../redux/store";
import { AppRouter, BottomNavigation } from "../utils/component-util";
import AppProvider from "./AppProvider";
import { ScrollToTop } from "./base/ScrollToTop";
import StickyButtons from "./common/StickyButtons";
import CustomAlert from "./UI/CustomAlert";
import PromogameGame from "@/pages/Game/PromogameGame";
import Promotion from "@/pages/Promotion";
import GameBox from "@/components/common/GameBox";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { keyComponent } from "@/types/home";

const Root = () => {
  const appConfig = useConfigApp();
  initData();
  const location = useLocation();
  const noBottomNavPaths = [Router.game];
  const isVoucherDetailPage = location.pathname.startsWith("/voucher/");
  const showBottomNav = !noBottomNavPaths.includes(location.pathname) && !isVoucherDetailPage;
  const { home } = useConfigApp();
  const isHomePage = location.pathname === Router.homepage;

  return (
    <>
      <Routes>
        <>
          <Route path={Router.login} element={<Login />} />
          <Route path={Router.register} element={<Register />} />
          <Route path={Router.otp} element={<OtpPage />} />
          <Route path={Router.homepage} element={<HomePage />} />
          <Route path={Router.profile.index}>
            <Route index element={<Profile />} />
            <Route path={Router.profile.info} element={<Info />} />
            <Route path={Router.profile.memberShip} element={<MemberShip />} />
            <Route path={Router.profile.payment} element={<PaymentInfo />} />
            <Route path={Router.profile.address} element={<Address />} />
            <Route path={Router.profile.policy.index}>
              <Route index element={<PolicyList />} />
              <Route path={Router.profile.policy.detail} element={<PolicyDetail />} />
            </Route>
            <Route path={Router.profile.teamList.index} index element={<TeamListPage />} />
            <Route path={Router.profile.sigupPartner} element={<RegisterPartner />} />
            <Route path={Router.profile.membershipDetail} element={<MembershipDetail />} />
            <Route path={Router.profile.earnPointsGuide} element={<EarnPointsGuide />} />
            <Route path={Router.profile.pointHistory} element={<PointHistory />} />
            <Route path={Router.profile.membershipBenefit} element={<MembershipBenefit />} />
          </Route>
          <Route path={Router.voucher.index}>
            <Route index element={<VoucherTabsPage />} />
            <Route path={Router.voucher.detail} element={<VoucherDetail />} />
          </Route>
          <Route path={Router.voucher.enterCode} element={<VoucherEnterCode />} />
          <Route path={Router.voucher.home} element={<Voucher />} />
          <Route path={Router.promotion.index} element={<Promotion />} />
          <Route path={Router.point} element={<Point />} />
          <Route path={Router.order.index} element={<Order />} />
          <Route path={Router.order.detail} element={<OrderDetail />} />
          <Route path={Router.order.f1Detail} element={<OrderF1Detail />} />
          <Route path={Router.post.index} element={<Posts />} />
          <Route path={Router.post.detail} element={<PostDetail />} />
          <Route path={Router.notification} element={<Notification />} />

          <Route path={Router.refer.index}>
            <Route index element={<ReferPage />} />
            <Route path={Router.refer.detail} element={<EnterCode />} />
          </Route>
          <Route path={Router.collabhistory.index}>
            <Route index element={<CollabHistoryPage />} />
            <Route path={Router.collabhistory.commission.index} element={<UserCommission />} />
            <Route path={Router.collabhistory.members.index} element={<CollabMembers />} />
            <Route path={Router.collabhistory.members.detail} element={<CollabMemberDetail />} />
            <Route path={Router.collabhistory.orders.index} element={<CollabOrders />} />
          </Route>
          <Route
            path={Router.product}
            element={appConfig.businessType === AppCategoryValue.FB ? <Menu /> : <Product />}
          />
          <Route path={Router.menu} element={<Menu />} />
          <Route path={Router.productDetail} element={<ProductDetail />} />
          <Route path={Router.search} element={<SearchProduct />} />
          <Route path={Router.cartPayment} element={<CartPayment />} />
          <Route path={Router.checkoutResult} element={<CheckoutResult />} />
          <Route path={Router.payment.index} element={<PaymentInfomation />} />
          <Route path={Router.bankTransfer.index} element={<BankTransfer />} />
          <Route path={Router.bankTransferV2.index} element={<BankTransferV2 />} />
          <Route path={Router.branch.index} element={<Branch />} />
          <Route path={Router.productCategoryPage} element={<ProductCategoryPage />} />
          <Route path={Router.invoiceRequest.index} element={<InvoiceRequest />} />
          <Route path={Router.game} element={<PromogameGame />} />
        </>
      </Routes>
      <CustomAlert />
      {showBottomNav && <BottomNavigation />}
      {showBottomNav && <StickyButtons />}
      {isHomePage && home?.some((item) => item.type === keyComponent.Game2 && item.show) && (
        <GameBox />
      )}
    </>
  );
};

const AppContent = () => {
  const theme = useAppTheme();

  return (
    <ThemeProvider theme={theme}>
      <AppProvider>
        <App>
          <SnackbarProvider>
            <AppRouter>
              <Root />
              <ScrollToTop />
            </AppRouter>
          </SnackbarProvider>
          <Toaster />
        </App>
      </AppProvider>
    </ThemeProvider>
  );
};

const MyApp = () => {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <RecoilRoot>
          <AppContent />
        </RecoilRoot>
      </PersistGate>
    </Provider>
  );
};

export default MyApp;
