import React, { useEffect, useState } from "react";
import ImageGallery from "react-image-gallery";
import { IProductImage } from "../types/product";
import CustomVideo from "./CustomVideo";
import { COLORS } from "../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Icon } from "@/constants/Assets";
import LazyImage from "@/components/UI/LazyImage";

export interface IImage {
  original: string;
  thumbnail: string;
  type: string;
  alt: string;
}

interface ItemGalleryProps {
  images?: IProductImage[];
}

export const ItemGallery = ({ images }: ItemGalleryProps) => {
  const [loadingImages, setLoadingImages] = useState<boolean>(true);
  const [processedImages, setProcessedImages] = useState<IImage[]>([]);
  const [displayImages, setDisplayImages] = useState<IImage[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const appConfig = useConfigApp();

  useEffect(() => {
    return () => {
      setLoadingImages(true);
      setProcessedImages([]);
      setDisplayImages([]);
    };
  }, []);

  useEffect(() => {
    if (!images || !images.length) {
      return;
    }
    try {
      processImages(images).then((processedImages) => {
        setProcessedImages(processedImages);
      });
    } catch (e) {
      console.error(e);
    }
  }, [images]);

  const handleError = (e) => {
    e.target.src = appConfig?.shopLogo || appConfig?.shopLogo?.link || Icon.errorImage; // Đặt URL ảnh thay thế
  };

  useEffect(() => {
    if (processedImages.length === 0) {
      return;
    }
    try {
      setDisplayImages(
        processedImages.map((img) => ({
          ...img,
          renderItem: () => {
            if (img.type.includes("IMAGE")) {
              return (
                <LazyImage
                  src={img.original}
                  alt={img.alt}
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                  }}
                  aspectRatio="1"
                />
              );
            }
            if (img.type.includes("VIDEO")) {
              return <CustomVideo src={img.original} type={img.type} />;
            }
            return <></>;
          },
          renderThumbInner: () => {
            if (img.type.includes("VIDEO")) {
              return (
                <video
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "fill",
                    borderRadius: 5,
                  }}
                  controlsList="nodownload nofullscreen noremoteplayback"
                  preload="metadata"
                  disablePictureInPicture={true}
                  disableRemotePlayback={true}
                >
                  <source src={`${img.thumbnail}#t=0.1`} type={img.type} />
                </video>
              );
            }
            return (
              <img
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  aspectRatio: 1 / 1,
                  borderRadius: 5,
                }}
                role="presentation"
                src={img.thumbnail}
                alt={img.alt}
                onError={handleError}
              />
            );
          },
        }))
      );
    } catch (e) {
      console.error(e);
    } finally {
      setLoadingImages(false);
    }
  }, [processedImages]);

  const processImages = async (images: IProductImage[]) => {
    const newImages: IImage[] = images.map((img) => {
      let URLImage = img.link;
      return {
        original: URLImage,
        thumbnail: URLImage,
        type: img.type || "image/png",
        alt: img.link || "alt",
      };
    });

    return await Promise.all(
      newImages.map(async (image) => {
        return {
          ...image,
          thumbnail: await generateThumbnail(image),
        };
      })
    );
  };

  const generateThumbnail = async (image: IImage): Promise<string> => {
    let thumbnail = "";
    if (image.type.includes("IMAGE")) {
      thumbnail = image.original;
      // thumbnail = await generateSmallImage(image);
    }
    if (image.type.includes("video/")) {
      thumbnail = image.original;
      // thumbnail = await captureVideoThumbnail(image.original);
    }
    return thumbnail;
  };

  if (loadingImages) {
    return <div style={{ height: "40dvh" }}></div>;
  }
  const handleSlide = (currentIndex) => {
    setCurrentIndex(currentIndex);
    setTimeout(() => {
      const gallery = document.querySelector(".image-gallery-thumbnails-wrapper");
      const thumbs = gallery?.querySelectorAll(".image-gallery-thumbnail");

      if (!gallery || !thumbs || thumbs.length === 0) return;

      const thumb = thumbs[currentIndex];
      thumb.scrollIntoView({
        behavior: "smooth",
        inline: "center",
        block: "nearest",
      });
    }, 150);
  };

  return (
    <ImageGallery
      items={displayImages}
      onSlide={handleSlide}
      showPlayButton={false}
      showFullscreenButton={false}
      showNav={false}
      autoPlay={false}
      swipingTransitionDuration={200}
      thumbnailPosition="bottom"
      renderCustomControls={() => {
        if (!images || images.length === 0) {
          return <></>;
        }
        return (
          <div
            className="total-file-gallery"
            style={{
              position: "absolute",
              bottom: "5px",
              left: "5px",
              padding: "10px 7px",
              backgroundColor: "#F3F3F3",
              color: "#777777",
              fontWeight: 700,
              borderRadius: "10px",
              zIndex: 1,
              fontSize: 15,
            }}
          >
            {`${currentIndex + 1}/${images.length}`}
          </div>
        );
      }}
    />
  );
};
