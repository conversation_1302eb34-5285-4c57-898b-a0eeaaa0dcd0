import { useConfigApp } from "@/hooks/useConfigApp";
import { Box } from "@mui/material";
import React, { useState } from "react";

interface LazyImageProps {
  src: string;
  alt?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  aspectRatio?: string;
  customSkeleton?: React.ReactNode;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt = "",
  style = {},
  onClick,
  aspectRatio = "1",
  customSkeleton,
}) => {
  const [isImageLoading, setIsImageLoading] = useState(true);
  const appConfig = useConfigApp();

  const handleImageLoad = () => {
    setIsImageLoading(false);
  };

  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    e.currentTarget.src = appConfig?.shopLogo?.link || appConfig?.logo || "/images/logo.png";
  };

  return (
    <Box style={{ position: "relative", width: "100%", aspectRatio }}>
      {isImageLoading &&
        (customSkeleton ? (
          customSkeleton
        ) : (
          <Box style={styles.imagePlaceholder}>
            <Box style={styles.loadingSpinner} />
          </Box>
        ))}
      <img
        style={{
          ...styles.image,
          ...style,
          opacity: isImageLoading ? 0 : 1,
          transition: "opacity 0.2s ease-in-out",
        }}
        src={src || appConfig?.shopLogo?.link || appConfig?.shopLogo || "/images/logo.png"}
        alt={alt}
        onLoad={handleImageLoad}
        onError={handleError}
        onClick={onClick}
        loading="lazy"
      />
    </Box>
  );
};

const styles: Record<string, React.CSSProperties> = {
  imagePlaceholder: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "#f0f0f0",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 3,
  },
  loadingSpinner: {
    width: 24,
    height: 24,
    border: "2px solid #e0e0e0",
    borderTop: "2px solid #3498db",
    borderRadius: "50%",
    animation: "spin 1s linear infinite",
  },
  image: {
    width: "100%",
    height: "100%",
    objectFit: "cover",
    borderRadius: 3,
  },
};

// Thêm keyframes animation
const styleSheet = document.createElement("style");
styleSheet.textContent = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;
document.head.appendChild(styleSheet);

export default LazyImage;
