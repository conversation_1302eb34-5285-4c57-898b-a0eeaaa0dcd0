import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  CircularProgress,
  <PERSON>ack,
  Tab,
  Tabs,
  Typography,
  useTheme,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import store, { AppDispatch, RootState } from "../redux/store";
import {
  clearOrderSearchCondition,
  getOrderByOrderStatus,
  setOrderSearchCondition,
} from "../redux/slices/order/orderSlice";
import { OrderStatusText, OrderStatus, OrderStatusType } from "../constants/Const";
import { IOrder, IOrderSearchCondition } from "../types/order";
import { formatPrice } from "../utils/formatPrice";
import { useNavigate } from "../utils/component-util";
import { COLORS, commonStyle } from "../constants/themes";
import NoDataView from "../components/UI/NoDataView";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import { useConfigApp } from "@/hooks/useConfigApp";
import { request } from "@/utils/request";
import InfiniteScroll from "@/components/UI/InfiniteScroll";

interface getListOrderType {
  statusOrder: string;
  skip: number;
  limit: number;
}
const getListOrder = async (params: getListOrderType) => {
  const response: any = await request("get", "/api/user/orderuser/listorder", {
    statusOrder: params.statusOrder,
    skip: params.skip,
    limit: params.limit,
  });

  return response;
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: string;
  value: string | null;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

export default function Order() {
  const theme = useTheme();
  const { color, ...appConfig } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);

  const filteredObject: Record<string, string> = {};

  for (const key in OrderStatusType) {
    if (key !== "Verified" && key !== "Paid") {
      filteredObject[key] = OrderStatusType[key];
    }
  }

  const category = Object.values(filteredObject).map((status) => ({
    label: OrderStatusText[status],
    id: status,
  }));

  const navigator = useNavigate();
  const {
    orderList,
    isLoading,
    orderSearchCondition,
  }: {
    orderList: IOrder[];
    isLoading: boolean;
    orderSearchCondition: IOrderSearchCondition;
  } = useSelector((state: RootState) => state.order);
  const dispatch = useDispatch<AppDispatch>();
  const handleChange = (event: React.SyntheticEvent, orderId: string) => {
    dispatch(
      setOrderSearchCondition({
        ...orderSearchCondition,
        orderStatus: orderId,
      })
    );
    // setTabIndex(orderId);
    setOrderListLocal([]);
    setSkip(0);
    setHasMore(true);
    setLoading(true);
  };

  const [orderListLocal, setOrderListLocal] = useState<IOrder[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(10);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    if (orderSearchCondition && orderSearchCondition.orderStatus && user) {
      loadMoreData(orderSearchCondition.orderStatus, true);
    }
  }, [orderSearchCondition, user]);

  const loadMoreData = async (tabId, reset = false) => {
    setSkip((prevSkip) => {
      const newSkip = reset ? 0 : prevSkip + limit;
      getListOrder({
        statusOrder: tabId,
        skip: newSkip,
        limit,
      }).then((response) => {
        setOrderListLocal((prevList) => (reset ? response.data : [...prevList, ...response.data]));
        setHasMore(newSkip + limit < response.total);
        setLoading(false);
      });

      return newSkip; // Cập nhật state dựa trên giá trị cũ
    });
  };

  useEffect(() => {
    return () => {
      dispatch(clearOrderSearchCondition());
    };
  }, []);

  return (
    <FrameContainerFull title="Đơn mua">
      <Tabs
        className="order-history"
        value={orderSearchCondition.orderStatus}
        onChange={handleChange}
        variant="scrollable"
        scrollButtons={false}
        style={{ background: "white", padding: 16 }}
        TabIndicatorProps={{
          style: {
            backgroundColor: color.primary,
            border: 1,
            color: color.primary,
          },
        }}
      >
        {category.map((item, index) => (
          <Tab
            key={index}
            label={item.label}
            value={item.id}
            style={{
              ...styles.tabLabel,
              color: orderSearchCondition.orderStatus == item.id ? color.primary : "#9C9C9C",
              fontWeight: orderSearchCondition.orderStatus == item.id ? 700 : 400,
            }}
          />
        ))}
      </Tabs>
      {loading ? (
        <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
          <CircularProgress />
        </Stack>
      ) : (
        category.map((item, index) => (
          <CustomTabPanel
            key={index}
            value={orderSearchCondition?.orderStatus || ""}
            index={item.id}
          >
            <Stack gap={1} paddingTop={1} paddingInline={2}>
              {orderListLocal?.length > 0 ? (
                <InfiniteScroll
                  loader={
                    <Stack justifyContent={"center"} alignItems={"center"} padding={4}>
                      <CircularProgress />
                    </Stack>
                  }
                  className=""
                  fetchMore={() => loadMoreData(item.id)}
                  hasMore={hasMore}
                  endMessage={null}
                >
                  {orderListLocal.map((order) => {
                    const firstItemOrder = order.listItems[0];
                    const imageFirstItemOrder = firstItemOrder?.images[0]?.link;
                    const variantValues = [
                      firstItemOrder.variantValueOne,
                      firstItemOrder.variantValueTwo,
                      firstItemOrder.variantValueThree,
                    ].filter(Boolean); // Lọc ra những giá trị không null/undefined/empty string

                    const variantFirstItemOrder = variantValues.length
                      ? `${firstItemOrder.itemsName} (${variantValues.join(", ")})`
                      : firstItemOrder.itemsName;

                    const priceFirstItemOrder = formatPrice(firstItemOrder.price);
                    const countMoreItemOrder = order.listItems.length - 1;
                    return (
                      <Stack
                        key={order.orderId}
                        style={styles.itemContainer}
                        onClick={() => {
                          navigator(`/order/${order.orderId}`);
                        }}
                      >
                        <Box gap={1} sx={styles.contentContainer} key={firstItemOrder.itemsId}>
                          <img
                            style={styles.imageProduct}
                            src={imageFirstItemOrder}
                            onError={(e) => {
                              e.currentTarget.onerror = null;
                              e.currentTarget.src = appConfig?.shopLogo?.link || "";
                            }}
                          />
                          <Box
                            sx={{
                              width: "100%",
                              marginLeft: 2,
                              overflow: "hidden",
                            }}
                          >
                            <Box sx={{ width: "100%" }}>
                              <Typography
                                sx={{
                                  textAlign: "left",
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                  width: "100%",
                                  display: "-webkit-box",
                                  WebkitLineClamp: 1,
                                  WebkitBoxOrient: "vertical",
                                  ...styles.nameProduct,
                                }}
                              >
                                {firstItemOrder.itemsName}
                              </Typography>
                              <Stack
                                direction="row"
                                gap={1}
                                justifyContent="space-between"
                                marginTop={0.5}
                                sx={{ width: "100%" }}
                              >
                                <Typography
                                  sx={{
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    width: "80%",
                                    display: "-webkit-box",
                                    WebkitLineClamp: 1,
                                    WebkitBoxOrient: "vertical",
                                  }}
                                  style={{ color: COLORS.neutral4 }}
                                >
                                  {variantFirstItemOrder}
                                </Typography>
                                <Typography style={styles.itemPrice}>
                                  x{firstItemOrder.quantity}
                                </Typography>
                              </Stack>
                            </Box>
                            <Box gap={1} sx={{ marginLeft: "auto" }}>
                              <Box>
                                <Typography style={styles.itemPrice}>
                                  {priceFirstItemOrder}
                                </Typography>
                                <Typography style={styles.orderPriceText}>
                                  Tổng số tiền ({order.listItems.length} sản phẩm):{" "}
                                  <span
                                    style={{
                                      ...styles.orderPrice,
                                      color: color.primary,
                                    }}
                                  >
                                    {formatPrice(order.totalAfterTax)}
                                  </span>
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                        </Box>
                        <Stack
                          direction="row"
                          gap={2}
                          sx={{ ...styles.btnContainer, width: "100%" }}
                        >
                          <Button
                            style={{
                              color: "#fff",
                              borderRadius: 5,
                              background: color.primary,
                              flexGrow: 1,
                            }}
                            variant="contained"
                          >
                            Xem đơn hàng
                          </Button>
                          <Button
                            style={{
                              borderRadius: 5,
                              color: color.primary,
                              borderColor: color.primary,
                              flexGrow: 1,
                            }}
                            variant="outlined"
                          >
                            {OrderStatusText[order.statusOrder]}
                          </Button>
                        </Stack>
                      </Stack>
                    );
                  })}
                </InfiniteScroll>
              ) : (
                <NoDataView content="Không có đơn hàng nào" />
              )}
            </Stack>
          </CustomTabPanel>
        ))
      )}
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    display: "flex",
  },
  btnContainer: {
    justifyContent: "space-around",
    alignItems: "center",
    paddingTop: 2,
  },
  itemContainer: {
    background: "#fff",
    padding: 16,
    borderRadius: 5,
    marginBlock: 4,
  },
  itemTitle: {
    fontSize: 16,
    color: COLORS.neutral2,
  },
  appName: {
    ...commonStyle.headline16,
    color: COLORS.accent1,
    alignSelf: "flex-start",
  },
  itemTile: {
    color: COLORS.neutral5,
  },
  tabLabel: {
    fontSize: "16px",
    padding: 0,
    marginRight: "30px",
    borderRadius: 5,
    minHeight: 30,
    height: 30,
    minWidth: 0,
  },
  imageProduct: {
    borderRadius: 5,
    width: 80,
    height: 80,
    minWidth: 80,
    objectFit: "cover",
  },
  nameProduct: {
    color: "#252525",
    fontSize: 17,
    fontWeight: 500,
  },
  itemPrice: {
    color: "#848484",
    fontSize: 16,
    fontWeight: 400,
    textAlign: "end",
  },
  orderPriceText: {
    color: "#919191",
    fontSize: 13,
    fontWeight: 400,
    textAlign: "end",
  },
  orderPrice: {
    fontSize: 18,
    fontWeight: 500,
  },
};
