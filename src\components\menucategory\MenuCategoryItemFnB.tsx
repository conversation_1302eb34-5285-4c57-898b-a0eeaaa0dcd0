import { Box, Stack, Typography } from "@mui/material";
import React from "react";
import { COLOR, COLORS } from "@/constants/themes";
import { IMenu } from "@/types/menu";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Icon } from "@/constants/Assets";

export default function MenuCategoryItemFnB({
  item,
  style,
  shopInfo,
}: {
  item?: IMenu;
  style?: { radius?: number; fontSize?: number; fontWeight?: number };
  shopInfo?: { businessType: string };
}) {
  const appConfig = useConfigApp();
  const _item = item || ({} as IMenu);
  const handleError = (e) => {
    e.target.src = Icon.errorImage; // Đặt URL ảnh thay thế
  };
  let imageSrc = _item.image && _item.image.link ? _item.image.link : _item.icon;

  if (!imageSrc) imageSrc = Icon.errorImage;

  return (
    <Stack sx={styles.container}>
      <Box
        sx={{
          ...styles.iconBox,
          backgroundColor: appConfig.color.secondary,
        }}
        display={"flex"}
        alignItems={"center"}
        justifyContent={"center"}
      >
        <img
          src={imageSrc}
          style={{
            ...styles.iconStyle,
            borderRadius: style?.radius ? `${style.radius}px` : "5px",
          }}
          onError={handleError}
        />
      </Box>
      <Typography
        style={{
          ...styles.titleStyle,
          color: COLOR.text.color2,
          fontSize: style?.fontSize ? `${style.fontSize}px` : "12px",
          fontWeight: style?.fontWeight ? `${style.fontWeight}` : 700,
        }}
      >
        {_item.title}
      </Typography>
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    marginRight: 1.3,
  },
  iconBox: {
    backgroundColor: COLORS.second,
    borderRadius: "50%",
    width: "100%",
    aspectRatio: "1 / 1",
    alignContent: "center",
  },
  iconStyle: {
    width: "100%",
    height: "100%",
    objectFit: "cover",
    borderRadius: 5,
    border: "1 solid",
    borderColor: COLORS.primary,
  },
  titleStyle: {
    marginTop: 5,
    fontSize: 12,
    textAlign: "center",
  },
};
