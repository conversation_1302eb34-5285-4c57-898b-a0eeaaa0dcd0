import { <PERSON>, <PERSON><PERSON>, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { IProduct, IProductVariant } from "../../types/product";
import { useConfigApp } from "@/hooks/useConfigApp";

interface VariantSelectorProps {
  productVariants: IProductVariant[];
  // selected?: IProductVariant | null;
  onVariantChange: (variant: IProductVariant, product?: IProduct | null) => void;
  open?: boolean;
  setLoading?: any;
}
const ItemOptionSelector = ({
  productItemOptions,
  // selected,
  onItemOptionChange,
  open,
  setLoading,
}) => {
  const appConfig = useConfigApp();
  const [selectedItemOptions, setSelectedItemOptions] = useState<any[]>([]);

  // useEffect(() => {
  //   if (selected) {
  //     setSelectedVariant(selected);
  //   }
  // }, [selected]);
  useEffect(() => {
    if (!open) {
      setSelectedItemOptions([]);
    }
  }, [open]);

  //   const groupedVariants = groupVariantsByName(productVariants);

  const isOptionDisabled = (variantName: string, value: string): boolean => {
    return false;
  };

  const selectedItemOptionsId = selectedItemOptions.map((t) => t.itemOptionId);

  const handleItemOptionSelect = (itemOption, itemOptionGroup) => {
    const isMultiSelect = itemOptionGroup.isMultiSelect;
    const selectedItemOptionsId = selectedItemOptions.map((t) => t.itemOptionId);
    const isExist = selectedItemOptionsId?.includes(itemOption.itemOptionId);
    let newData = [...selectedItemOptions];
    //Bỏ chọn
    if (isExist) {
      //   if (!isRequire) {
      newData = newData.filter((item) => item.itemOptionId !== itemOption.itemOptionId);
      //   }
    } else {
      //Chọn một
      if (isMultiSelect) {
        newData = [...newData, itemOption];
      } else {
        // Lấy danh sách ID từ test2
        const idsToRemove = itemOptionGroup.itemOptions.map((t) => t).map((i) => i.itemOptionId);
        // Lọc test1 để loại bỏ các phần tử có trong idsToRemove
        newData = newData.filter((item) => !idsToRemove.includes(item.itemOptionId));
        newData = [...newData, itemOption];
      }
    }
    setSelectedItemOptions(newData);
    setLoading(false);
    onItemOptionChange(newData);
  };

  return (
    <Box paddingBlock={1}>
      {productItemOptions.map((itemOptionGroup, fIndex) => (
        <Stack key={itemOptionGroup.itemOptionGroupId} style={styles.variantContainer} gap={1}>
          <Box style={{ display: "flex", alignItems: "center", width: "100%" }}>
            <span
              style={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                maxWidth: "97%",
                display: "inline-block",
                verticalAlign: "middle",
              }}
            >
              {itemOptionGroup.name}
            </span>
            {itemOptionGroup.require && (
              <span
                style={{
                  color: "red",
                  marginLeft: 4,
                  fontSize: "inherit",
                  lineHeight: "inherit",
                  verticalAlign: "middle",
                }}
              >
                *
              </span>
            )}
          </Box>
          <Box>
            {itemOptionGroup.itemOptions.map((itemOption, index) => (
              <Button
                key={index}
                // disabled={isOptionDisabled(itemOption, itemOption)}
                style={{
                  ...styles.variantBtn,
                  background: selectedItemOptionsId.includes(itemOption.itemOptionId)
                    ? appConfig.color.primary
                    : "#f0f0f0",
                  maxWidth: "100%",
                }}
                onClick={() => handleItemOptionSelect(itemOption, itemOptionGroup)}
              >
                <Typography
                  sx={{
                    color: selectedItemOptionsId.includes(itemOption.itemOptionId)
                      ? "#ffffff"
                      : appConfig.color.primary,
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    maxWidth: "100%",
                    display: "block",
                  }}
                >
                  {itemOption.name}
                </Typography>
              </Button>
            ))}
          </Box>
        </Stack>
      ))}
    </Box>
  );
};

export default ItemOptionSelector;

const styles: Record<string, React.CSSProperties> = {
  variantBtn: {
    paddingBlock: 4,
    marginRight: "5px",
    marginBottom: "5px",
  },
  variantContainer: {
    marginBlock: 10,
  },
  variantName: {
    color: "#878787",
    fontSize: 14,
    fontWeight: 500,
  },
  imageStyle: {
    width: 30,
    height: 30,
  },
  optionContainer: {
    display: "flex",
    alignItems: "center",
    gap: 4,
  },
  optionnName: {
    color: "#6C6C6C",
    fontSize: 13,
    fontWeight: 400,
  },
};
