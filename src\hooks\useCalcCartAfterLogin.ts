import { getCart, getCartInfo } from "@/redux/slices/cart/cartSlice";
import { AppDispatch } from "@/redux/store";
import { useDispatch } from "react-redux";
import { useCart } from "./useCart";
import { ICart } from "@/types/cart";
import _ from "lodash";
export const useCalcCartAfterLogin = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { cartPayment, setCartAndSave } = useCart();

  const fetchUserAndCart = async () => {
    const cartUser: ICart = await dispatch(getCartInfo()).unwrap();
    if (!cartPayment.userId && cartPayment.listItems.length > 0) {
      const newItems = mergeCartItems(cartUser.listItems, cartPayment.listItems);
      await setCartAndSave({
        ...cartUser,
        listItems: newItems,
        typePay: cartPayment.typePay,
      });
    } else {
      await dispatch(getCart());
    }
  };

  return fetchUserAndCart;
};

function mergeCartItems(cartItems, newItems) {
  let updatedCart = [...cartItems]; // Sao chép mảng cũ để không làm thay đổi dữ liệu gốc

  newItems.forEach((product) => {
    const existingItemIndex = updatedCart.findIndex(
      (item) =>
        _.isEqual(new Set(item.extraOptions), new Set(product.extraOptions)) &&
        item.note === product.note &&
        item.itemsCode === product.itemsCode &&
        item.variantNameOne === product.variantNameOne &&
        item.variantValueOne === product.variantValueOne &&
        item.variantNameTwo === product.variantNameTwo &&
        item.variantValueTwo === product.variantValueTwo &&
        item.variantNameThree === product.variantNameThree &&
        item.variantValueThree === product.variantValueThree
    );

    if (existingItemIndex !== -1) {
      // Nếu đã có trong giỏ, thay thế số lượng
      updatedCart[existingItemIndex] = { ...product };
    } else {
      // Nếu chưa có, thêm mới vào giỏ
      updatedCart.push({ ...product });
    }
  });

  return updatedCart;
}
