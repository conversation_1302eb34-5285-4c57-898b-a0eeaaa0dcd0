import React, { useEffect, useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Box,
  Switch,
  FormControlLabel,
  FormHelperText,
} from "@mui/material";

import { Controller, useForm } from "react-hook-form";
import store, { AppDispatch, RootState } from "../../redux/store";
import {
  createAddress,
  deleteAddressList,
  getAddressList,
  getDistrict,
  getProvince,
  getWard,
  setCurrentAddress,
  updateAddress,
} from "../../redux/slices/address/addressSlice";
import { useDispatch, useSelector } from "react-redux";
import { COLORS, commonStyle } from "../../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { IAddress, IDistrict, IProvince, IWard } from "@/types/address";
import { showToast } from "@/utils/common";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { c } from "vite/dist/node/types.d-aGj9QkWt";
import toast from "react-hot-toast";
import * as yup from "yup";
import { Phone84Regex, PhoneRegex } from "@/constants/Const";
import { yupResolver } from "@hookform/resolvers/yup";

const addressSchema = yup.object().shape({
  fullName: yup.string().trim().required("Vui lòng nhập họ và tên"),
  phoneNumber: yup
    .string()
    .required("Vui lòng nhập số điện thoại")

    .matches(Phone84Regex, "Định dạng số điện thoại không đúng"),
  provinceId: yup.string().required("Vui lòng chọn tỉnh"),
  wardId: yup.string().required("Vui lòng chọn xã/phường"),
  districtId: yup.string().required("Vui lòng chọn quận/huyện"),
  isDefault: yup.boolean(),
  address: yup
    .string()
    .trim()
    .min(4, "Địa chỉ phải có ít nhất 4 ký tự")
    .required("Vui lòng nhập địa chỉ"),
});

const defaultValues: IAddress = {
  id: "",
  shippingAddressId: "",
  fullName: "",
  phoneNumber: "",
  provinceId: "",
  provinceName: "",
  districtId: "",
  districtName: "",
  wardId: "",
  wardName: "",
  address: "",
  isDefault: false,
};

interface PopupAddAddressProps {
  mode: "add" | "edit";
  address?: IAddress;
  onClose: () => void;
}
export default function PopupAddAddress({ mode, address, onClose }: PopupAddAddressProps) {
  const { color } = useConfigApp();
  const dispatch = useDispatch<AppDispatch>();
  const { province, district, ward } = useSelector((state: RootState) => state.address);
  const [wardData, setWardData] = React.useState<IWard[]>([]);
  const [districtData, setDistrictData] = React.useState<IDistrict[]>([]);
  const { showAlert } = useAlert();

  const {
    control,
    handleSubmit,
    register,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: address || defaultValues,
    resolver: yupResolver(addressSchema),
  });

  const { currentAddress } = useSelector((state: RootState) => state.address);

  useEffect(() => {
    setDistrictData([]);
    setWardData([]);
  }, [watch("provinceId")]);

  useEffect(() => {
    setWardData([]);
  }, [watch("districtId")]);

  React.useEffect(() => {
    if (mode === "edit" && address) {
      setDistrictData(district);
      setWardData(ward);
      reset(address);
    }
  }, [mode, address, reset]);

  const onSubmit = async (data: IAddress) => {
    if (mode === "edit" && address) {
      data.id = address.id;
      const res = await store.dispatch(updateAddress(data));
      if (res) {
        showToast({
          content: "Cập nhật địa chỉ thành công",
          type: "success",
        });
        await store.dispatch(getAddressList());
        if (res.payload.id == currentAddress?.id) {
          dispatch(setCurrentAddress(res.payload));
        }
        onClose();
      }
    } else {
      const res = await store.dispatch(createAddress(data));
      if (res) {
        showToast({
          content: "Thêm địa chỉ thành công",
          type: "success",
        });
        await store.dispatch(getAddressList());
        onClose();
      }
    }
  };

  const onDelete = async () => {
    showAlert({
      title: "Xác nhận",
      content: "Bạn có chắc muốn xóa địa chỉ này không?",
      contentStyle: {
        color: "#1D1D1D",
      },
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "Xác nhận",
          action: async () => {
            if (address?.isDefault === true) {
              showToast({
                content: "Bạn không thể xoá địa chỉ mặc định",
                type: "error",
              });
            } else {
              if (address?.shippingAddressId) {
                const res = await store.dispatch(deleteAddressList(address?.shippingAddressId));
                if (res) {
                  showToast({
                    content: "Xóa địa chỉ thành công",
                    type: "success",
                  });
                  await store.dispatch(getAddressList());
                  onClose();
                }
              }
            }
          },
        },
      ],
    });
  };

  return (
    <Dialog
      fullWidth
      open
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: "12px",
          paddingBottom: "20px",
        },
      }}
    >
      <form
        onSubmit={handleSubmit(onSubmit, (errors) => console.log("❌ Validation errors:", errors))}
      >
        <DialogTitle
          id="alert-dialog-title"
          style={{
            ...commonStyle.headline16,
            color: color.primary,
            textAlign: "center",
          }}
        >
          {mode === "edit" ? "Sửa địa chỉ" : "Thêm địa chỉ"}
        </DialogTitle>
        <DialogContent>
          <Stack gap={3}>
            <Controller
              name="fullName"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  {...register("fullName")}
                  fullWidth
                  required
                  id="standard-required"
                  label="Họ và tên"
                  variant="standard"
                  error={!!errors.fullName}
                  helperText={errors.fullName?.message}
                />
              )}
            />
            <Controller
              name="phoneNumber"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  required
                  label="Số điện thoại"
                  variant="standard"
                  error={!!errors.phoneNumber}
                  helperText={errors.phoneNumber?.message}
                />
              )}
            />

            <Stack direction={"row"} gap={2}>
              <Controller
                name="provinceId"
                control={control}
                render={({ field }) => (
                  <FormControl variant="standard" fullWidth error={Boolean(errors.provinceId)}>
                    <InputLabel id="province-label">Tỉnh</InputLabel>
                    <Select
                      {...register("provinceId")}
                      value={watch("provinceId") || ""}
                      fullWidth
                      required
                      labelId="province-label"
                      id="province"
                      label="Tỉnh"
                      onFocus={() => {
                        if (!province || province.length == 0) {
                          dispatch(getProvince());
                        }
                      }}
                    >
                      {province?.map((item: IProvince, index) => (
                        <MenuItem key={`province-${index}`} value={item.provinceID}>
                          {item.provinceName}
                        </MenuItem>
                      ))}
                    </Select>
                    {/* Hiển thị lỗi nếu có */}
                    {errors.provinceId && (
                      <FormHelperText>{errors.provinceId.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />

              <Controller
                name="districtId"
                control={control}
                render={({ field }) => (
                  <FormControl variant="standard" fullWidth>
                    <InputLabel id="district-label">Quận (Huyện)</InputLabel>
                    <Select
                      {...field}
                      {...register("districtId")}
                      value={watch("districtId") || ""}
                      fullWidth
                      required
                      labelId="district-label"
                      id="district"
                      label="Quận (Huyện)"
                      onFocus={async () => {
                        if (!watch("provinceId")) return;
                        const res: any = await dispatch(getDistrict(watch("provinceId")));
                        if (Array.isArray(res.payload) && res.payload.length > 0) {
                          setDistrictData(res.payload);
                        }
                      }}
                    >
                      {districtData?.map((item: IDistrict, index) => (
                        <MenuItem key={`district-${index}`} value={item.districtID}>
                          {item.districtName}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Stack>
            <Controller
              name="wardId"
              control={control}
              render={({ field }) => (
                <FormControl variant="standard" fullWidth>
                  <InputLabel id="ward-label">Xã (Phường)</InputLabel>
                  <Select
                    {...field}
                    {...register("wardId")}
                    value={watch("wardId") || ""}
                    fullWidth
                    required
                    labelId="ward-label"
                    id="ward"
                    label="Xã (Phường)"
                    onFocus={async () => {
                      if (!watch("districtId")) return;
                      const res: any = await dispatch(getWard(watch("districtId")));
                      if (Array.isArray(res.payload) && res.payload.length > 0) {
                        setWardData(res.payload);
                      }
                    }}
                  >
                    {wardData?.map((item: IWard, index) => (
                      <MenuItem key={`ward-${index}`} value={item.wardID}>
                        {item.wardName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />

            <Controller
              name="address"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  required
                  id="standard-required"
                  label="Số nhà, đường"
                  defaultValue=""
                  variant="standard"
                  error={!!errors.address}
                  helperText={errors.address?.message}
                />
              )}
            />
            <Controller
              name="isDefault"
              control={control}
              render={({ field }) => (
                <div
                  onClick={() => {
                    if (field.value === true && address?.isDefault === true) {
                      showToast({
                        content:
                          "Để huỷ địa chỉ mặc định này, vui lòng chọn một địa chỉ khác làm mặc định",
                        type: "error",
                      });
                    }
                  }}
                  style={{ display: "inline-block" }}
                >
                  <FormControlLabel
                    control={
                      <Switch
                        {...field}
                        checked={field.value}
                        disabled={field.value === true && address?.isDefault === true}
                      />
                    }
                    label="Đặt làm mặc định"
                  />
                </div>
              )}
            />
          </Stack>
        </DialogContent>
        <DialogActions sx={styles.bottomBtnContainer}>
          <Button
            style={{
              ...styles.bottomBtn,
              background: "#e7e7e7",
              color: "#a8a8a8",
              flexGrow: 1,
            }}
            onClick={onClose}
          >
            Hủy bỏ
          </Button>
          <Button
            style={{
              ...styles.bottomBtn,
              background: color.primary,
              color: "#fff",
              flexGrow: 1,
            }}
            type="submit"
          >
            {mode === "edit" ? "Lưu thay đổi" : "Thêm địa chỉ"}
          </Button>
        </DialogActions>
        {mode === "edit" && (
          <DialogActions sx={{ paddingInline: "20px" }}>
            <Button
              style={{
                ...styles.bottomBtn,
                background: "#f44336",
                color: "#fff",
                flexGrow: 2,
              }}
              onClick={() => onDelete()}
            >
              Xóa
            </Button>
          </DialogActions>
        )}
      </form>
    </Dialog>
  );
}

const styles: Record<string, React.CSSProperties> = {
  bottomBtnContainer: {
    justifyContent: "space-around",
    gap: 2,
    paddingInline: "20px",
  },
  bottomBtn: {
    margin: 0,
    height: "100%",
    width: 120,
    display: "flex",
    gap: "8px",
    fontSize: 12,
  },
};
