import VoucherAction, { BtnCategory } from "@/components/voucher/ActionVoucher";
import { SelectedVoucher } from "@/components/voucher/CheckVoucherType";
import { ButtonHandleVoucherType, ReleaseType, VoucherType } from "@/constants/Const";
import { Router } from "@/constants/Route";
import { COLOR, COLORS } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { collectVoucher, VoucherDto } from "@/redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { IDataCreateVoucherUser } from "@/types/voucher";
import { formatPrice } from "@/utils/formatPrice";
import { Box, Button, Stack, Typography } from "@mui/material";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

export enum VoucherItemCategory {
  MYLIST,
  MYDETAIL,
  LIST,
  DETAIL,
  SELECT,
  DIALOG,
}

export default function VourcherItemCollectRetail({
  item,
  category,
  isShowMatchPoint,
  onSelectVoucher,
  onNavigateToDetail,
  onApplyClick,
  isChecked,
  myVoucherList,
  isDisabled,
}: {
  item: VoucherDto;
  category?: VoucherItemCategory;
  onSelectVoucher?: (item: VoucherDto) => void;
  isShowMatchPoint?: boolean;
  onNavigateToDetail?: () => void;
  isChecked?: boolean;
  onApplyClick?: (item) => void;
  myVoucherList?: VoucherDto[];
  isDisabled?: boolean;
}) {
  const { color, bgColor, textColor } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const appConfig = useConfigApp();

  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const [isItemChecked, setItemChecked] = useState(isChecked || false);

  useEffect(() => {
    setItemChecked(isChecked || false);
  }, [isChecked]);

  const onItemAction = (item) => {
    if (onNavigateToDetail) {
      onNavigateToDetail();
    } else if (onSelectVoucher) {
      onSelectVoucher(item);
    }
  };

  const isHaveInMyVoucher = myVoucherList?.find((voucher) => voucher.voucherId === item.voucherId);

  let btnAction: BtnCategory = BtnCategory.NONE;

  let btnActionLabel = "";
  if (category === VoucherItemCategory.LIST) {
    btnActionLabel = item.exchangePoints
      ? ButtonHandleVoucherType.Exchange
      : ButtonHandleVoucherType.Save;
    btnAction = BtnCategory.BTN;
  } else if (category === VoucherItemCategory.MYLIST) {
    btnActionLabel = ButtonHandleVoucherType.Use;
    btnAction = BtnCategory.BTN;
  } else if (category === VoucherItemCategory.SELECT) {
    btnAction = BtnCategory.RADIO;
  }

  const handleSaveVoucher = async () => {
    try {
      if (!user?.userId) return;

      const res = await dispatch(collectVoucher(item?.voucherDetails?.[0].voucherCode ?? ""));
      if (res?.payload?.status === 400) {
        toast.error(res?.payload?.detail);
      } else {
        toast.success("Lưu voucher thành công");
        navigate(Router.voucher.index);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  const handleExchangeVoucher = async () => {
    if (item.releaseType === ReleaseType.Free) return;
    if (!user?.point) {
      toast.error("Người dùng chưa có điểm khuyến mại");
      return;
    }
    if (user.point < item.exchangePoints) {
      toast.error("Điểm của người dùng không đủ");
      return;
    }

    const voucherData: IDataCreateVoucherUser = {
      voucherId: item?.voucherId,
      shopId: item?.shopId,
    };

    const res = await dispatch(collectVoucher(item?.voucherDetails?.[0].voucherCode ?? ""));
    if (res?.payload?.status === 400) {
      toast.error(res?.payload?.detail);
    } else {
      toast.success("Đổi voucher thành công");
      navigate(Router.voucher.index);
    }
  };

  const onClickVoucher = (event: React.MouseEvent) => {
    event.stopPropagation();

    switch (btnActionLabel) {
      case ButtonHandleVoucherType.Save:
        handleSaveVoucher();
        break;
      case ButtonHandleVoucherType.Exchange:
        handleExchangeVoucher();
        break;
      default:
        onSelectVoucher?.(item);
    }
  };
  const onNavigate = () => {
    navigate(
      `${Router.voucher.detail.replace(":code", item?.voucherDetails?.[0].voucherCode ?? "")}`,
      {
        state: { voucher: item },
      }
    );
  };

  return (
    <Box
      className="wrap"
      key={item.voucherId}
      onClick={() => {
        if (isDisabled) {
          onNavigate();
        } else {
          onItemAction(item);
        }
      }}
      bgcolor={COLOR.bg.primary}
      sx={{
        opacity: isDisabled ? 0.5 : 1,
        cursor: isDisabled ? "not-allowed" : "pointer",
      }}
    >
      <Box
        className="coupon"
        style={{ border: `1px soild #000000` }}
        bgcolor={COLORS.bgColor.fourth}
      >
        <Stack className="coupon-left" sx={styles.leftContainer} p={1}>
          <Stack
            className="content"
            sx={{ ...styles.leftContent }}
            borderRadius="2px"
          >
            <Box
              p={1}
              borderRadius={"50%"}
              bgcolor={color.secondary}
              width={55}
              height={55}
              display={"flex"}
              justifyContent={"center"}
              alignItems={"center"}
            >
              <img
                width={100}
                height={100}
                src={item.image?.link || appConfig?.shopLogo}
                style={{ objectFit: "cover" }}
              />
            </Box>
          </Stack>
        </Stack>
        <Box
          sx={{
            width: "1px",
            backgroundImage: "linear-gradient(to bottom, #e0e0e0 50%, transparent 50%)",
            backgroundSize: "1px 8px",
            marginY: 2,
          }}
        />
        <Stack className="coupon-con" direction="row" px={1}>
          <Stack sx={styles.rightTop} direction="column" py={1}>
            <Typography
              position={"absolute"}
              top={0}
              right={10}
              fontSize={14}
              fontWeight={700}
              lineHeight={"24px"}
              color={COLOR.text.white}
              bgcolor={color.accent}
              width={40}
              height={24}
              align="center"
              borderRadius={"2px 2px 0 0"}
            >
              {btnAction === BtnCategory.RADIO
                ? `x${item?.quantity}`
                : btnActionLabel !== ButtonHandleVoucherType.Use
                ? `x${item?.quantity}`
                : `x${item.quantity}`}
            </Typography>
            <Box>
              <Button
                variant="outlined"
                sx={styles.btnTopLeft}
                style={{
                  borderColor: color.primary,
                  color: color.primary,
                  borderRadius: 2,
                  fontSize: 12,
                  // backgroundColor: color.accent,
                }}
              >
                {item?.voucherType === VoucherType.Transport ? "Mã freeship" : "Mã giảm giá"}
              </Button>
              {isShowMatchPoint && (
                <Button
                  variant="outlined"
                  sx={styles.btnTopLeft}
                  style={{
                    borderColor: color.primary,
                    color: color.primary,
                    marginLeft: 3,
                    borderRadius: 2,
                    fontSize: 12,
                  }}
                >
                  {item.exchangePoints} Star
                </Button>
              )}
            </Box>
            {item && item && SelectedVoucher(item)}
            {/* <Typography color={COLOR.text.voucher_code} fontSize={12} fontWeight={500} mt={0.5}>
              #{item?.voucherCode}
            </Typography> */}
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="space-between"
              width="100%"
              sx={{ marginTop: 0.5 }}
            >
              <Stack>
                <Typography color={COLOR.text.voucher_info} fontSize={11}>
                  {item?.minOrder ? "Đơn tối thiểu: " + formatPrice(item?.minOrder) : ""}
                </Typography>
                <Typography color={COLOR.text.voucher_info} fontSize={11}>
                  HSD:{" "}
                  {item?.startDate
                    ? `${dayjs(item?.startDate).format("DD/MM/YYYY")} - ${dayjs(
                        item?.endDate
                      ).format("DD/MM/YYYY")}`
                    : "Không giới hạn"}
                </Typography>
              </Stack>
              <Box>
                <VoucherAction
                  item={item}
                  category={btnAction}
                  value={btnActionLabel}
                  isChecked={isItemChecked}
                  isMyVoucher={isHaveInMyVoucher ? true : false}
                  eventAction={onClickVoucher}
                  isDisabled={isDisabled}
                />
              </Box>
            </Stack>
          </Stack>
        </Stack>
      </Box>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  leftContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  leftContent: {
    textAlign: "center",
    justifyContent: "center",
    alignItems: "center",
    color: COLORS.white,
    gap: 1,
    fontSize: 10,
    height: "100%",
    width: "100%",
  },
  zaloText: {
    width: 50,
    height: 50,
    background: COLORS.white,
    display: "flex",
    alignItems: "center",
    borderRadius: "50%",
    justifyContent: "center",
    margin: "0px auto",
    fontSize: 14,
    fontWeight: 700,
  },
  rightContainer: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    fontSize: 12,
  },
  rightTop: {
    width: "100%",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  typeText: {
    padding: "4px 8px",
    background: COLORS.primary2,
    textAlign: "center",
    borderRadius: 4,
    fontSize: 12,
    marginBottom: 5,
  },
  btnTopLeft: {
    fontSize: 11,
    paddingInline: 1,
    paddingBlock: 0.3,
    fontWeight: 400,
    marginBottom: 0.5,
  },
};
