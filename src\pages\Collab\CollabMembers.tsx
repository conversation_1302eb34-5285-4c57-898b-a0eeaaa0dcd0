import React, { useEffect, useState } from "react";
import FrameContainer from "../../components/layout/Container";
import {
  Stack,
  FormControl,
  TextField,
  InputAdornment,
  CircularProgress,
  Button,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import MemberItem from "./components/MemberItem";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { getMember, IFilterTeamData } from "../../redux/slices/team/team";
import NoDataView from "../../components/UI/NoDataView";
import { COLORS } from "@/constants/themes";
import { SearchNormal } from "@/constants/IconSvg";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useLocation } from "react-router-dom";

export default function CollabMembers() {
  const { color } = useConfigApp();
  const [keyword, setKeyword] = useState("");
  const { user } = useSelector((state: RootState) => state.auth);
  const { list, isLoading } = useSelector((state: RootState) => state.team);
  const location = useLocation();
  const { type } = location.state || {};
  const [filterData, setFilterData] = useState<IFilterTeamData>({
    search: "",
    type: type ?? undefined,
  });
  const dispatch = useDispatch<AppDispatch>();

  const [showClearIcon, setShowClearIcon] = useState("none");

  const handleChangeSearch = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setKeyword(event.target.value);
    setFilterData({ ...filterData, search: event.target.value });
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
  };

  const handleClick = (): void => {
    setKeyword("");
    setFilterData({ ...filterData, search: "" });
  };

  useEffect(() => {
    dispatch(getMember(filterData));
  }, [filterData]);

  // const members = list?.filter((e) => e.fParent == user?.id);

  return (
    <FrameContainer title="Danh sách thành viên">
      <Stack sx={styles.contentContainer}>
        <Stack
          direction={"row"}
          justifyContent={"space-between"}
          gap={1.5}
          style={{
            marginBottom: "20px",
          }}
        >
          <Stack
            style={{
              width: "100%",
            }}
          >
            <FormControl>
              <TextField
                placeholder="Nhập tên hoặc ID thành viên"
                style={{
                  width: "100%",
                  backgroundColor: COLORS.white,
                  borderRadius: "50px",
                  height: "50px",
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "50px",
                    border: 0,
                    height: "50px",
                  },
                  "& .MuiInputBase-input": {
                    color: COLORS.neutral1,
                  },
                }}
                size="small"
                variant="outlined"
                value={keyword}
                onChange={handleChangeSearch}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end" onClick={handleClick}>
                      <SearchNormal />
                    </InputAdornment>
                  ),
                }}
              />
            </FormControl>
          </Stack>
          <Stack
            style={{
              minWidth: "120px",
            }}
          >
            <Button
              style={{
                ...styles.btnContainer,
                background: color.primary,
              }}
            >
              Tất cả
            </Button>
          </Stack>
        </Stack>
        {isLoading ? (
          <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
            <CircularProgress />
          </Stack>
        ) : (
          <Stack>
            {list?.length > 0 ? (
              list.map((item, index) => <MemberItem item={item} key={item.userId} />)
            ) : (
              <NoDataView content="Đội nhóm của bạn chưa có thành viên nào" />
            )}
          </Stack>
        )}
      </Stack>
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    marginTop: 1.25,
    marginBottom: 4,
  },
  btnContainer: {
    fontSize: "16px",
    fontWeight: 500,
    color: COLORS.white,
    padding: "10px 20px",
    borderRadius: "50px",
  },
};
