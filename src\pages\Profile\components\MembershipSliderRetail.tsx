import LookUpQrIcon from "@/components/icon/LookUpQrIcon";
import StarBanner from "@/components/icon/StarBanner";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getDetailVoucherByCode, searchByPointsCode } from "@/redux/slices/voucher/voucherSlice";
import { showToast } from "@/utils/common";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { Box, Button, ButtonBase, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { scanQRCode } from "zmp-sdk/apis";
import { COLORS } from "../../../constants/themes";
import { AppDispatch, RootState } from "../../../redux/store";

export default function MembershipSliderRetail() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { user } = useSelector((state: RootState) => state.auth);
  const { color, ...appConfig } = useConfigApp();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { shopId } = useSelector((state: RootState) => state.appInfo);

  const data =
    user?.membershipLevel || (user?.point && user?.point > 0)
      ? [
          {
            title: user?.membershipLevel?.levelName ?? "Chưa có xếp hạng",
            points: user?.point,
            logo: user?.membershipLevel?.logo,
          },
        ]
      : [];

  useEffect(() => {
    const slickList = document.querySelector(".slick-list") as HTMLElement;
    if (!slickList) return;
    if (currentIndex === 0) {
      slickList.style.paddingLeft = "0";
    } else if (currentIndex === data.length - 1) {
      slickList.style.paddingRight = "0";
    } else {
      slickList.style.padding = "0 20px";
    }
  }, [currentIndex, data]);

  return (
    <Stack>
      {data.map((item, index) => (
        <Stack key={index}>
          <Box
            sx={{
              position: "relative",
              borderRadius: "16px",
              overflow: "hidden",
              height: "130px",
              mb: "20px",
            }}
          >
            <StarBanner fill={currentIndex === index ? color.primary : COLORS.neutral11} />
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                px: 3,
                pt: 2.5,
                pb: 2,
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                color: COLORS.white,
                zIndex: 1,
              }}
            >
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                sx={{ width: "100%" }}
              >
                <ButtonBase
                  sx={{
                    p: 0,
                    background: "none",
                    borderRadius: 2,
                    display: "flex",
                    alignItems: "flex-start",
                    minWidth: 0,
                  }}
                  onClick={() => navigate("/profile/membership-detail")}
                >
                  <Stack direction="row" alignItems="flex-start" gap={1} sx={{ minWidth: 90 }}>
                    <Stack>
                      <Stack direction="row" alignItems="center">
                        <Typography
                          sx={{
                            fontSize: 32,
                            fontWeight: 700,
                            lineHeight: 1,
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          {item.points}
                        </Typography>
                        <ChevronRightIcon
                          sx={{
                            fontSize: 28,
                            color: "#fff",
                            ml: 0.5,
                            cursor: "pointer",
                          }}
                        />
                      </Stack>
                      <Typography
                        sx={{
                          fontSize: 18,
                          fontWeight: 600,
                          letterSpacing: 0.2,
                          mt: "2px",
                          lineHeight: 1.2,
                          pr: 8,
                        }}
                      >
                        Star
                      </Typography>
                    </Stack>
                  </Stack>
                </ButtonBase>
                <Button
                  variant="contained"
                  sx={{
                    background: "#fff",
                    color: color.primary,
                    borderRadius: 99,
                    fontWeight: 600,
                    fontSize: 15,
                    px: 2.5,
                    py: 0.5,
                    minWidth: 0,
                    boxShadow: "none",
                    height: 45,
                    display: "flex",
                    alignItems: "center",
                    whiteSpace: "nowrap",
                    "&:hover": { background: "#f5f5f5" },
                  }}
                  startIcon={
                    <LookUpQrIcon
                      fill={currentIndex === index ? color.primary : COLORS.neutral11}
                      style={{ width: 25, height: 25 }}
                    />
                  }
                  onClick={async () => {
                    try {
                      const { content } = await scanQRCode({});
                      // --- Begin enhanced QR scan logic ---
                      const url = new URL(content);
                      const isReferLink =
                        url.searchParams.get("targetType") === "REFER" &&
                        url.searchParams.has("refCode");
                      const refCode = url.searchParams.get("refCode") || undefined;

                      if (isReferLink) {
                        if (!refCode || refCode === "undefined") {
                          navigate("/refer");
                          return;
                        }
                        if (!user) {
                          navigate("/refer");
                          return;
                        }
                        navigate(`/refer?refCode=${refCode}`);
                        return;
                      }

                      const pointsCode = (() => {
                        try {
                          const url = new URL(content);
                          return url.searchParams.get("promo-code");
                        } catch (e) {
                          return content;
                        }
                      })();

                      if (pointsCode) {
                        const result = await dispatch(getDetailVoucherByCode(pointsCode)).unwrap();
                        if (result && result.result.data) {
                          navigate(
                            `/voucher/${result.result.data?.voucherDetails?.[0]?.voucherCode}`
                          );
                        } else {
                          showToast({ content: "Không tìm thấy mã voucher", type: "error" });
                        }
                      }
                    } catch (err) {
                      showToast({ content: "Quét mã QR thất bại hoặc bị huỷ", type: "error" });
                    }
                  }}
                >
                  Quét mã QR
                </Button>
              </Stack>
              {/* Thông báo nâng hạng */}
              <div style={{ position: "relative", zIndex: 2 }}>
                {/* {item.points >= 1 ? (
                  <Typography
                    sx={{ fontSize: 13, color: "#fff", textAlign: "center", lineHeight: 1 }}
                  >
                    Chi tiêu thêm 1,540,333 vnđ để đạt hạng Thành viên Kim Cương và mở khóa nhiều
                    đặc quyền hấp dẫn!
                  </Typography>
                ) : ( */}
                <Typography
                  sx={{ fontSize: 13, color: "#fff", textAlign: "center", lineHeight: 1 }}
                >
                  Hãy tích điểm để nâng hạng và nhận nhiều ưu đãi hơn!
                </Typography>
                {/* )} */}
              </div>
            </Box>
          </Box>
        </Stack>
      ))}
    </Stack>
  );
}
