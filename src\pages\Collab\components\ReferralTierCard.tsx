import { Box, Button, Stack, Typography } from "@mui/material";
import React from "react";
import { Export } from "../../../constants/IconSvg";
import { useConfigApp } from "@/hooks/useConfigApp";
import { COLORS, commonStyle } from "@/constants/themes";

interface ReferralTierCardProps {
  title: React.ReactNode;
  subTitle: React.ReactNode;
  onShare: () => void;
}

const ReferralTierCard = ({ title, subTitle, onShare }: ReferralTierCardProps) => {
  const { color } = useConfigApp();
  return (
    <Stack
      direction={"row"}
      justifyContent={"space-between"}
      alignItems={"center"}
      gap={2.5}
      style={styles.collabDetail}
    >
      <Stack>
        {title}
        {subTitle}
      </Stack>
      <Button
        sx={{
          minWidth: "36px",
          height: "36px",
          borderRadius: "50%",
          background: "rgba(23, 70, 162, 0.1)",
          padding: 0,
        }}
        onClick={onShare}
      >
        <Export width={16} height={16} />
      </Button>
    </Stack>
  );
};

export default ReferralTierCard;

const styles: Record<string, React.CSSProperties> = {
  collabDetail: {
    background: COLORS.white,
    borderRadius: "12px",
    padding: "16px",
    marginTop: "12px",
    boxShadow: "0 2px 12px rgba(0,0,0,0.04)",
  },
};
