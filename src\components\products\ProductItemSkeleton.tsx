import React from "react";
import { Box, Skeleton } from "@mui/material";
import { COLOR } from "@/constants/themes";

const ProductItemSkeleton = () => (
  <Box
    style={{
      backgroundColor: COLOR.bg.product_item,
      borderRadius: 5,
      position: "relative",
      display: "flex",
      flexDirection: "column",
      width: "100%",
    }}
  >
    <Box
      style={{
        width: "100%",
        aspectRatio: "1",
        borderRadius: 5,
        overflow: "hidden",
      }}
    >
      <Skeleton variant="rectangular" width="100%" height="100%" />
    </Box>
    <Box
      style={{
        paddingInline: 10,
        paddingBlock: 10,
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Skeleton variant="text" width="80%" height={36} />
      <Skeleton variant="text" width="40%" height={16} sx={{ mt: 0.5 }} />
      <Box style={{ display: "flex", alignItems: "baseline", gap: 4, minHeight: 45 }}>
        <Skeleton variant="text" width="60%" height={24} />
        <Skeleton variant="text" width="40%" height={16} />
      </Box>
      <Box style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
        <Skeleton variant="text" width="30%" height={20} />
        <Skeleton variant="circular" width={24} height={24} />
      </Box>
    </Box>
  </Box>
);

export default ProductItemSkeleton;
