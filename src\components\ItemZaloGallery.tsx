import { Box } from "@mui/material";
import { <PERSON>Vie<PERSON>, Swiper } from "zmp-ui";
import React, { useState } from "react";
import { ImageType } from "zmp-ui/image-viewer";

interface ImageGalleryProps {
  images: (ImageType & {
    type: string;
  })[];
}

export const ItemZaloGallery = ({ images }: ImageGalleryProps) => {
  const [visible, setVisible] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const onClickImage = (index: number) => {
    setActiveIndex(index);
    setVisible(true);
  };

  return (
    <>
      <Box
        display="flex"
        flexDirection="row"
        flexWrap="nowrap"
        sx={{ width: "100%", height: "40dvh" }}
      >
        <Swiper>
          {images.map((img, index) => (
            <Swiper.Slide key={index}>
              {img.type.includes("image/") ? (
                <img
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "fill",
                  }}
                  role="presentation"
                  onClick={() => onClickImage(index)}
                  src={img.src}
                  alt={img.alt}
                />
              ) : img.type.includes("video/") ? (
                <video
                  style={{
                    width: "100",
                    height: "100",
                    objectFit: "fill",
                  }}
                  onClick={() => {
                    console.log("clicked");
                  }}
                  controls
                >
                  <source src={img.src} type={img.type} />
                  Your browser does not support the video tag.
                </video>
              ) : (
                <> </>
              )}
            </Swiper.Slide>
          ))}
        </Swiper>
      </Box>
      <ImageViewer
        onClose={() => setVisible(false)}
        activeIndex={activeIndex}
        images={images}
        visible={visible}
      />
    </>
  );
};
