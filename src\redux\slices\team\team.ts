import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { ITeamReport } from "../../../types/team";
import { IOrder } from "../../../types/order";

interface TeamState {
  list: any;
  teamReport: ITeamReport | null;
  teamOrder: IOrder[] | [];
  isLoading: boolean;
}

const initialState: TeamState = {
  list: [],
  teamReport: null,
  isLoading: true,
  teamOrder: [],
};

export interface IFilterTeamData {
  search?: string;
  type?: "Purchase" | "NotPurchase";
}

export const joinTeam = createAsyncThunk("team/joinTeam", async (data: any) => {
  const response: any = await request("post", "/api/my-team/join", {
    referCode: data,
  });
  return response;
});

export const getMember = createAsyncThunk(
  "team/getMember",
  async (filterData?: IFilterTeamData) => {
    let url = "/api/affiliation/affiliationuser/myteam";

    if (filterData) {
      const params = new URLSearchParams();

      if (filterData.search) {
        params.append("search", filterData.search);
      }

      if (filterData.type) {
        params.append("type", filterData.type);
      }

      const queryString = params.toString();
      if (queryString) {
        url += `?${queryString}`;
      }
    }

    const response = await request("get", url);
    return response;
  }
);

export const getReport = createAsyncThunk("team/getReport", async () => {
  const response: any = await request("get", "/api/my-team/report");
  return response;
});
export const getTeamOrder = createAsyncThunk("team/getTeamOrder", async (params?: any) => {
  const response: any = await request("get", `/api/my-team/order?orderBy[createdAt]=desc`, params);
  return response;
});

export const getTeamOrderByOrderStatus = createAsyncThunk(
  "team/getTeamOrderByOrderStatus",
  async (data: { orderStatus: number; params?: any }) => {
    const response: any = await request(
      "get",
      `/api/my-team/order?filters[orderStatus][$eq]=${data.orderStatus}&orderBy[createdAt]=desc`,
      data.params
    );

    return response;
  }
);

const TeamState = createSlice({
  name: "team",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(joinTeam.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(joinTeam.fulfilled, (state) => {
      state.isLoading = false;
    });
    builder.addCase(joinTeam.rejected, (state, action) => {
      state.isLoading = false;
    });
    builder.addCase(getMember.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(getMember.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
      const { payload } = action;
      state.list = payload.data;
      state.isLoading = false;
    });
    builder.addCase(getReport.fulfilled, (state, action: PayloadAction<any>) => {
      const { payload } = action;

      state.teamReport = payload;
      state.isLoading = false;
    });
    builder.addCase(getTeamOrder.fulfilled, (state, action: PayloadAction<any>) => {
      const { payload } = action;
      state.teamOrder = payload.data;
      state.isLoading = false;
    });
    builder.addCase(getMember.rejected, (state) => {
      state.isLoading = false;
    });
    builder.addCase(getTeamOrderByOrderStatus.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(
      getTeamOrderByOrderStatus.fulfilled,
      (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.teamOrder = payload.data;
        state.isLoading = false;
      }
    );
    builder.addCase(getTeamOrderByOrderStatus.rejected, (state) => {
      state.isLoading = false;
    });
  },
});

export default TeamState.reducer;
