import React, { memo, useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { IconButton, InputAdornment, Stack, TextField, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { changePassword, updateMe } from "../../redux/slices/authen/authSlice";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { showToast } from "../../utils/common";
import { COLORS } from "../../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Visibility, VisibilityOff } from "@mui/icons-material";

const validationSchema = Yup.object().shape({
  password: Yup.string()
    .required("<PERSON>ui lòng nhập mật khẩu")
    .min(8, "Mật khẩu phải dài từ 8 đến 20 ký tự")
    .max(20, "Mật khẩu phải dài từ 8 đến 20 ký tự"),
  confirmPassword: Yup.string()
    .required("Vui lòng xác nhận mật khẩu")
    .oneOf([Yup.ref("password")], "Mật khẩu xác nhận không đúng"),
});

type FormData = {
  password: string;
  confirmPassword: string;
};

const PopupEditPassword = memo(
  ({
    openSetPassword,
    setOpenSetPassword,
  }: {
    openSetPassword: boolean;
    setOpenSetPassword: (status: boolean) => void;
  }) => {
    const { color } = useConfigApp();
    const dispatch = useDispatch<AppDispatch>();
    const formOptions = { resolver: yupResolver(validationSchema) };
    const user = useSelector((state: RootState) => state.auth.user);
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const { handleSubmit, register, formState, reset } = useForm<FormData>(formOptions);
    const { errors } = formState;

    const submitSetPassword = async (values) => {
      if (!user) return;
      const res: any = await dispatch(
        changePassword({
          userId: user.userId,
          newPassword: values.password,
        })
      );

      if (!res.error) {
        setOpenSetPassword(false);
        reset();
        showToast({
          content: "Cập nhật mật khẩu thành công",
          type: "success",
        });
      } else {
        showToast({
          content: res.error?.message ?? "Quá trình cập nhật lỗi. Vui lòng thử lại",
          type: "error",
        });
      }
    };

    const handleCloseSetPassword = () => {
      setOpenSetPassword(false);
      setShowPassword(false);
      setShowConfirmPassword(false);
      reset();
    };

    return (
      <Dialog
        open={openSetPassword}
        onClose={handleCloseSetPassword}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: "12px",
            paddingBottom: "20px",
          },
        }}
      >
        <form onSubmit={handleSubmit(submitSetPassword)}>
          <DialogContent>
            <Stack
              alignItems={"center"}
              gap={0.6}
              style={{
                marginBottom: 12,
              }}
            >
              <Typography style={{ ...styles.title, color: color.primary }}>Lấy mật khẩu</Typography>
              <Typography style={styles.subTitle}>
                Lấy mật khẩu cho tài khoản của bạn để đăng nhập vào nền tảng web của chúng tôi tại địa chỉ
              </Typography>
            </Stack>
            <Stack gap={2}>
              <TextField
                required
                type={showPassword ? "text" : "password"}
                id="outlined-password"
                label="Mật khẩu mới"
                {...register("password")}
                sx={{
                  "& .MuiInputBase-root": {
                    ...styles.inputStyle,
                  },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setShowPassword((prev) => !prev)} edge="end">
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              {errors.password && <p style={{ color: 'red', margin: 0 }}>{errors.password.message}</p>}

              <TextField
                required
                type={showConfirmPassword ? "text" : "password"}
                id="outlined-confirm-password"
                label="Nhập lại mật khẩu"
                {...register("confirmPassword")}
                sx={{
                  "& .MuiInputBase-root": {
                    ...styles.inputStyle,
                  },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword((prev) => !prev)}
                        edge="end"
                      >
                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              {errors.confirmPassword && <p style={{ color: 'red', margin: 0 }}>{errors.confirmPassword.message}</p>}
            </Stack>
          </DialogContent>
          <DialogActions
            style={{
              justifyContent: "center",
            }}
          >
            <Button
              type="submit"
              style={{
                ...styles.acceptBtn,
                background: color.primary,
              }}
            >
              Xác nhận
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    );
  }
);

export default memo(PopupEditPassword);

const styles: Record<string, React.CSSProperties> = {
  title: {
    color: COLORS.black,
    fontWeight: 700,
    fontSize: "15px",
  },
  subTitle: {
    color: COLORS.neutral14,
    fontWeight: 400,
    fontSize: "11px",
    textAlign: "center",
  },
  inputStyle: {
    borderRadius: "40px",
  },
  inputPlaceHolder: {
    color: COLORS.black,
  },
  acceptBtn: {
    fontSize: "14px",
    fontWeight: 700,
    color: COLORS.white,
    padding: "7px 20px",
    borderRadius: "40px",
    width: "40%",
  },
};
