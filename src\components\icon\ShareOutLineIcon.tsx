import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const ShareOutLineIcon: React.FC<IconCustomProps> = ({
  fillColor,
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
    >
      <path
        d="M13.5417 11.4587L22.0833 2.91699"
        stroke={fillColor}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.9168 7.08325V2.08325H17.9168"
        stroke={fillColor}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.4583 2.08325H9.37498C4.16665 2.08325 2.08331 4.16659 2.08331 9.37492V15.6249C2.08331 20.8333 4.16665 22.9166 9.37498 22.9166H15.625C20.8333 22.9166 22.9166 20.8333 22.9166 15.6249V13.5416"
        stroke={fillColor}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ShareOutLineIcon;
