import React, { memo } from "react";
import { Box } from "@mui/material";

const Poster = () => {
  return (
    <Box>
      <img style={styles.imageContainer} src={"/demo/images/banner.png"} />
    </Box>
  );
};

export default memo(Poster);

const styles: Record<string, React.CSSProperties> = {
  imageContainer: {
    display: "flex",
    overflow: "hidden",
    aspectRatio: 1 / 1,
    width: "100%",
    backgroundSize: "cover",
    backgroundPosition: "center",
  },
  textContainer: {
    // height: 100,
    paddingBlock: 20,
  },
};
