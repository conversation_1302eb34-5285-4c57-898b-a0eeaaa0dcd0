import * as React from "react";

const LookUpQrIcon: React.FC<React.SVGProps<SVGSVGElement> & { fill?: string }> = ({
  fill = "#1531AD",
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    fill="none"
    viewBox="0 0 20 20"
    {...props}
  >
    <path
      fill={fill}
      d="M.892 6.936c.566 0 .87-.315.87-.892V3.476c0-1.132.61-1.708 1.698-1.708h2.634c.566 0 .88-.316.88-.882 0-.555-.315-.87-.88-.87H3.428C1.164.016.01 1.136.01 3.378v2.666c0 .577.316.892.882.892m18.216 0c.577 0 .882-.315.882-.892V3.378c0-2.242-1.132-3.362-3.417-3.362h-2.656c-.576 0-.892.315-.892.87 0 .566.316.882.892.882h2.634c1.066 0 1.686.576 1.686 1.708v2.568c0 .577.316.892.871.892M9.532 9.058V5.261a.476.476 0 0 0-.479-.479H5.267a.476.476 0 0 0-.48.479v3.798c0 .26.208.468.48.468h3.786a.467.467 0 0 0 .48-.469m1.883-3.329h2.85v2.85h-2.85zm2.024 2.024V6.567h-1.186v1.186zm-5.68 0V6.567H6.562v1.186zM5.733 11.42h2.851v2.851h-2.85zm9.337.37v-1.186h-1.186v1.186zm-3.275 0v-1.186h-1.187v1.186zm-4.037 1.644v-1.187H6.562v1.186zm5.68 0v-1.187h-1.197v1.186zm.478 6.55h2.656c2.285 0 3.417-1.132 3.417-3.373v-2.656c0-.577-.316-.892-.882-.892s-.87.316-.87.892v2.568c0 1.132-.62 1.708-1.687 1.708h-2.634c-.576 0-.892.316-.892.882 0 .555.316.871.892.871m-10.49 0h2.667c.566 0 .88-.316.88-.87 0-.567-.315-.882-.88-.882H3.46c-1.088 0-1.697-.577-1.697-1.709v-2.568c0-.577-.316-.892-.87-.892-.577 0-.883.316-.883.892v2.655c0 2.253 1.154 3.374 3.418 3.374m8.369-4.908V13.89h-1.187v1.186zm3.275 0V13.89h-1.186v1.186zm.142-6.017V5.26a.476.476 0 0 0-.48-.479h-3.786a.476.476 0 0 0-.478.479v3.798c0 .26.206.468.478.468h3.787a.467.467 0 0 0 .479-.468m-9.479-3.33h2.851v2.85h-2.85zm3.798 9.01V10.94a.467.467 0 0 0-.479-.467H5.267a.467.467 0 0 0-.48.467v3.798c0 .261.208.479.48.479h3.786a.476.476 0 0 0 .48-.479"
    ></path>
  </svg>
);

export default LookUpQrIcon;
