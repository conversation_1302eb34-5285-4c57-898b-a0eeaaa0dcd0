{"name": "RetailWeb", "private": true, "version": "1.0.0", "description": "RetailWeb", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"start:zalo": "node setup.js --zalo && zmp start --port 3007", "start:zalo-d": "node setup.js --zalo --name retail && zmp start -D --port 3007", "start:zalo-test": "node setup.js --zalo --test --name retail && zmp start -M test -P 3007", "start:zalo-staging": "node setup.js --zalo --staging --name retail && zmp start -M staging -P 3007", "login:zalo": "zmp login", "setup:zalo-prod": "node setup.js --name retail --zalo --production", "deploy:zalo-prod": "node setup.js --name retail --zalo --production  && zmp deploy --mode=production", "deploy:zalo-staging": "node setup.js --name retail --zalo --staging  && zmp deploy --mode=staging", "deploy:zalo-test": "node setup.js --name retail --zalo --test && zmp deploy --mode=test", "start:web": "node setup.js --web --name retail && vite --port 3007 --host 0.0.0.0", "build:web-prod": "yarn && node setup.js --web --name retail --production && vite build --mode production --config vite.config.web.js", "build:web-staging": "yarn && node setup.js --web --name retail && vite build --mode staging --config vite.config.web.js", "build:web-test": "yarn && node setup.js --web --name retail && vite build --mode test --config vite.config.web.js", "preview:web": "vite preview --config vite.config.web.js --port 8010", "start-server": "cd .. && cd giatin-strapi && yarn && yarn watch-admin", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "eslint": "eslint . --fix", "deploy": "zmp deploy"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "3.1.1", "@mui/icons-material": "^5.15.12", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.12", "@mui/x-date-pickers": "^8.5.1", "@reduxjs/toolkit": "^2.2.3", "@vitejs/plugin-react": "^1.3.0", "axios": "^1.6.8", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dompurify": "^3.2.4", "hex-rgb": "^5.0.0", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.30.1", "promogame-player": "^0.0.28", "prop-types": "^15.8.1", "react": "^18.2.0", "react-datepicker": "^6.9.0", "react-dom": "^18.2.0", "react-hook-form": "7.49.3", "react-horizontal-scrolling-menu": "^8.2.0", "react-hot-toast": "^2.4.1", "react-image-gallery": "^1.3.0", "react-redux": "^9.1.1", "react-router-dom": "^6.22.3", "react-slick": "^0.30.2", "recoil": "^0.7.7", "redux-persist": "^6.0.0", "slick-carousel": "^1.8.1", "use-debounce": "^10.0.0", "yup": "^1.3.3", "zmp-qrcode": "^3.0.0", "zmp-sdk": "^2.45.2", "zmp-ui": "^1.9.2", "zmp-vite-plugin": "^1.1.1"}, "devDependencies": {"@types/node": "^20.12.7", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@types/react-router-dom": "^5.3.3", "cross-env": "^7.0.3", "postcss-preset-env": "^6.7.0", "prettier": "^2.7.1", "typescript": "5.4.5", "vite": "^5.2.11", "zmp-cli": "^3.15.5", "zmp-developer-token": "^3.1.0"}, "prettier": {"trailingComma": "es5", "singleQuote": false, "tabWidth": 2, "semi": true, "printWidth": 100}}