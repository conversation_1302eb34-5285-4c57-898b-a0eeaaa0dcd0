import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { INews } from "../../../types/news";
import { request } from "../../../utils/request";
import { NewPosition } from "@/constants/Const";
import {
  IAffiliateReport,
  ICommissionItem,
  IRecruitmentPage,
  ISuccessOrderAffiliate,
} from "@/types/affiliation";
import { ICommissionReport } from "@/types/commission";

interface AffiliationState {
  recruitmentPage: IRecruitmentPage;
  affiliateReport: IAffiliateReport;
  pendingCommission: ICommissionItem[];
  approvedCommission: ICommissionItem[];
  listCommissionReports: ICommissionReport[];
  isAffiliationConfigActive: boolean;
  listCommissionReportsPagination: {
    skip: number;
    limit: number;
    total: number;
  };
  successOrders: {
    list: ISuccessOrderAffiliate[];
    pagination: {
      skip: number;
      limit: number;
      total: number;
    };
  };
  affiliateConfig: any;
  isLoading: boolean;
}
const initPaginate = {
  skip: 0,
  limit: 10,
  total: 0,
};
const initialState: AffiliationState = {
  recruitmentPage: {
    bannerFilePath: "",
    content: "",
    navBarContent: "",
    title: "",
  },
  affiliateReport: {
    totalCustomers: 0,
    customersPurchased: 0,
    successfulOrders: 0,
    totalRevenue: 0,
    pendingCommission: 0,
    paidCommission: 0,
    pointEarnByShare: 0,
    requireMinSpent: null,
    levelOneCommissionPercentage: 0,
    levelTwoCommissionPercentage: null,
    totalUserWithPurchased: 0,
    totalUserWithoutPurchase: 0,
    thisMonthApprovedCommission: 0,
    totalCommissionReceived: 0,
  },
  isAffiliationConfigActive: false,
  listCommissionReportsPagination: initPaginate,
  listCommissionReports: [],
  pendingCommission: [],
  approvedCommission: [],
  successOrders: {
    list: [],
    pagination: {
      skip: 0,
      limit: 10,
      total: 0,
    },
  },
  affiliateConfig: null,
  isLoading: false,
};

export const getRecruitmentPage = createAsyncThunk("affiliation/getRecruitmentPage", async () => {
  const response: any = await request("get", `/api/affiliation/recruitmentpage`);
  return response;
});

export const getAffiliateConfig = createAsyncThunk(
  "affiliation/affiliationuser",
  async (shopId: any) => {
    const response: any = await request("get", `/api/affiliation/affiliationuser?shopId=${shopId}`);
    return response;
  }
);

export const registerAffiliate = createAsyncThunk(
  "affiliation/registerAffiliate",
  async (data: any) => {
    try {
      const response: any = await request(
        "post",
        `/api/affiliation/affiliationuser/registeraffiliate`,
        data
      );
      return response;
    } catch (error: any) {
      return error;
    }
  }
);

export const getAffiliateReport = createAsyncThunk("affiliation/getAffiliateReport", async () => {
  const response: any = await request("get", `/api/affiliation/affiliationuser/affiliateReport`);
  return response;
});

export const getAffiliationConfigActiveStatus = createAsyncThunk(
  "affiliation/getAffiliationConfigActiveStatus",
  async (data: { shopId: string }) => {
    const response: any = await request(
      "get",
      `/api/affiliation/affiliationuser/iscommissionactive?shopId=${data.shopId}`
    );
    return response;
  }
);

export const updateShopVisit = createAsyncThunk(
  "affiliation/updateShopVisit",
  async (data: { shopId?: string | null; isByLink?: boolean }) => {
    const { shopId, isByLink = false } = data;
    const response: any = await request("post", `/api/affiliation/shopvisit`, {
      isByLink: isByLink.toString(),
      shopId,
    });
    return response;
  }
);
export const getPendingCommission = createAsyncThunk(
  "affiliation/getPendingCommission",
  async () => {
    const response: any = await request(
      "get",
      `/api/affiliation/affiliationuser/MyCommission?type=Pending`
    );
    return response;
  }
);

export const getApprovedCommission = createAsyncThunk(
  "affiliation/getApprovedCommission",
  async () => {
    const response: any = await request(
      "get",
      `/api/affiliation/affiliationuser/MyCommission?type=Approved`
    );
    return response;
  }
);

export const getUserCommissionReport = createAsyncThunk(
  "auth/getUserCommissionReport",
  async (params?: { skip?: number; limit?: number }) => {
    const skip = params?.skip ?? 0;
    const limit = params?.limit ?? 10;
    const response = await request(
      "get",
      `/api/affiliation/affiliationuser/mycommissionreport?skip=${skip}&limit=${limit}`
    );
    return response;
  }
);

export const getSuccessOrderAffiliate = createAsyncThunk(
  "auth/getSuccessOrderAffiliate",
  async (params?: { skip?: number; limit?: number }) => {
    const skip = params?.skip ?? 0;
    const limit = params?.limit ?? 10;
    const response = await request(
      "get",
      `/api/affiliation/affiliationuser/MyOrders?skip=${skip}&limit=${limit}`
    );
    return response;
  }
);

const affiliationSlice = createSlice({
  name: "affiliation",
  initialState,
  reducers: {
    clearListCommissionReports: (state) => {
      // keep category id for highlight
      state.listCommissionReports = [];
      state.listCommissionReportsPagination = initPaginate;
    },
    clearSuccessOrderAffiliate: (state) => {
      // keep category id for highlight
      state.successOrders.list = [];
      state.successOrders.pagination = initPaginate;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getRecruitmentPage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getRecruitmentPage.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.recruitmentPage = payload.data;
        state.isLoading = false;
      })
      .addCase(getRecruitmentPage.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getAffiliateReport.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAffiliateReport.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.affiliateReport = payload.data;
        state.isLoading = false;
      })
      .addCase(getAffiliateReport.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getPendingCommission.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPendingCommission.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.pendingCommission = payload.data;
        state.isLoading = false;
      })
      .addCase(getPendingCommission.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getApprovedCommission.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getApprovedCommission.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.approvedCommission = payload.data;
        state.isLoading = false;
      })
      .addCase(getApprovedCommission.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(getUserCommissionReport.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserCommissionReport.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        const isFirstPage = payload?.skip === 0;
        const incomingData = payload?.data ?? [];

        // Nếu là trang đầu → reset list, ngược lại → nối thêm
        const newData = isFirstPage
          ? [...incomingData]
          : [...state.listCommissionReports, ...incomingData];

        state.listCommissionReports = newData;
        state.listCommissionReportsPagination = {
          limit: payload.limit,
          skip: payload.skip,
          total: payload.total,
        };
        state.isLoading = false;
      })
      .addCase(getUserCommissionReport.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getSuccessOrderAffiliate.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getSuccessOrderAffiliate.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        const isFirstPage = payload?.skip === 0;
        const incomingData = payload?.data ?? [];

        // Nếu là trang đầu → reset list, ngược lại → nối thêm
        const newData = isFirstPage
          ? [...incomingData]
          : [...state.successOrders.list, ...incomingData];

        state.successOrders.list = newData;
        state.successOrders.pagination = {
          limit: payload.limit,
          skip: payload.skip,
          total: payload.total,
        };
        state.isLoading = false;
      })
      .addCase(getSuccessOrderAffiliate.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getAffiliationConfigActiveStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getAffiliationConfigActiveStatus.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.isAffiliationConfigActive = payload.data.isActive;
          state.isLoading = false;
        }
      )
      .addCase(getAffiliationConfigActiveStatus.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getAffiliateConfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAffiliateConfig.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.affiliateConfig = payload.data;
        state.isLoading = false;
      })
      .addCase(getAffiliateConfig.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export const { clearListCommissionReports, clearSuccessOrderAffiliate } = affiliationSlice.actions;

export default affiliationSlice.reducer;
