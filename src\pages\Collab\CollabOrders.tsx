import React, { useEffect, useState } from "react";
import FrameContainer from "../../components/layout/Container";
import { Stack, Tab, Tabs, Box, CircularProgress } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { OrderStatusText, OrderStatus } from "../../constants/Const";
import { getTeamOrderByOrderStatus } from "../../redux/slices/team/team";
import OrderItem from "../../components/order/OrderItem";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number | null;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

export default function CollabOrders() {
  const dispatch = useDispatch<AppDispatch>();

  const category = Object.values(OrderStatus).map((status) => ({
    label: OrderStatusText[status],
    id: status,
  }));

  const [tabIndex, setTabIndex] = useState<number | null>(0);
  const handleChange = (event: React.SyntheticEvent, orderId: number) => {
    setTabIndex(orderId);
  };
  const { teamOrder } = useSelector((state: RootState) => state.team);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getOrderCategoryByOrderStatus();
  }, [tabIndex]);

  const getOrderCategoryByOrderStatus = async () => {
    if (tabIndex !== null) {
      setLoading(true);
      await dispatch(
        getTeamOrderByOrderStatus({ orderStatus: category[tabIndex].id }),
      );
      setLoading(false);
    }
  };

  return (
    <FrameContainer title="Lịch sử đơn hàng thành viên">
      <Tabs
        className="order-history"
        value={tabIndex}
        onChange={handleChange}
        variant="scrollable"
        scrollButtons={false}
      >
        {category.map((item, index) => (
          <Tab
            key={index}
            style={{ fontSize: 14, fontWeight: 400 }}
            label={item.label}
          />
        ))}
      </Tabs>
      {loading ? (
        <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
          <CircularProgress />
        </Stack>
      ) : (
        category.map((_, index) => (
          <CustomTabPanel key={index} value={tabIndex} index={index}>
            <Stack gap={1} paddingTop={1}>
              {teamOrder?.length > 0 ? (
                teamOrder.map((order) => (
                  <OrderItem order={order} key={String(order.id)} isShowLevel />
                ))
              ) : (
                <Stack direction="row" justifyContent={"center"} padding={2}>
                  Không có sản phẩm nào
                </Stack>
              )}
            </Stack>
          </CustomTabPanel>
        ))
      )}
    </FrameContainer>
  );
}
