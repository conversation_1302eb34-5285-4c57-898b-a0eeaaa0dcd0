import { Box, Grid, Typography } from "@mui/material";
import dayjs from "dayjs";
import * as React from "react";
import { INews } from "../../types/news";
import { useNavigate } from "../../utils/component-util";
import LazyImage from "../UI/LazyImage";

interface NewsItemProps {
  news: INews;
  containerStyles?: React.CSSProperties;
  titleFromParent?: string;
}

const NewsItem = ({ news, containerStyles, titleFromParent }: NewsItemProps) => {
  const navigate = useNavigate();

  return (
    <Grid
      item
      xs={6}
      style={{ ...styles.gridStyle, ...containerStyles }}
      onClick={() => {
        navigate(`/posts/${news.articleId}`, { state: { title: titleFromParent, news } });
      }}
      key={news.articleId}
    >
      <Box style={styles.boxStyle}>
        <LazyImage
          src={news?.images[0]?.link}
          alt={news.title}
          aspectRatio="19/12"
          style={styles.imageStyle}
        />
        <Box style={styles.containerContentStyle}>
          <Typography style={styles.titleStyle}>{news.title}</Typography>
          {/* <Typography style={styles.timeStyle}>
            {dayjs(news.created).format("HH:mm, DD/MM/YYYY")}
          </Typography> */}
        </Box>
      </Box>
    </Grid>
  );
};

export default NewsItem;

const styles: Record<string, React.CSSProperties> = {
  gridStyle: {
    textAlign: "left",
  },
  boxStyle: {
    borderRadius: 10,
  },
  containerContentStyle: {
    paddingBlock: 6,
    display: "flex",
    flexDirection: "column",
    gap: 4,
  },
  titleStyle: {
    color: "#343434",
    height: "40px",
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 2,
    fontSize: 14,
    fontWeight: 500,
  },
  timeStyle: {
    fontWeight: 400,
    fontSize: 12,
    color: "#B5B5B5",
  },
  imageStyle: {
    background: "#EBEBEB",
  },
};

// Thêm keyframes animation
const styleSheet = document.createElement("style");
styleSheet.textContent = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;
document.head.appendChild(styleSheet);
