import { RootState } from "@/redux/store";
import { getUrlBeImage, hexWithOpacityToHex } from "@/utils/common";
import { hexToRgb, useTheme } from "@mui/material";
import hexRgb from "hex-rgb";
import { useSelector } from "react-redux";

export const useConfigApp = () => {
  const theme = useTheme();
  const { shopInfo } = useSelector((state: RootState) => state.appInfo);

  return {
    businessType: shopInfo.businessType,
    logo: shopInfo.shopLogo?.data?.attributes?.url
      ? getUrlBeImage(shopInfo.shopLogo?.data?.attributes?.url)
      : "/demo/images/logo.png",
    ...shopInfo,
    color: {
      primary: shopInfo.shopTheme?.primaryColor || theme.palette.primary.main,
      secondary: shopInfo.shopTheme?.secondaryColor || theme.palette.secondary.main,
      accent: shopInfo.shopTheme?.accentColor || theme.palette.primary.main,
    },
    bgColor: {
      primary: shopInfo.shopTheme?.bgPrimaryColor || theme.palette.background.default,
      secondary: shopInfo.shopTheme?.bgSecondaryColor || theme.palette.background.default,
      accent: shopInfo.shopTheme?.bgAccentColor || theme.palette.background.paper,
    },
    textColor: {
      primary: shopInfo.shopTheme?.textPrimaryColor || theme.palette.text.primary,
      secondary: shopInfo.shopTheme?.textSecondaryColor || theme.palette.text.secondary,
      accent: shopInfo.shopTheme?.textAccentColor || theme.palette.text.primary,
      disable: theme.palette.text.disabled,
    },
    oaId: shopInfo.oaId || "",
    listProductStyle: "slice",
    ...shopInfo.template,
  };
};

const homeConfig = [
  {
    type: "BannerWithBranch",
    show: true,
    style: {
      category: "slice",
      itemInRow: 1,
    },
  },
  {
    type: "BranchLocation",
    show: true,
  },

  {
    type: "HtmlRender",
    show: true,
  },

  {
    type: "MenuAction",
    show: true,
  },
  {
    type: "MenuCategory",
    show: true,
    style: {
      title: "Categories",
      category: "slice",
      itemInRow: 1,
      itemOption: 1,
    },
  },
  {
    type: "BannerHome1",
    show: true,
    style: {
      category: "slice",
      itemInRow: 1,
    },
  },
  {
    type: "ActiveAccount",
    show: true,
  },
  {
    type: "FollowOA",
    show: true,
  },
  {
    type: "ProductList",
    show: true,
    style: {
      title: "Món mới",
      category: "slice",
      itemInRow: 2,
    },
    // categoryId: "a68b2493-76a9-484d-92ee-d03585258ded",
  },
  {
    type: "BannerHome2",
    show: true,
    style: {
      category: "slice",
      itemInRow: 1,
    },
  },
  {
    type: "ListProduct2",
    show: true,
    style: {
      title: "Best seller",
      category: "slice",
      itemInRow: 2,
    },
  },
  {
    type: "ListNews",
    show: true,
    style: {
      title: "Tin tức nổi bật",
      category: "slice",
      itemInRow: 1,
    },
  },
];
