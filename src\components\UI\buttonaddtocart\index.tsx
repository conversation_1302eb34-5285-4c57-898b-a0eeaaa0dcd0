import PopupAddToCartFnB from "@/components/UI/buttonaddtocart/PopupAddToCartFnB";
import PopupAddToCartRetail from "@/components/UI/buttonaddtocart/PopupAddToCartRetail";
import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";

const componentMap = {
  FB: PopupAddToCartFnB,
  Retail: PopupAddToCartRetail,
};

export default function PopUpAddToCart(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FB");
  const Component = componentMap[businessType] || componentMap.FB;
  return <Component {...props} />;
}
