const fs = require("node:fs");

let Platform = "web";
let AppEnv = "dev";
for (const i in process.argv) {
  const val = process.argv[i];
  if (val === "--zalo") Platform = "zalo";
  if (val === "--web") Platform = "web";
  if (val === "--dev") AppEnv = "dev";
  if (val === "--test") AppEnv = "test";
  if (val === "--staging") AppEnv = "staging";
  if (val === "--production") AppEnv = "production";
}

const content = `
  export const Platform: "web" | "zalo" = "${Platform}";
  export const AppEnv: "dev" | "test" | "staging" | "production" = "${AppEnv}";
`;
fs.writeFile("./src/config.ts", content, () => {});
