import AvtHolderIcon from "@/components/icon/AvtHolderIcon";
import BenefitIcon from "@/components/icon/BenefitIcon";
import DealsIcon from "@/components/icon/DealsIcon";
import GetPointIcon from "@/components/icon/GetPointIcon";
import PointHistoryIcon from "@/components/icon/PointHistoryIcon";
import StarBanner from "@/components/icon/StarBanner";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import VoucherItemCollect, { VoucherItemCategory } from "@/components/voucher/itemcollect";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getMyVoucherList } from "@/redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { Box, Button, Stack as MuiStack, RadioGroup, Stack, Typography } from "@mui/material";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

export default function MembershipDetail() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const shopInfo = useSelector((state: RootState) => state.appInfo.shopInfo);
  const [voucherList, setVoucherList] = React.useState<any[]>([]);
  const { color } = useConfigApp();

  useEffect(() => {
    if (shopId && user?.userId) {
      dispatch(getMyVoucherList({ shopId, userId: user.userId })).then((res: any) => {
        if (res?.payload?.data) setVoucherList(res.payload.data);
      });
    }
  }, [dispatch, shopId, user]);

  return (
    <FrameContainerFull title={shopId ? `${shopInfo?.shopName || "Shop"} Rewards` : "Rewards"}>
      <Box
        sx={{
          bgcolor: "unset",
          minHeight: "100vh",
          display: "flex",
          flexDirection: "column",
          p: 0,
          overflow: "auto",
        }}
      >
        {/* Membership Info - Custom UI */}
        <Box sx={{ bgcolor: "#fff", borderRadius: 2, p: 2, mb: 2, mt: 2 }}>
          <Stack direction="row" alignItems="center" gap={2} width="100%">
            <Box
              sx={{
                width: 56,
                height: 56,
                borderRadius: "50%",
                bgcolor: "#eee",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                overflow: "hidden",
              }}
            >
              {user?.avatar ? (
                <AvtHolderIcon width={56} height={56} fill={color.primary} />
              ) : (
                <AvtHolderIcon width={56} height={56} fill={color.primary} />
              )}
            </Box>
            <Box sx={{ width: "100%" }}>
              <Typography sx={{ fontWeight: 600, fontSize: 18 }}>
                {user?.fullname || user?.name || "Thành viên"}
              </Typography>
              {user?.membershipLevel?.levelName && (
                <Typography
                  sx={{
                    fontWeight: 400,
                    fontSize: 14,
                    color: color.primary,
                    opacity: 0.85,
                    mt: 0.5,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    width: "95%",
                  }}
                >
                  {user.membershipLevel.levelName}
                </Typography>
              )}
            </Box>
          </Stack>
          <Box
            sx={{
              mt: 2,
              bgcolor: "transparent",
              borderRadius: 2,
              p: 2,
              textAlign: "center",
              color: "#fff",
              position: "relative",
              overflow: "hidden",
              minHeight: 140,
            }}
          >
            <Box
              sx={{
                position: "absolute",
                inset: 0,
                width: "100%",
                height: "100%",
                zIndex: 0,
                pointerEvents: "none",
              }}
            >
              <StarBanner fill={color.primary} />
            </Box>
            {/* Content overlay */}
            <Box sx={{ position: "relative", zIndex: 1 }}>
              <Typography sx={{ fontWeight: 700, fontSize: 32 }}>{user?.point ?? 0}</Typography>
              <Typography sx={{ fontWeight: 500, fontSize: 16 }}>Star</Typography>
              <Button
                variant="contained"
                sx={{
                  mt: 1,
                  borderRadius: 99,
                  bgcolor: "#fff",
                  color: color.primary,
                  fontWeight: 600,
                  px: { xs: 1.5, sm: 2 },
                  py: 0.5,
                  boxShadow: "none",
                  whiteSpace: "normal",
                  fontSize: { xs: 12, sm: 15 },
                  width: "90%",
                  textAlign: "center",
                  lineHeight: 1.5,
                }}
                onClick={() => navigate("/voucher", { state: { voucherList } })}
              >
                Dùng Star để đổi lấy Ưu đãi hoặc quà tặng tại {shopInfo?.shopName || "Shop"}
              </Button>
              <Typography
                sx={{
                  fontSize: 13,
                  color: "#fff",
                  mt: 1,
                  maxWidth: 280,
                  mx: "auto",
                  wordBreak: "break-word",
                }}
              >
                {user?.membershipLevel?.levelName &&
                user?.membershipLevel?.levelName !== "Chưa có xếp hạng"
                  ? "Hãy tích điểm để nâng hạng và nhận nhiều ưu đãi hơn!"
                  : "Hãy tích điểm để nâng hạng và nhận nhiều ưu đãi hơn!"}
              </Typography>
            </Box>
          </Box>
        </Box>
        {/* Slider 4 icon */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            px: 2,
            py: 3,
            mb: 2,
            background: "#f6f9ff",
            borderRadius: 2,
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              flex: 1,
              cursor: "pointer",
            }}
            onClick={() => navigate("/voucher", { state: { voucherList } })}
          >
            <Box
              sx={{
                width: 56,
                height: 56,
                borderRadius: "50%",
                background: "#eaf2ff",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                mb: 1,
              }}
            >
              {/* Ưu đãi của bạn */}
              <DealsIcon width={56} height={56} fill={color.primary} />
            </Box>
            <Typography
              sx={{ color: color.primary, fontSize: 14, textAlign: "center", fontWeight: 500 }}
            >
              Ưu đãi
              <br />
              của bạn
            </Typography>
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              flex: 1,
              cursor: "pointer",
            }}
            onClick={() => navigate("/profile/earn-points-guide")}
          >
            <Box
              sx={{
                width: 56,
                height: 56,
                borderRadius: "50%",
                background: "#eaf2ff",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                mb: 1,
              }}
            >
              {/* Cách kiếm điểm */}
              <GetPointIcon width={56} height={56} fill={color.primary} />
            </Box>
            <Typography
              sx={{ color: color.primary, fontSize: 14, textAlign: "center", fontWeight: 500 }}
            >
              Cách kiếm
              <br />
              điểm
            </Typography>
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              flex: 1,
              cursor: "pointer",
            }}
            onClick={() => navigate("/profile/point-history")}
          >
            <Box
              sx={{
                width: 56,
                height: 56,
                borderRadius: "50%",
                background: "#eaf2ff",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                mb: 1,
              }}
            >
              {/* Lịch sử đổi điểm */}
              <PointHistoryIcon width={56} height={56} fill={color.primary} />
            </Box>
            <Typography
              sx={{ color: color.primary, fontSize: 14, textAlign: "center", fontWeight: 500 }}
            >
              Lịch sử
              <br />
              đổi điểm
            </Typography>
          </Box>
          <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", flex: 1 }}>
            <Box
              sx={{
                width: 56,
                height: 56,
                borderRadius: "50%",
                background: "#eaf2ff",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                mb: 1,
                cursor: "pointer",
              }}
              onClick={() => navigate("/profile/membership-benefit")}
            >
              {/* Quyền lợi của bạn */}
              <BenefitIcon width={56} height={56} fill={color.primary} />
            </Box>
            <Typography
              sx={{ color: color.primary, fontSize: 14, textAlign: "center", fontWeight: 500 }}
            >
              Quyền lợi
              <br />
              của bạn
            </Typography>
          </Box>
        </Box>
        {/* Đổi Star - Voucher Xịn */}
        <Box sx={{ background: "#fff", borderRadius: 2, p: 2, mb: 2 }}>
          <Box
            sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}
          >
            <Typography sx={{ fontWeight: 700, fontSize: 18, color: color.primary }}>
              Đổi Star
            </Typography>
            <Button
              variant="text"
              sx={{
                color: color.primary,
                fontWeight: 500,
                fontSize: 14,
                textTransform: "none",
                p: 0,
                minWidth: 0,
              }}
              onClick={() => navigate("/voucher")}
            >
              {`Xem tất cả`}
              <img
                src="/icons/arrow_right.svg"
                alt="arrow"
                style={{ width: 18, height: 18, marginLeft: 4 }}
              />
            </Button>
          </Box>
          <MuiStack className="voucher-profile" width={"100%"} marginBlock={2}>
            <RadioGroup row name="voucher-list" defaultValue="top">
              {(voucherList || []).slice(0, 4).map((item) => (
                <Box
                  sx={{ paddingBottom: 2, width: "100%" }}
                  key={item.voucherUserId || item.voucherId}
                >
                  <VoucherItemCollect
                    item={item.voucherDetail || item}
                    category={VoucherItemCategory.LIST}
                    isShowMatchPoint={
                      !!(item.voucherDetail
                        ? item.voucherDetail.exchangePoints
                        : item.exchangePoints)
                    }
                    onNavigateToDetail={() =>
                      navigate(
                        `/voucher/detail/${item.voucherId || item.voucherDetail?.voucherId}`,
                        {
                          state: { voucher: item.voucherDetail || item },
                        }
                      )
                    }
                    onSelectVoucher={() => {}}
                    isChecked={false}
                    onApplyClick={() => {}}
                    myVoucherList={voucherList}
                  />
                </Box>
              ))}
            </RadioGroup>
          </MuiStack>
        </Box>
      </Box>
    </FrameContainerFull>
  );
}
