import React, { useState } from "react";
import { AppBar, Avatar, Box, Button, IconButton, Toolbar, Typography } from "@mui/material";
import { commonStyle } from "@/constants/themes";
import { ICONS } from "@/constants/icons";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useNavigate } from "react-router-dom";
import PopupChangePassword from "./components/PopupChangePassword";
import RegisterPartnerSheet from "./components/RegisterPartnerSheet";
import { useAuth } from "@/hooks/useAuth";
import { useConfigApp } from "@/hooks/useConfigApp";

const LIST_MENU = [
  {
    icon: ICONS.ICON_PROFILE_ORDER,
    title: "Lịch sử mua hàng",
    path: "/invoices",
  },
  {
    icon: ICONS.ICON_PROFILE_ADDRESS,
    title: "Sổ địa chỉ",
    path: "/address",
  },
  {
    icon: ICONS.ICON_PROFILE_INVITE,
    title: "Giớ<PERSON> thiệu bạn bè",
    path: "/referral",
  },
  {
    icon: ICONS.ICON_PROFILE_QA,
    title: "Câu hỏi thường gặp",
    path: "/faq",
  },
  {
    icon: ICONS.ICON_PROFILE_TERM,
    title: "Điều khoản và chính sách",
    path: "/terms",
  },
  {
    icon: ICONS.ICON_PROFILE_CONTACT,
    title: "Liên hệ và góp ý",
    path: "/contact",
  },
  {
    icon: ICONS.ICON_PROFILE_CHANGE_PASS,
    title: "Thay đổi mật khẩu",
    onClick: "handleTogglePopupPassword",
  },
];

const Profile = () => {
  const { user, isLogin } = useSelector((state: RootState) => state.auth);
  const { handleLogout } = useAuth();
  const { color } = useConfigApp();
  const navigate = useNavigate();

  const [openPassword, setOpenPassword] = useState(false);
  const [openRegisterPartner, setOpenRegisterPartner] = useState(false);

  const handleTogglePopupPassword = () => {
    setOpenPassword(!openPassword);
  };
  const handleItemClick = (item) => {
    if (item.path) {
      navigate(item.path);
    } else if (item.onClick === "handleTogglePopupPassword") {
      handleTogglePopupPassword();
    }
  };

  const handleLogin = () => {
    navigate("/login");
  };

  return (
    <Box sx={{ background: "#F2F2F2", height: "100vh" }}>
      <PopupChangePassword open={openPassword} onClose={handleTogglePopupPassword} />
      <RegisterPartnerSheet
        open={openRegisterPartner}
        onOpen={() => setOpenRegisterPartner(true)}
        onClose={() => setOpenRegisterPartner(false)}
      />

      <AppBar position="static" sx={{ background: color.primary, ...style.appBar }}>
        <Toolbar sx={style.toolBar}>
          <Typography sx={commonStyle.titleApp}>Cá nhân</Typography>
          <IconButton onClick={() => navigate("/cart")}>
            <img src={ICONS.ICON_CART} alt="cart" />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Box sx={style.body}>
        <Box sx={style.infoUser}>
          <Avatar sx={style.avatar} src={user?.avatar} />
          <Box sx={{ ml: "10px" }}>
            <Typography sx={style.name}>{user?.name}</Typography>
            <Typography sx={style.phone}>{user?.phone}</Typography>
          </Box>
        </Box>
        <Box sx={style.viewCard}>
          <Box sx={style.viewItem}>
            <Typography sx={style.textOne}>Điểm</Typography>
            <Typography sx={style.textTwo}>0</Typography>
          </Box>
          <Box sx={{ border: "1px solid #F2F2F2" }} />
          <Box sx={style.viewItem}>
            <Typography sx={style.textOne}>Hạng</Typography>
            <Typography sx={style.textTwo}>Member</Typography>
          </Box>
        </Box>
        <Box sx={style.line} />
        {LIST_MENU.map((item, index) => (
          <Box key={index} sx={style.menuItem} onClick={() => handleItemClick(item)}>
            <img src={item.icon} alt={item.title} />
            <Typography sx={style.titleMenu}>{item.title}</Typography>
            <img src={ICONS.ICON_ARROW_RIGHT} alt="arrow" />
          </Box>
        ))}

        <Box sx={style.line} />
        <Box sx={style.menuItem} onClick={() => setOpenRegisterPartner(true)}>
          <img src={ICONS.ICON_PROFILE_AFFILIATE} alt={"affiliate"} />
          <Typography sx={style.titleMenu}>Đăng ký Affiliate</Typography>
          <img src={ICONS.ICON_ARROW_RIGHT} alt="arrow" />
        </Box>

        {isLogin ? (
          <Button sx={style.btnLogout} onClick={handleLogout}>
            Đăng xuất
          </Button>
        ) : (
          <Button sx={style.btnLogout} onClick={handleLogin}>
            Đăng nhập
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default Profile;

const style = {
  appBar: {
    padding: "10px",
  },
  toolBar: {
    minHeight: "auto",
    padding: 0,
  },
  body: {
    padding: "10px",
  },
  infoUser: {
    display: "flex",
    alignItems: "center",
  },
  avatar: {
    width: 60,
    height: 60,
  },
  name: {
    fontSize: "18px",
    fontWeight: "bold",
    color: "#000",
  },
  phone: {
    fontSize: "14px",
    color: "#A0A0A0",
  },
  viewCard: {
    display: "flex",
    alignItems: "center",
    background: "#fff",
    borderRadius: "10px",
    padding: "10px",
    mt: "10px",
  },
  viewItem: {
    flex: 1,
    textAlign: "center",
  },
  textOne: {
    fontSize: "14px",
    color: "#A0A0A0",
  },
  textTwo: {
    fontSize: "18px",
    fontWeight: "bold",
    color: "#000",
    mt: "5px",
  },
  line: {
    height: "10px",
    background: "#F2F2F2",
    margin: "0 -10px",
  },
  menuItem: {
    display: "flex",
    alignItems: "center",
    padding: "15px 0",
    borderBottom: "1px solid #F2F2F2",
    "& > img:first-of-type": {
      width: "24px",
      height: "24px",
      mr: "10px",
    },
    "& > img:last-of-type": {
      width: "24px",
      height: "24px",
      ml: "auto",
    },
  },
  titleMenu: {
    fontSize: "16px",
    color: "#000",
  },
  btnLogout: {
    width: "100%",
    height: "50px",
    background: "#fff",
    borderRadius: "10px",
    mt: "20px",
    color: "red",
    fontSize: "16px",
    fontWeight: "bold",
  },
};
