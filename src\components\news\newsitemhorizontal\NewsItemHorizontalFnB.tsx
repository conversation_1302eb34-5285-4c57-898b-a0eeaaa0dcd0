import { COLORS } from "@/constants/themes";
import { Box, Grid, Typography } from "@mui/material";
import * as React from "react";
import { INews } from "../../../types/news";
import { useNavigate } from "../../../utils/component-util";
import LazyImage from "@/components/UI/LazyImage";

interface NewsItemHorizontalProps {
  news: INews;
  containerStyles?: React.CSSProperties;
  titleFromParent?: string;
}

const NewsItemHorizontal = ({
  news,
  containerStyles,
  titleFromParent,
}: NewsItemHorizontalProps) => {
  const navigate = useNavigate();

  return (
    <Grid
      item
      xs={6}
      style={{ textAlign: "left", ...containerStyles }}
      onClick={() => {
        navigate(`/posts/${news.articleId}`, {
          state: { title: titleFromParent, news },
        });
      }}
      key={news.articleId}
    >
      <Box style={{ background: COLORS.white, borderRadius: 5 }}>
        <LazyImage
          src={news.images[0]?.link}
          alt={news?.title}
          style={styles.imageStyle}
          aspectRatio="16/9"
        />
        <Box style={styles.containerContentStyle}>
          <Typography style={styles.titleStyle}>{news.title}</Typography>
          <Typography style={styles.descStyle}>{news.desc}</Typography>
          {/* <Typography style={styles.timeStyle}>
            {dayjs(news.createdAt).format("HH:mm, DD/MM")}
          </Typography> */}
        </Box>
      </Box>
    </Grid>
  );
};

export default NewsItemHorizontal;

const styles: Record<string, React.CSSProperties> = {
  containerContentStyle: {
    paddingBlock: 6,
    display: "flex",
    flexDirection: "column",
    gap: 4,
  },
  titleStyle: {
    color: "#555555",
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 2,
    paddingInline: 4,
    fontSize: 14,
    fontWeight: 500,
  },
  descStyle: {
    color: "#B4B4B4",
    fontSize: 12,
    fontWeight: 400,
    paddingInline: 4,
  },
  timeStyle: {
    fontWeight: 500,
    fontSize: 12,
    color: "#969595",
    paddingInline: 4,
  },
  imageStyle: {
    aspectRatio: 15 / 8,
    objectFit: "cover",
    borderTopRightRadius: 5,
    borderTopLeftRadius: 5,
  },
};
