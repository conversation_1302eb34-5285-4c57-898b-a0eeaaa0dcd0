import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";
import NewsItemHorizontalFnB from "./NewsItemHorizontalFnB";
import NewsItemHorizontalRetail from "./NewsItemHorizontalRetail";

const componentMap = {
  FnB: NewsItemHorizontalFnB,
  Retail: NewsItemHorizontalRetail,
};

export default function NewsItemHorizontal(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FnB");
  const Component = componentMap[businessType] || componentMap.FnB;
  return <Component {...props} />;
}
