import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  FormControl,
  IconButton,
  RadioGroup,
  Slide,
  Stack,
  InputAdornment,
  TextField,
  Container,
  Typography,
  Box,
  Divider,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import React, { useEffect, useMemo } from "react";
import { useState } from "react";
import { Icon } from "../../constants/Assets";
import { TransitionProps } from "@mui/material/transitions";
import ClearIcon from "@mui/icons-material/Clear";
import { AppDispatch, RootState } from "../../redux/store";
import { useDispatch, useSelector } from "react-redux";
import {
  getListVoucherByCart,
  GetVoucherByShopParams,
  VoucherDto,
} from "../../redux/slices/voucher/voucherSlice";
import { SelectedVoucher } from "@/components/voucher/CheckVoucherType";
import { useNavigate } from "../../utils/component-util";
import { COLORS, commonStyle } from "../../constants/themes";
import VoucherItem, { VoucherItemCategory } from "./VourcherItem";
import { useDebounce } from "use-debounce";
import NoDataView from "../UI/NoDataView";
import { Router } from "../../constants/Route";
import { useConfigApp } from "@/hooks/useConfigApp";
import { setCartPayment } from "@/redux/slices/cart/cartSlice";
import { useCart } from "@/hooks/useCart";
import { ICart } from "@/types/cart";
import { ReleaseType, StatusDelivery, VoucherType } from "@/constants/Const";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function SelectVoucher({
  open,
  setOpen,
  onApplyVoucher,
  initVoucher,
  isCreatedOrder,
  setIsCreatedOrder,
  selectedVoucher,
  setSelectedVoucher,
}) {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [showClearIcon, setShowClearIcon] = useState("none");
  const [searchKey, setSearchKey] = useState("");
  const [debounceValue] = useDebounce(searchKey, 1000);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { user } = useSelector((state: RootState) => state.auth);
  const {
    cartPayment,
    clearCart,
    changeProductQuantity,
    removeProductFromCart,
    createOrder,
    setCartAndSave,
  } = useCart();
  const { myVoucherList, disabledVouchers } = useSelector((state: RootState) => state.vouchers);
  const { defaultBranch, list } = useSelector((state: RootState) => state.branch);
  const data = { shopId: shopId, userId: user?.userId };
  const [tempVoucherTransport, setTempVoucherTransport] = useState<VoucherDto | null>();
  const [tempVoucherPromotion, setTempVoucherPromotion] = useState<VoucherDto | null>();
  // useEffect(() => {
  //   dispatch(getMyVoucherList(data));
  // }, [open]);
  const { color, bgColor, shopLogo } = useConfigApp();

  useEffect(() => {
    setSelectedVoucher(initVoucher);
    setTempVoucherTransport(initVoucher);
    setTempVoucherPromotion(initVoucher);
  }, [initVoucher]);

  // Handle open voucher list
  const handleClickOpen = () => {
    setOpen(true);
    // dispatch(getMyVoucherList(data));
  };
  const handleClose = () => {
    setOpen(false);
  };

  // Handle select voucher
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    setSearchKey(event.target.value);
  };

  // Handle search voucher
  const handleClick = (): void => {
    setSearchKey("");
  };

  const [myVouchers, setMyVouchers] = useState<VoucherDto[]>([]);
  const [myVouchersTransport, setMyVouchersTransport] = useState<VoucherDto[]>([]);
  const [myVouchersPromotion, setMyVouchersPromotion] = useState<VoucherDto[]>([]);

  // Helper to get the first voucherCode from voucherDetails
  const getFirstVoucherCode = (voucher: VoucherDto | undefined | null) =>
    Array.isArray(voucher?.voucherDetails) && voucher.voucherDetails.length > 0
      ? voucher.voucherDetails[0].voucherCode
      : undefined;

  useEffect(() => {
    if (!user) return;
    const fetchVoucherUser = async () => {
      const res = await dispatch(getListVoucherByCart(cartPayment));
      if (res?.payload) {
        setMyVouchers(res.payload?.result);

        const transportVouchers: VoucherDto[] = [];
        const promotionVouchers: VoucherDto[] = [];

        res.payload?.result?.forEach((item: VoucherDto) => {
          if (item.voucherType === VoucherType.Transport) {
            transportVouchers.push(item);
          } else {
            promotionVouchers.push(item);
          }
        });

        setMyVouchersTransport(transportVouchers);
        setMyVouchersPromotion(promotionVouchers);
      }
    };
    fetchVoucherUser();
  }, [user]);

  useEffect(() => {
    // Transport
    let selectedTransport: VoucherDto | null = null;
    if (cartPayment.voucherCodes && myVouchersTransport.length > 0) {
      selectedTransport =
        myVouchersTransport.find((v) =>
          v.voucherDetails?.some((d) => cartPayment.voucherCodes?.includes(d.voucherCode))
        ) || null;
      if (!selectedTransport && cartPayment.voucherTransport?.[0]) {
        const code =
          getFirstVoucherCode(cartPayment.voucherTransport[0]) || cartPayment.voucherCodes[0];
        selectedTransport = {
          ...cartPayment.voucherTransport[0],
          voucherDetails: [
            {
              voucherDetailId: "",
              voucherId: cartPayment.voucherTransport[0].voucherId || "",
              voucherCode: code,
              voucherCodeLink: "",
              status: "",
              isSystemAssigned: false,
              user: null,
              numUse: null,
              branchId: null,
              createdDate: "",
              modifiedDate: "",
              createdBy: "",
              modifiedBy: "",
              isDeleted: false,
              deletedAt: null,
              id: "",
            },
          ],
        };
      }
    }
    setTempVoucherTransport(selectedTransport);

    // Promotion
    let selectedPromotion: VoucherDto | null = null;
    if (cartPayment.voucherCodes && myVouchersPromotion.length > 0) {
      selectedPromotion =
        myVouchersPromotion.find((v) =>
          v.voucherDetails?.some((d) => cartPayment.voucherCodes?.includes(d.voucherCode))
        ) || null;
      if (!selectedPromotion && cartPayment.voucherPromotion?.[0]) {
        const code =
          getFirstVoucherCode(cartPayment.voucherPromotion[0]) || cartPayment.voucherCodes[0];
        selectedPromotion = {
          ...cartPayment.voucherPromotion[0],
          voucherDetails: [
            {
              voucherDetailId: "",
              voucherId: cartPayment.voucherPromotion[0].voucherId || "",
              voucherCode: code,
              voucherCodeLink: "",
              status: "",
              isSystemAssigned: false,
              user: null,
              numUse: null,
              branchId: null,
              createdDate: "",
              modifiedDate: "",
              createdBy: "",
              modifiedBy: "",
              isDeleted: false,
              deletedAt: null,
              id: "",
            },
          ],
        };
      }
    }
    setTempVoucherPromotion(selectedPromotion);
  }, [open, myVouchersTransport, myVouchersPromotion, cartPayment.voucherCodes]);

  // const fetchVoucherStatus = async () => {
  //   const voucherIdList = filteredVoucherList.map((x) => x.voucherId);
  //   await dispatch(validateVoucherByCart({ voucherIds: voucherIdList }));
  // };

  // useEffect(() => {
  //   fetchVoucherStatus();
  // }, [dispatch, open]);

  const updateCartAndClose = async (
    voucherTransport: VoucherDto[] = [],
    voucherPromotion: VoucherDto[] = []
  ) => {
    // Extract voucher codes from both arrays
    const transportCodes = voucherTransport
      .map((v) => (v.voucherDetails ? v.voucherDetails.map((d) => d.voucherCode) : []))
      .reduce((acc, cur) => acc.concat(cur), [] as string[]);
    const promotionCodes = voucherPromotion
      .map((v) => (v.voucherDetails ? v.voucherDetails.map((d) => d.voucherCode) : []))
      .reduce((acc, cur) => acc.concat(cur), [] as string[]);
    const voucherCodes = transportCodes.concat(promotionCodes);
    const updatedCart: ICart = {
      ...cartPayment,
      voucherTransport,
      voucherPromotion,
      voucherCodes: voucherCodes,
    };
    await dispatch(setCartPayment(updatedCart));
    const res = await setCartAndSave(updatedCart);
    if (res === true) {
      setIsCreatedOrder(true);
    }
    handleClose();
  };

  const handleApplyVoucher = async () => {
    if (!tempVoucherTransport && !tempVoucherPromotion) {
      setSelectedVoucher([]);
      await updateCartAndClose();
      return;
    }

    setSelectedVoucher((prev) => {
      let updatedVouchers = prev || [];
      if (tempVoucherTransport) {
        updatedVouchers = updatedVouchers.filter((v) => v.voucherType !== VoucherType.Transport);
        updatedVouchers.push(tempVoucherTransport);
      }
      if (tempVoucherPromotion) {
        updatedVouchers = updatedVouchers.filter((v) => v.voucherType !== VoucherType.Discount);
        updatedVouchers.push(tempVoucherPromotion);
      }
      return updatedVouchers.slice(0, 2);
    });
    await updateCartAndClose(
      tempVoucherTransport ? [tempVoucherTransport] : [],
      tempVoucherPromotion ? [tempVoucherPromotion] : []
    );
  };

  const handleSelectVoucher = (item: VoucherDto, type: "transport" | "promotion") => {
    // Helper to get all codes from a voucher
    const getCodes = (voucher?: VoucherDto) =>
      Array.isArray(voucher?.voucherDetails)
        ? voucher.voucherDetails.map((d) => d.voucherCode)
        : [];

    if (type === "transport") {
      setTempVoucherTransport((prev) => {
        const prevCodes = getCodes(prev || undefined);
        const itemCodes = getCodes(item);
        if (prevCodes.length && itemCodes.some((code) => prevCodes.includes(code))) {
          return null;
        }
        return item;
      });
    } else {
      setTempVoucherPromotion((prev) => {
        const prevPromotionCodes = getCodes(prev || undefined);
        const itemCodes = getCodes(item);
        if (
          prevPromotionCodes.length &&
          itemCodes.some((code) => prevPromotionCodes.includes(code))
        ) {
          return null;
        }
        return item;
      });
    }

    // Cập nhật selectedVoucher: luôn chỉ giữ 1 voucher cùng loại
    setSelectedVoucher((prev) => {
      let newSelected = (prev || []).filter((v) => v.voucherType !== item.voucherType);
      const itemCodes = getCodes(item);
      // Nếu đang chọn lại chính voucher này thì không thêm vào
      if (
        Array.isArray(prev) &&
        prev.some(
          (v) =>
            v.voucherType === item.voucherType &&
            getCodes(v).some((code) => itemCodes.includes(code))
        )
      ) {
        return newSelected;
      }
      return [...newSelected, item];
    });
  };

  // const filterAndSortVouchers = (
  //   vouchers: ICheckVoucherDisable[],
  //   searchKey: string,
  //   cartPrice: number
  // ): VoucherDto[] => {
  //   if (!vouchers?.length) return [];

  //   const filteredList = searchKey
  //     ? vouchers.filter((voucher) => {
  //         const removeMarkVoucherCode =
  //           voucher.voucher.voucherDetails &&
  //           voucher.voucher.voucherDetails.length > 0 &&
  //           voucher.voucher.voucherDetails[0].voucherCode
  //             ? removeMark(voucher.voucher.voucherDetails[0].voucherCode).toLowerCase()
  //             : "";
  //         const removeMarkSearchKey = removeMark(searchKey).toLowerCase();
  //         return removeMarkVoucherCode.includes(removeMarkSearchKey);
  //       })
  //     : vouchers;

  //   return [...filteredList].sort((a, b) => {
  //     const aDisabled = disabledVouchers && !disabledVouchers[a.voucherId] ? 1 : -1;
  //     const bDisabled = disabledVouchers && !disabledVouchers[b.voucherId] ? 1 : -1;

  //     if (aDisabled !== bDisabled) return aDisabled - bDisabled;

  //     const aEligible = a.minOrder < cartPrice ? -1 : 1;
  //     const bEligible = b.minOrder < cartPrice ? -1 : 1;
  //     if (aEligible !== bEligible) return aEligible - bEligible;

  //     return a.minOrder - b.minOrder;
  //   });
  // };
  // const filteredVoucherList = useMemo(() => {
  //   return filterAndSortVouchers(myVouchers, debounceValue, cartPayment.originPrice);
  // }, [open, debounceValue, myVouchers, cartPayment.originPrice]);

  // const filteredVoucherTransportList = useMemo(() => {
  //   return filterAndSortVouchers(myVouchersTransport, debounceValue, cartPayment.originPrice);
  // }, [open, debounceValue, myVouchersTransport, cartPayment.originPrice]);

  // const filteredVoucherPromotionList = useMemo(() => {
  //   return filterAndSortVouchers(myVouchersPromotion, debounceValue, cartPayment.originPrice);
  // }, [open, debounceValue, myVouchersPromotion, cartPayment.originPrice]);

  return (
    <Box>
      {cartPayment?.voucherPromotion?.length > 0 || cartPayment?.voucherTransport?.length > 0 ? (
        <Stack
          direction="row"
          justifyContent={"space-between"}
          onClick={handleClickOpen}
          paddingBlock={1}
        >
          <Stack direction="row" gap={2}>
            <img src={Icon.voucher} width={25} height={41.48} />
            <Stack>
              {cartPayment.voucherTransport.length > 0 && (
                <Typography style={styles.contentTitle}>
                  {SelectedVoucher(cartPayment?.voucherTransport?.[0])}
                </Typography>
              )}
              {cartPayment.voucherPromotion.length > 0 && (
                <Typography style={styles.contentTitle}>
                  {SelectedVoucher(cartPayment?.voucherPromotion?.[0])}
                </Typography>
              )}
              <Typography style={styles.contentText}>
                Voucher của{" "}
                <span
                  style={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    maxWidth: "170px",
                    display: "inline-block",
                    verticalAlign: "bottom",
                  }}
                  title={appConfig?.shopName}
                >
                  {appConfig?.shopName}
                </span>
              </Typography>
            </Stack>
          </Stack>
          <IconButton>
            <img src={Icon.arrow_right} alt="" />
          </IconButton>
        </Stack>
      ) : (
        <Stack
          direction="row"
          justifyContent={"space-between"}
          onClick={handleClickOpen}
          paddingBlock={1}
        >
          <Stack direction="row" gap={2}>
            <img src={Icon.voucher} width={25} height={41.48} />
            <Stack>
              <Typography style={styles.contentTitle}>Chưa áp dụng (chọn hoặc nhập mã)</Typography>
              <Typography style={styles.contentText}>
                Voucher của{" "}
                <span
                  style={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    maxWidth: "170px",
                    display: "inline-block",
                    verticalAlign: "bottom",
                  }}
                  title={appConfig?.shopName}
                >
                  {appConfig?.shopName}
                </span>
              </Typography>
            </Stack>
          </Stack>
          <IconButton>
            <img src={Icon.arrow_right} alt="" />
          </IconButton>
        </Stack>
      )}

      <Dialog
        className="popup-voucher"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
        scroll={"paper"}
      >
        <DialogActions sx={{ justifyContent: "center" }}>
          <Stack width={"100%"}>
            <Stack direction="row" sx={styles.topContainer}>
              {/* <img src="/images/voucher.png" /> */}
              <Stack sx={{ alignItems: "center", width: "100%" }}>
                <Typography
                  style={{
                    ...commonStyle.headline16,
                    color: appConfig.color.primary,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    display: "inline-block",
                    verticalAlign: "bottom",
                    width: "100%",
                  }}
                >
                  {appConfig?.shopName}
                </Typography>
                <Typography style={{ fontSize: 16, color: appConfig.color.primary }}>
                  (Ưu đãi của bạn)
                </Typography>
              </Stack>
            </Stack>
            <Stack direction={"row"} gap={2} justifyContent="space-between">
              <FormControl style={{ flexGrow: 1 }}>
                <TextField
                  placeholder="Nhập mã ưu đãi"
                  size="small"
                  variant="outlined"
                  style={{ width: "100%" }}
                  onChange={handleChange}
                  value={searchKey}
                  className="search-input"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">{<SearchIcon />}</InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment
                        position="end"
                        style={{ display: showClearIcon }}
                        onClick={handleClick}
                      >
                        <ClearIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </FormControl>
              <Button
                style={{
                  ...styles.moreVoucher,
                  background: appConfig.color.primary,
                }}
                onClick={() =>
                  navigate(Router.voucher.index, {
                    state: { valueSearch: searchKey },
                  })
                }
              >
                Tìm thêm Voucher
              </Button>
            </Stack>
          </Stack>
        </DialogActions>
        <DialogContent style={{ width: "100%", padding: 10 }}>
          {cartPayment?.statusDelivery === StatusDelivery.InHome && (
            <>
              {myVouchersTransport?.length > 0 && (
                <>
                  <Typography sx={{ fontSize: 14, fontWeight: 500, mb: 1 }}>
                    Ưu đãi phí vận chuyển
                  </Typography>
                  <Stack className="voucher-profile" width={"100%"}>
                    <RadioGroup
                      row
                      aria-labelledby="demo-form-control-label-placement"
                      name="position"
                      defaultValue="top"
                    >
                      {myVouchersTransport?.length > 0 ? (
                        myVouchersTransport?.map((item: VoucherDto) => (
                          <Box
                            sx={{ paddingBottom: 2, width: "100%" }}
                            key={item?.voucherDetails?.[0]?.voucherCode}
                          >
                            <VoucherItem
                              item={item}
                              category={VoucherItemCategory.SELECT}
                              onSelectVoucher={() => handleSelectVoucher(item, "transport")}
                              isChecked={
                                !!item?.voucherDetails?.[0]?.voucherCode &&
                                Array.isArray(tempVoucherTransport?.voucherDetails) &&
                                tempVoucherTransport.voucherDetails.some(
                                  (d) => d.voucherCode === item?.voucherDetails?.[0]?.voucherCode
                                )
                              }
                            />
                          </Box>
                        ))
                      ) : (
                        <NoDataView content="Không có voucher" />
                      )}
                    </RadioGroup>
                  </Stack>
                </>
              )}
            </>
          )}
          {/* {myVouchersTransport?.length > 0 &&
            myVouchersTransport?.length > 0 &&
            cartPayment?.statusDelivery === StatusDelivery.InHome && (
              <Divider
                sx={{
                  borderBottomWidth: 5,
                  borderColor: color.primary,
                  marginBottom: 2,
                  width: "100%",
                }}
              />
            )} */}
          {myVouchersPromotion?.length > 0 && (
            <>
              <Typography sx={{ fontSize: 14, fontWeight: 500, mb: 1, mt: 1 }}>
                Mã giảm giá
              </Typography>
              <Stack className="voucher-profile" width={"100%"}>
                <RadioGroup
                  row
                  aria-labelledby="demo-form-control-label-placement"
                  name="position"
                  defaultValue="top"
                >
                  {myVouchersPromotion?.length > 0 ? (
                    myVouchersPromotion?.map((item: VoucherDto) => (
                      <Box
                        sx={{ paddingBottom: 2, width: "100%" }}
                        key={item?.voucherDetails?.[0]?.voucherCode}
                      >
                        <VoucherItem
                          item={item}
                          category={VoucherItemCategory.SELECT}
                          onSelectVoucher={() => handleSelectVoucher(item, "promotion")}
                          isChecked={
                            !!item?.voucherDetails?.[0]?.voucherCode &&
                            Array.isArray(tempVoucherPromotion?.voucherDetails) &&
                            tempVoucherPromotion.voucherDetails.some(
                              (d) => d.voucherCode === item?.voucherDetails?.[0]?.voucherCode
                            )
                          }
                        />
                      </Box>
                    ))
                  ) : (
                    <NoDataView content="Không có voucher" />
                  )}
                </RadioGroup>
              </Stack>
            </>
          )}
        </DialogContent>
        <DialogActions sx={styles.bottomBtnContainer}>
          <Container>
            <Stack width="100%">
              <Divider />
              <Stack sx={styles.bottomBtnContainer} direction="column">
                {/* {filteredVoucherList.length > 0 && (
                  <Box>
                    <Typography>
                      {Array.isArray(selectedVoucher) ? "Một voucher đã được chọn" : "Chỉ chọn một voucher"}
                    </Typography>
                    {tempVoucherTransport && (
                      <Typography style={{ ...commonStyle.headline14, color: appConfig.color.primary }}>
                        Áp dụng mã ưu đãi
                      </Typography>
                    )}
                  </Box>
                )} */}
                <Button
                  style={{
                    ...styles.bottomBtn,
                    background: appConfig.color.primary,
                    color: Array.isArray(selectedVoucher) ? "#fff" : "#fff",
                  }}
                  onClick={Array.isArray(selectedVoucher) ? handleApplyVoucher : handleClose}
                >
                  {Array.isArray(selectedVoucher) ? "Áp dụng" : "Đóng"}
                </Button>
              </Stack>
            </Stack>
          </Container>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentTitle: {
    fontWeight: 500,
    fontSize: 14,
    color: "#828282",
  },
  contentText: {
    color: "#C5C5C5",
    fontWeight: 400,
    fontSize: 12,
    paddingTop: 1,
  },
  topContainer: {
    justifyContent: "center",
    alignItems: "center",
    marginBlock: 1,
    gap: 2,
    width: "100%",
  },
  inputContainer: {
    gap: 2,
    justifyContent: "space-between",
    marginBlock: 2,
    width: "100%",
  },
  applyBtn: {
    color: "white",
    whiteSpace: "nowrap",
    background: COLORS.accent1,
    paddingInline: 16,
  },
  bottomBtnContainer: {
    justifyContent: "space-around",
    alignItems: "center",
    gap: 2,
    marginBlock: 1,
  },
  bottomBtn: {
    margin: 0,
    height: "100%",
    width: "100%",
    paddingBlock: 10,
    borderRadius: 99,
    display: "flex",
    gap: "8px",
    background: COLORS.primary1,
    color: "#fff",
  },
  moreVoucher: {
    ...commonStyle.headline14,
    backgroundColor: COLORS.primary,
    color: "#fff",
    borderRadius: 10,
    minWidth: 150,
  },
};
