import FrameContainer from "../../components/layout/Container";
import React, { useEffect, useState } from "react";
import ProfileHeader from "../Profile/ProfileHeader";
import { Avatar, Box, CircularProgress, Grid, IconButton, Stack, Typography } from "@mui/material";
import { formatPrice } from "../../utils/formatPrice";
import { COLORS, commonStyle } from "../../constants/themes";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { ICommissionReport } from "../../types/commission";
import NoDataView from "../../components/UI/NoDataView";
import { Icon } from "../../constants/Assets";
import dayjs from "dayjs";
import { Clipboard, EmptyWalletAdd, Eye, EyeSlash, WalletTick } from "@/constants/IconSvg";
import { useConfigApp } from "@/hooks/useConfigApp";
import {
  clearListCommissionReports,
  getUserCommissionReport,
} from "@/redux/slices/affiliation/affiliationSlice";
import InfiniteScroll from "@/components/UI/InfiniteScroll";
import { copy } from "@/utils/common";
import FrameContainerFull from "@/components/layout/ContainerFluid";

export default function UserCommission() {
  const { color } = useConfigApp();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const [commissionList, setCommissionList] = useState<ICommissionReport[]>([]);
  // const [loading, setLoading] = useState(false);
  const { affiliateReport, listCommissionReportsPagination, listCommissionReports, isLoading } =
    useSelector((state: RootState) => state.affiliation);
  const [showCommission, setShowCommission] = useState(false);

  // const [skip, setSkip] = useState(0);
  // const [limit, setLimit] = useState(10);
  // const [hasMore, setHasMore] = useState(true);

  const commissionItems = [
    {
      icon: <WalletTick />,
      title: "Hoa hồng đã duyệt",
      type: "total",
    },
    {
      icon: <WalletTick />,
      title: "Hoa hồng đã trả",
      type: "paid",
    },
  ];

  useEffect(() => {
    if (user) {
      getUserCommissionData();
    }
    return () => {
      // Unmount: chạy khi component bị hủy
      dispatch(clearListCommissionReports());
    };
  }, [user]);

  const hasMore =
    listCommissionReportsPagination.skip + listCommissionReportsPagination.limit <
    listCommissionReportsPagination.total;

  const getUserCommissionData = async () => {
    // if (user?.id) {
    // setLoading(true);
    await dispatch(getUserCommissionReport());
    // console.log("🚀 ~ getUserCommissionData ~ response:", response);
    // if (response?.payload?.data) {
    //   setCommissionList(response.payload.data);
    // } else {
    // setCommissionList([
    //   {
    //     id: "1",
    //     attributes: {
    //       userId: 1,
    //       referCode: "referCode",
    //       commission: "123124",
    //       member: "{tenthanhvien}",
    //       month: 12,
    //       year: 2024,
    //       done: false,
    //       tax: "82934759346",
    //       afterTaxAmount: "13500000",
    //       timeMarkDone: "2024-07-31T17:00:00.000Z",
    //     },
    //   },
    //   {
    //     id: "2",
    //     attributes: {
    //       userId: 1,
    //       referCode: "referCode",
    //       commission: "123124",
    //       member: "{tenthanhvien}",
    //       month: 12,
    //       year: 2024,
    //       done: true,
    //       tax: "82934759346",
    //       afterTaxAmount: "13500000",
    //       timeMarkDone: "2024-07-31T17:00:00.000Z",
    //     },
    //   },
    //   {
    //     id: "3",
    //     attributes: {
    //       userId: 1,
    //       referCode: "referCode",
    //       commission: "123124",
    //       member: "{tenthanhvien}",
    //       month: 12,
    //       year: 2024,
    //       done: true,
    //       tax: "82934759346",
    //       afterTaxAmount: "13500000",
    //       timeMarkDone: "2024-07-31T17:00:00.000Z",
    //     },
    //   },
    // ]);
    // }
    // setLoading(false);
    // }
  };
  const curMonth = new Date().getMonth() + 1;
  const curYear = new Date().getFullYear();
  // const curCommission = commissionList.find(
  //   (e) => e.attributes.month === curMonth && e.attributes.year === curYear
  // );
  // const commissionHistory = commissionList.filter(
  //   (e) => e.attributes.month !== curMonth || e.attributes.year !== curYear
  // );
  // const totalCommission = commissionList.reduce(
  //   (tCommission, commission) => tCommission + Number(commission.attributes.commission),
  //   0
  // );
  // const paidCommission = commissionList
  //   .filter((e) => e.attributes.done)
  //   .reduce((tCommission, commission) => tCommission + Number(commission.attributes.commission), 0);

  const fetchMoreProducts = () => {
    dispatch((_, getState) => {
      const state = getState().affiliation;
      if (!state) return;

      dispatch(
        getUserCommissionReport({
          skip:
            state.listCommissionReportsPagination.skip +
            state.listCommissionReportsPagination.limit,
          limit: state.listCommissionReportsPagination.limit,
        })
      );
    });
  };

  const handleCopy = () => {
    copy(user?.referralCode || "", "mã giới thiệu");
  };

  return (
    <FrameContainerFull title="Lịch sử hoa hồng">
      <Stack sx={styles.contentContainer}>
        <Box sx={{ ...styles.container, background: color.primary }}>
          <Stack direction={"row"} alignItems="center" gap={1.5}>
            <Avatar src={user?.avatar} />
            <Stack>
              <Typography sx={{ fontSize: 16, fontWeight: 700, color: COLORS.white }}>
                {user?.fullname}
              </Typography>
              <Stack direction="row" alignItems="center" gap={0.5}>
                <Typography sx={{ fontSize: 14, color: COLORS.white }}>
                  ID: {user?.referralCode}
                </Typography>
                <IconButton onClick={handleCopy} sx={{ p: 0.5 }}>
                  <Clipboard color={COLORS.white} />
                </IconButton>
              </Stack>
            </Stack>
          </Stack>

          <Stack sx={{ mt: 2, color: COLORS.white, alignItems: "center" }}>
            <Stack direction="row" alignItems="center" ml={2}>
              <Typography sx={{ fontSize: 14 }}>Hoa hồng tháng này</Typography>
              <IconButton onClick={() => setShowCommission(!showCommission)}>
                {showCommission ? <Eye color={COLORS.white} /> : <EyeSlash color={COLORS.white} />}
              </IconButton>
            </Stack>
            <Typography
              sx={{
                fontSize: 32,
                fontWeight: 700,
                pb: 1,
                mb: 1,
                lineHeight: 1.2,
              }}
            >
              {showCommission
                ? formatPrice(affiliateReport.thisMonthApprovedCommission || 0)
                : "********"}
            </Typography>
          </Stack>
        </Box>

        <Grid container spacing={2} paddingInline={2}>
          {commissionItems.map((item, index) => (
            <Grid item xs={6} key={String(index)}>
              <Stack alignItems={"center"} justifyItems={"center"} sx={styles.commissionItem}>
                <Stack
                  sx={{
                    width: "100%",
                  }}
                  direction={"row"}
                  alignItems={"center"}
                  gap={1}
                >
                  <Box
                    sx={{
                      ...styles.iconBgrd,
                    }}
                  >
                    {item.icon}
                  </Box>
                  <div>
                    <Typography
                      style={{
                        ...commonStyle.headline12,
                        color: COLORS.neutral2,
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        display: "-webkit-box",
                        WebkitBoxOrient: "vertical",
                      }}
                    >
                      {item.title}
                    </Typography>
                    <Typography
                      style={{
                        ...commonStyle.headline14,
                        color: color.primary,
                      }}
                    >
                      {formatPrice(
                        item.type === "total"
                          ? affiliateReport.pendingCommission + affiliateReport.paidCommission
                          : affiliateReport.totalCommissionReceived
                      )}
                    </Typography>
                  </div>
                </Stack>
              </Stack>
            </Grid>
          ))}
        </Grid>

        <Stack paddingInline={2} bgcolor={"white"} mt={2}>
          <Typography
            style={{
              ...commonStyle.headline17,
              color: color.primary,
              paddingTop: 20,
            }}
          >
            Lịch sử chi trả hoa hồng
          </Typography>

          {isLoading && !listCommissionReports.length ? (
            <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
              <CircularProgress />
            </Stack>
          ) : listCommissionReports.length > 0 ? (
            <InfiniteScroll
              loader={
                <Stack justifyContent={"center"} alignItems={"center"} padding={4}>
                  <CircularProgress />
                </Stack>
              }
              className=""
              fetchMore={fetchMoreProducts}
              hasMore={hasMore}
              endMessage={null}
            >
              {listCommissionReports.map((item, index) => (
                <Stack
                  key={index}
                  style={{
                    ...styles.itemContainer,
                  }}
                >
                  <Typography
                    style={{
                      ...commonStyle.headline15,
                      color: COLORS.neutral2,
                      marginBottom: 12,
                    }}
                  >
                    Chi trả hoa hồng {item.fullName} tháng {item.month}/{item.year}
                  </Typography>
                  <Stack
                    style={{
                      borderLeft: `1px solid #E8E8E8`,
                      paddingLeft: 10,
                    }}
                  >
                    <Typography style={styles.itemText}>
                      Số tiền:{" "}
                      <span
                        style={{
                          ...commonStyle.headline15,
                          color: color.primary,
                        }}
                      >
                        {formatPrice(item.commissionValue)}
                      </span>
                    </Typography>
                    <Typography style={styles.itemText}>
                      Loại tiền: <span>Chuyển khoản</span>
                    </Typography>
                    {item.created && (
                      <Typography style={styles.itemText}>
                        Thời gian thanh toán:{" "}
                        <span>{dayjs(item.created).format("DD/MM/YYYY")}</span>
                      </Typography>
                    )}
                    <Typography style={styles.itemText}>
                      Trạng thái:{" "}
                      <span
                        style={{
                          ...commonStyle.headline15,
                          color: color.primary,
                        }}
                      >
                        Thành công
                      </span>
                    </Typography>
                    <Typography style={styles.itemText}>
                      Ghi chú: <span></span>
                    </Typography>
                  </Stack>
                </Stack>
              ))}
            </InfiniteScroll>
          ) : (
            <NoDataView content="Không có lịch sử hoa hồng" />
          )}
        </Stack>

        {/* {loading ? (
          <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
            <CircularProgress />
          </Stack>
        ) : commissionHistory?.length > 0 ? (
          commissionHistory.map((item, index) => (
            <Stack
              key={index}
              style={{
                ...styles.itemContainer,
              }}
            >
              <Typography
                style={{
                  ...commonStyle.headline15,
                  color: COLORS.neutral2,
                  marginBottom: 12,
                }}
              >
                Chi trả hoa hồng {item.attributes.member} tháng {item.attributes.month}/
                {item.attributes.year}
              </Typography>
              <Stack
                style={{
                  borderLeft: `1px solid #E8E8E8`,
                  paddingLeft: 10,
                }}
              >
                <Typography style={styles.itemText}>
                  Số tiền:{" "}
                  <span
                    style={{
                      ...commonStyle.headline15,
                      color: color.primary,
                    }}
                  >
                    {formatPrice(item.attributes.commission)}
                  </span>
                </Typography>
                <Typography style={styles.itemText}>
                  Loại tiền: <span>Chuyển khoản</span>
                </Typography>
                {item.attributes.timeMarkDone && (
                  <Typography style={styles.itemText}>
                    Thời gian thanh toán:{" "}
                    <span>{dayjs(item.attributes.timeMarkDone).format("DD/MM/YYYY")}</span>
                  </Typography>
                )}
                <Typography style={styles.itemText}>
                  Trạng thái:{" "}
                  <span
                    style={{
                      ...commonStyle.headline15,
                      color: color.primary,
                    }}
                  >
                    {item.attributes.done ? "Thành công" : "Chưa thanh toán"}
                  </span>
                </Typography>
                <Typography style={styles.itemText}>
                  Ghi chú: <span></span>
                </Typography>
              </Stack>
            </Stack>
          ))
        ) : (
          <NoDataView content="Không có lịch sử hoa hồng" />
        )} */}
      </Stack>
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    padding: "16px",
    marginBottom: 2,
  },
  contentContainer: {
    marginTop: 1.25,
    marginBottom: 4,
  },
  commissionItem: {
    ...commonStyle.shadowBorder,
    alignItems: "center",
    justifyItems: "center",
    padding: "10px",
  },
  iconBgrd: {
    background: "#1BAC4B14",
    borderRadius: "50%",
    height: "34px",
    width: "34px",
    minWidth: "34px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  whiteText: {
    color: COLORS.white,
    fontWeight: 700,
  },
  itemContainer: {
    background: COLORS.white,
    color: COLORS.neutral16,
    fontSize: "14px",
    fontWeight: 400,
    padding: "11px 20px 15px",
    marginBlock: 8,
    borderRadius: 10,
  },
};
