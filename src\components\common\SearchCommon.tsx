import {
  FormControl,
  InputAdornment,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useDebounce } from "use-debounce";

export default function SearchCommon({
  placeholder,
  size = "small",
  variant = "outlined",
  isBorder = true,
  iconConfig = {
    start: true,
    end: true,
  },
  iconStart = <SearchIcon />,
  iconEnd = <ClearIcon />,
  setSearchValue,
  onSearch,
}: {
  placeholder: string;
  size?: "small" | "medium";
  variant?: "filled" | "outlined" | "standard";
  isBorder?: boolean;
  iconConfig?: {
    start: boolean;
    end: boolean;
  };
  iconStart?: React.ReactNode,
  iconEnd?: React.ReactNode,
  setSearchValue: React.Dispatch<React.SetStateAction<string>>;
  onSearch?: () => void;
}) {
  const appConfig = useConfigApp();

  const [showClearIcon, setShowClearIcon] = useState("none");
  const [searchKey, setSearchKey] = useState("");

  const [debounceValue] = useDebounce(searchKey, 1000);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    setSearchKey(event.target.value);
  };

  const handleClick = (): void => {
    setShowClearIcon("none");
    setSearchKey("");
  };

  // Xử lý khi nhấn Enter để tìm kiếm
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>): void => {
    if (event.key === 'Enter' && onSearch) {
      onSearch();
    }
  };

  // Xử lý khi click vào icon tìm kiếm
  const handleSearchClick = (): void => {
    if (onSearch) {
      onSearch();
    }
  };

  useEffect(() => {
    setSearchValue(debounceValue);
  }, [debounceValue]);

  return (
    <FormControl>
      <TextField
        fullWidth
        placeholder={placeholder}
        size={size}
        variant={variant}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        value={searchKey}
        className="search-input"
        InputProps={{
          startAdornment: iconConfig.start ? (
            <InputAdornment position="start" onClick={handleSearchClick} style={{ cursor: 'pointer' }}>
              {iconStart}
            </InputAdornment>
          ) : null,
          endAdornment: iconConfig.end ? (
            <InputAdornment
              position="end"
              style={{ display: showClearIcon }}
              onClick={handleClick}
            >
              {iconEnd}
            </InputAdornment>
          ) : null,
        }}
        sx={
          !isBorder
            ? {
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: "transparent", // Hide the border
                },
                "&:hover fieldset": {
                  borderColor: "transparent", // Prevent border on hover
                },
                "&.Mui-focused fieldset": {
                  borderColor: "transparent", // Prevent border when focused
                },
              },
              color: appConfig.textColor.secondary,
            }
            : { color: appConfig.textColor.secondary }
        }
      />
    </FormControl>
  );
}

const styles: Record<string, React.CSSProperties> = {};
