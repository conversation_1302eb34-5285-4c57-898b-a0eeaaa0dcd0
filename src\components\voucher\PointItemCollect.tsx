import React, { useEffect, useState } from "react";
import { IDataCreateVoucherUser } from "@/types/voucher";
import {
  Box,
  Button,
  FormControlLabel,
  Grid,
  Radio,
  Snackbar,
  Stack,
  Typography,
} from "@mui/material";
import { SelectedVoucher, VoucherTypeLabel } from "@/components/voucher/CheckVoucherType";
import { COLOR, COLORS } from "@/constants/themes";
import { formatPrice } from "@/utils/formatPrice";
import dayjs from "dayjs";
import { Router } from "@/constants/Route";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { collectVoucher, setCurVoucher, VoucherDto } from "@/redux/slices/voucher/voucherSlice";
import SmallCheckIcon from "../icon/SmallCheckIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import VoucherAction, { BtnCategory } from "@/components/voucher/ActionVoucher";
import { ButtonHandleVoucherType, ReleaseType, VoucherType } from "@/constants/Const";
import toast from "react-hot-toast";

export enum VoucherItemCategory {
  MYLIST,
  MYDETAIL,
  LIST,
  DETAIL,
  SELECT,
  DIALOG,
}

export default function PointItemCollect({
  item,
  category,
  isShowMatchPoint,
  onSelectVoucher,
  onNavigateToDetail,
  onApplyClick,
  isChecked,
  myVoucherList,
  isDisabled,
}: {
  item: VoucherDto;
  category?: VoucherItemCategory;
  onSelectVoucher?: (item: VoucherDto) => void;
  isShowMatchPoint?: boolean;
  onNavigateToDetail?: () => void;
  isChecked?: boolean;
  onApplyClick?: (item) => void;
  myVoucherList?: VoucherDto[];
  isDisabled?: boolean;
}) {
  const { color, bgColor, textColor } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const appConfig = useConfigApp();

  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const [isItemChecked, setItemChecked] = useState(isChecked || false);

  useEffect(() => {
    setItemChecked(isChecked || false);
  }, [isChecked]);

  const onItemAction = (item) => {
    if (onNavigateToDetail) {
      onNavigateToDetail();
    } else if (onSelectVoucher) {
      onSelectVoucher(item);
    }
  };

  const isHaveInMyVoucher = myVoucherList?.find((voucher) => voucher.voucherId === item.voucherId);

  let btnAction: BtnCategory = BtnCategory.NONE;

  let btnActionLabel = "";
  if (category === VoucherItemCategory.LIST) {
    btnActionLabel = item.exchangePoints
      ? ButtonHandleVoucherType.Exchange
      : ButtonHandleVoucherType.Save;
    btnAction = BtnCategory.BTN;
  } else if (category === VoucherItemCategory.MYLIST) {
    btnActionLabel = ButtonHandleVoucherType.Use;
    btnAction = BtnCategory.BTN;
  } else if (category === VoucherItemCategory.SELECT) {
    btnAction = BtnCategory.RADIO;
  }

  const handleSaveVoucher = async () => {
    try {
      if (!user?.userId) return;
      const voucherData: IDataCreateVoucherUser = {
        voucherId: item?.voucherId,
        shopId: item?.shopId,
      };

      const res = await dispatch(collectVoucher(item?.voucherDetails?.[0]?.voucherCode ?? ""));
      if (res?.payload?.status === 400) {
        toast.error(res?.payload?.detail);
      } else {
        toast.success("Lưu voucher thành công");
        navigate(Router.voucher.index);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  const handleExchangeVoucher = async () => {
    if (item.releaseType === ReleaseType.Free) return;
    if (!user?.point) {
      toast.error("Người dùng chưa có điểm khuyến mại");
      return;
    }
    if (user.point < item.exchangePoints) {
      toast.error("Điểm của người dùng không đủ");
      return;
    }

    const voucherData: IDataCreateVoucherUser = {
      voucherId: item?.voucherId,
      shopId: item?.shopId,
    };

    const res = await dispatch(collectVoucher(item?.voucherDetails?.[0]?.voucherCode ?? ""));
    if (res?.payload?.status === 400) {
      toast.error(res?.payload?.detail);
    } else {
      toast.success("Đổi voucher thành công");
      navigate(Router.voucher.index);
    }
  };

  const onClickVoucher = (event: React.MouseEvent) => {
    event.stopPropagation();

    switch (btnActionLabel) {
      case ButtonHandleVoucherType.Save:
        handleSaveVoucher();
        break;
      case ButtonHandleVoucherType.Exchange:
        handleExchangeVoucher();
        break;
      default:
        onSelectVoucher?.(item);
    }
  };
  const onNavigate = () => {
    const voucherCode = item?.voucherDetails?.[0]?.voucherCode?.toString() ?? "";
    navigate(`${Router.voucher.detail.replace(":code", voucherCode)}`, {
      state: { voucher: item },
    });
  };

  return (
    <Box
      className="wrap"
      key={item.voucherId}
      onClick={() => {
        if (isDisabled) {
          onNavigate();
        } else {
          onItemAction(item);
        }
      }}
      bgcolor={COLOR.bg.primary}
      sx={{
        opacity: isDisabled ? 0.5 : 1,
        cursor: isDisabled ? "not-allowed" : "pointer",
      }}
    >
      <Box className="coupon" style={{ border: `1px soild #000000` }} bgcolor={COLOR.bg.primary}>
        <Stack className="coupon-left" sx={styles.leftContainer} p={1}>
          <Stack
            className="content"
            sx={{ ...styles.leftContent, background: color.primary }}
            borderRadius="2px"
          >
            <Box
              p={1}
              borderRadius={"50%"}
              // bgcolor={color.secondary}
              width={80}
              height={80}
              display={"flex"}
              flexDirection={"column"}
              justifyContent={"center"}
              alignItems={"center"}
              gap={1}
            >
              <img
                width={40}
                src={"/images/zalo.png"}
                style={{ maxHeight: 50, objectFit: "cover" }}
              />
              <Typography fontSize={11} color={COLOR.text.white}>
                Áp dụng trên Zalo app
              </Typography>
            </Box>
          </Stack>
        </Stack>
        <Box
          sx={{
            width: "1px",
            backgroundImage: "linear-gradient(to bottom, #e0e0e0 50%, transparent 50%)",
            backgroundSize: "1px 8px",
            marginY: 2,
          }}
        />
        <Stack className="coupon-con" direction="row" px={1}>
          <Stack sx={styles.rightTop} direction="column" py={1}>
            <Typography
              position={"absolute"}
              top={0}
              right={10}
              fontSize={14}
              fontWeight={700}
              lineHeight={"24px"}
              color={COLOR.text.white}
              bgcolor={color.accent}
              width={40}
              height={24}
              align="center"
              borderRadius={"2px 2px 0 0"}
            >
              {btnAction === BtnCategory.RADIO
                ? `x${item?.maxUsagePerUser}`
                : btnActionLabel !== ButtonHandleVoucherType.Use
                ? `x${item?.maxUsagePerUser}`
                : `x${item.maxUsagePerUser}`}
            </Typography>
            <Box>
              <Button
                variant="outlined"
                sx={styles.btnTopLeft}
                style={{
                  borderColor: color.primary,
                  color: COLOR.text.white,
                  borderRadius: 2,
                  fontSize: 12,
                  backgroundColor: color.accent,
                }}
              >
                Mã tích điểm
              </Button>
              {/* {isShowMatchPoint && (
                <Button
                  variant="outlined"
                  sx={styles.btnTopLeft}
                  style={{
                    borderColor: color.primary,
                    color: color.primary,
                    marginLeft: 3,
                    borderRadius: 2,
                    fontSize: 12,
                    backgroundColor: color.accent,
                  }}
                >
                  {item.exchangePoints} điểm
                </Button>
              )} */}
            </Box>
            <Typography fontWeight={600} fontSize={16}>
              {item.isFixedRewardPoint ? "Tích điểm cố định" : "Tích điểm ngẫu nhiên"}
            </Typography>
            <Typography color={COLOR.text.voucher_code} fontSize={12} fontWeight={500} mb={0.5}>
              #{item?.voucherDetails?.[0]?.voucherCode}
            </Typography>
            <Typography color={COLOR.text.voucher_info} fontSize={11}>
              HSD:{" "}
              {item.isLongTerm !== true
                ? `${dayjs(item?.startDate).format("DD/MM/YYYY")} - ${dayjs(item?.endDate).format(
                    "DD/MM/YYYY"
                  )}`
                : "Không giới hạn"}
            </Typography>
            <Box position={"absolute"} bottom={10} right={10}>
              <VoucherAction
                item={item}
                category={btnAction}
                value={btnActionLabel}
                isChecked={isItemChecked}
                isMyVoucher={isHaveInMyVoucher ? true : false}
                eventAction={onClickVoucher}
                isDisabled={isDisabled}
              />
            </Box>
          </Stack>
        </Stack>
      </Box>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  leftContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  leftContent: {
    textAlign: "center",
    justifyContent: "center",
    alignItems: "center",
    color: COLORS.white,
    gap: 1,
    fontSize: 10,
    height: "100%",
    width: "100%",
  },
  zaloText: {
    width: 50,
    height: 50,
    background: COLORS.white,
    display: "flex",
    alignItems: "center",
    borderRadius: "50%",
    justifyContent: "center",
    margin: "0px auto",
    fontSize: 14,
    fontWeight: 700,
  },
  rightContainer: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    fontSize: 12,
  },
  rightTop: {
    width: "100%",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  typeText: {
    padding: "4px 8px",
    background: COLORS.primary2,
    textAlign: "center",
    borderRadius: 4,
    fontSize: 12,
    marginBottom: 5,
  },
  btnTopLeft: {
    fontSize: 11,
    paddingInline: 1,
    paddingBlock: 0.3,
    fontWeight: 400,
    marginBottom: 0.5,
  },
};
