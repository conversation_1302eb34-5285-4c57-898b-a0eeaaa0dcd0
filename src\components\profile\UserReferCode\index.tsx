import ShareIcon from "@mui/icons-material/Share";
import { <PERSON><PERSON>, <PERSON>rid, <PERSON>ack, Typography } from "@mui/material";
import React, { memo, useState } from "react";
import { useSelector } from "react-redux";
import { QRCode } from "zmp-qrcode";
import { saveImageToGallery } from "zmp-sdk/apis";
import { RootState } from "../../../redux/store";
import { getReferLink, showToast } from "../../../utils/common";

import VerticalAlignBottomIcon from "@mui/icons-material/VerticalAlignBottom";
import { Platform } from "../../../config";
import { useShareReferLink } from "../../../hooks/useShareReferLink";

const UserReferCode = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const referLink = getReferLink(user?.referCode);
  const [src, setSrc] = useState("");
  const { shareLink } = useShareReferLink();

  const downloadQRCode = () => {
    if (!src) return;
    const onSuccess = () => {
      showToast({ content: "Tải mã QR thành công", type: "success" });
    };
    const onError = (error) => {
      showToast({ content: "Tải mã QR thất bại", type: "error" });
      console.log(error);
    };
    if (Platform === "web") {
      try {
        const link = document.createElement("a");
        link.href = src.startsWith("data:image") ? src : `data:image/png;base64,${src}`;
        link.download = "qr-code.png";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        onSuccess();
      } catch (error) {
        onError(error);
      }
    } else {
      saveImageToGallery({
        imageUrl: src,
        success: onSuccess,
        fail: onError,
      });
    }
  };

  return (
    <Stack>
      <Typography textAlign={"center"} pt={6}>
        Chia sẻ mã QR này tới Khách hàng tiềm năng
      </Typography>
      <Stack pt={1}>
        <div
          style={{
            padding: 8,
            display: "flex",
            borderRadius: 8,
            margin: "auto",
            position: "relative",
            background: "white",
          }}
        >
          <QRCode
            size={256}
            value={referLink}
            ref={(el) => (el ? setTimeout(() => setSrc(el.getBase64()), 1000) : el)}
          />
        </div>
      </Stack>
      <Grid
        container
        pt={3}
        pb={3}
        spacing={3}
        direction="row"
        justifyContent={"center"}
        alignItems={"center"}
      >
        {Platform === "zalo" ? (
          <Grid item xs={6}>
            <Button
              style={{
                fontWeight: 400,
                color: "#000",
                borderRadius: 99,
                background: "#fff",
                padding: "10px 16px",
                width: "100%",
              }}
              onClick={downloadQRCode}
              startIcon={<VerticalAlignBottomIcon />}
            >
              Tải xuống
            </Button>
          </Grid>
        ) : null}
        <Grid item xs={6}>
          <Button
            style={{
              fontWeight: 400,
              color: "#000",
              borderRadius: 99,
              background: "#fff",
              padding: "10px 16px",
              width: "100%",
            }}
            startIcon={<ShareIcon />}
            onClick={shareLink}
          >
            Chia sẻ
          </Button>
        </Grid>
      </Grid>
    </Stack>
  );
};

export default memo(UserReferCode);
