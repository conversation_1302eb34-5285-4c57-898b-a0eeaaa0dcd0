import React, { memo, useEffect, useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../redux/store";
import { getUser, updateMe } from "../../redux/slices/authen/authSlice";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { showToast } from "../../utils/common";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { COLORS } from "../../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import CheckIcon from "@/components/icon/CheckIcon";
import { getDistrict, getProvince, getWard } from "@/redux/slices/address/addressSlice";
import { IDistrict, IProvince, IWard } from "@/types/address";
import dayjs from "dayjs";
import "dayjs/locale/vi";

const validationSchema = Yup.object().shape({
  fullname: Yup.string().required("Vui lòng nhập họ tên"),
  birthdate: Yup.string()
    .required("Vui lòng chọn ngày sinh")
    .test("is-future-date", "Không được chọn ngày tương lai", function (value) {
      if (!value) return true;
      return new Date(value) <= new Date();
    })
    .test("is-valid-year", "Năm sinh không hợp lệ", function (value) {
      if (!value) return true;
      return new Date(value).getFullYear() >= 1900;
    }),
  email: Yup.string().email("Email không hợp lệ"),
  provinceId: Yup.string().required("Vui lòng chọn tỉnh"),
  districtId: Yup.string().required("Vui lòng chọn Quận (Huyện)"),
  wardId: Yup.string().required("Vui lòng chọn Phường (Xã)"),
});

type FormData = {
  birthdate: string;
  address: string;
  fullname: string;
  email: string;
  provinceId: string;
  districtId: string;
  wardId: string;
};

const PopupEditProfile = memo(
  ({ open, setOpen }: { open: boolean; setOpen: (status: boolean) => void }) => {
    const { color } = useConfigApp();
    const { showAlert } = useAlert();
    const { province, district, ward } = useSelector((state: RootState) => state.address);
    const dispatch = useDispatch<AppDispatch>();
    useEffect(() => {
      if (open) {
        const getUserInfo = async () => {
          await dispatch(getUser());
        };
        getUserInfo();
      }
    }, [open]);

    const user = useSelector((state: RootState) => state.auth.user);

    const [provinceId, setProvinceId] = useState("");
    const [districtId, setDistrictId] = useState("");
    const [wardId, setWardId] = useState("");

    useEffect(() => {
      if (user && open) {
        setValue("email", user?.email || "");
        setValue("fullname", user?.fullname || "");
        setValue("birthdate", user?.birthdate ? dayjs(user?.birthdate).format("YYYY-MM-DD") : "");
        setValue("address", user?.address || "");

        // Set địa chỉ từ user data
        const userProvinceId = user?.provinceId || "";
        const userDistrictId = user?.districtId || "";
        const userWardId = user?.wardId || "";

        setProvinceId(userProvinceId);
        setDistrictId(userDistrictId);
        setWardId(userWardId);

        setValue("provinceId", userProvinceId);
        setValue("districtId", userDistrictId);
        setValue("wardId", userWardId);
      }
    }, [user, open]);

    const formOptions: any = {
      resolver: yupResolver(validationSchema),
    };

    const { control, handleSubmit, register, reset, watch, setValue } =
      useForm<FormData>(formOptions);
    const submitSetPassword = async (values: any) => {
      const provinceName = province.find((x) => x.provinceID === provinceId)?.provinceName;
      const districtName = district.find((x) => x.districtID === districtId)?.districtName;
      const wardName = ward.find((x) => x.wardID === wardId)?.wardName;
      const data = {
        provinceId: provinceId,
        provinceName: provinceName,
        districtId: districtId,
        districtName: districtName,
        wardId: wardId,
        wardName: wardName,
        address: values.address,
        fullname: values.fullname,
        birthdate: values.birthdate,
        email: values.email,
      };
      const res: any = await dispatch(updateMe(data));
      if (!res.error) {
        setOpen(false);
        reset();
        await dispatch(getUser());
        showAlert({
          icon: <CheckIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
          title: "Cập nhật thông tin thành công",
          content:
            "Bạn đã cập nhật thông tin của bạn đã được cập. Hãy tận hưởng trọn vẹn các đặc quyền dành riêng cho thành viên!",
        });
      } else {
        showToast({
          content: res.error?.message ?? "Quá trình cập nhật lỗi. Vui lòng thử lại",
          type: "error",
        });
      }
    };

    const handleCloseSetPassword = () => {
      setOpen(false);
      reset();
      // Reset address states
      setProvinceId("");
      setDistrictId("");
      setWardId("");
    };

    useEffect(() => {
      if (open) {
        dispatch(getProvince());
      }
    }, [open, dispatch]);

    useEffect(() => {
      if (provinceId && open) {
        dispatch(getDistrict(provinceId));
      }
    }, [provinceId, open, dispatch]);

    useEffect(() => {
      if (districtId && open) {
        dispatch(getWard(districtId));
      }
    }, [districtId, open, dispatch]);

    return (
      <Dialog
        open={open}
        onClose={handleCloseSetPassword}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        maxWidth="sm"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: "12px",
            paddingBottom: "20px",
            margin: "16px",
            maxHeight: "calc(100vh - 32px)",
            width: "calc(100vw - 32px)",
            maxWidth: "500px",
          },
          "& .MuiDialog-container": {
            alignItems: "center",
            justifyContent: "center",
          },
          "@media (max-width: 600px)": {
            "& .MuiDialog-paper": {
              margin: "8px",
              maxHeight: "calc(100vh - 16px)",
              width: "calc(100vw - 16px)",
              borderRadius: "8px",
            },
          },
        }}
      >
        <form onSubmit={handleSubmit(submitSetPassword)}>
          <DialogContent
            sx={{
              padding: "20px 24px",
              overflowY: "auto",
              "@media (max-width: 600px)": {
                padding: "16px",
              },
            }}
          >
            <Stack
              alignItems={"center"}
              gap={0.6}
              style={{
                marginBottom: 12,
              }}
            >
              <Typography style={{ ...styles.title, color: color.primary }}>
                Thông tin cơ bản
              </Typography>
              <Typography style={styles.subTitle}>
                Hãy cập nhật ngay thông tin của bạn để được ưu tiên chăm sóc và tận hưởng trọn vẹn
                các đặc quyền dành riêng cho thành viên!
              </Typography>
            </Stack>
            <Stack gap={2}>
              <Controller
                name="fullname"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    id="outlined-required"
                    label={
                      <span>
                        Họ tên <span style={{ color: "red" }}>*</span>
                      </span>
                    }
                    {...register("fullname")}
                    defaultValue={user?.fullname}
                    sx={{
                      "& .MuiInputBase-root": {
                        ...styles.inputStyle,
                        "&::placeholder": {
                          ...styles.inputPlaceHolder,
                        },
                      },
                    }}
                    error={!!error}
                    helperText={error?.message}
                  />
                )}
              ></Controller>

              <Controller
                name="birthdate"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      label="Ngày sinh nhật"
                      value={field.value ? dayjs(field.value) : null}
                      onChange={(date) => {
                        field.onChange(date ? date.format("YYYY-MM-DD") : "");
                      }}
                      maxDate={dayjs()}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          error: !!error,
                          helperText: error?.message,
                          sx: {
                            "& .MuiInputBase-root": {
                              ...styles.inputStyle,
                              borderRadius: "40px",
                            },
                            "& .MuiInputLabel-root": {
                              color: COLORS.neutral14,
                              fontSize: "14px",
                              "&.Mui-focused": {
                                color: color.primary,
                              },
                            },
                            "& .MuiPickersOutlinedInput-root": {
                              borderRadius: "40px",
                            },
                          },
                        },
                      }}
                    />
                  </LocalizationProvider>
                )}
              ></Controller>

              <Controller
                name="email"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    id="outlined-required"
                    label="Email"
                    defaultValue={user?.email}
                    {...register("email")}
                    sx={{
                      "& .MuiInputBase-root": {
                        ...styles.inputStyle,
                        "&::placeholder": {
                          ...styles.inputPlaceHolder,
                        },
                      },
                    }}
                    error={!!error}
                    helperText={error?.message}
                  />
                )}
              />

              <Controller
                name="address"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    id="outlined-required"
                    label="Địa chỉ"
                    defaultValue={user?.address}
                    {...register("address")}
                    sx={{
                      "& .MuiInputBase-root": {
                        ...styles.inputStyle,
                        "&::placeholder": {
                          ...styles.inputPlaceHolder,
                        },
                      },
                    }}
                  />
                )}
              />

              <Stack direction={"row"} gap={2}>
                <Controller
                  name="provinceId"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <FormControl variant="outlined" fullWidth>
                      <InputLabel id="province-label">Tỉnh</InputLabel>
                      <Select
                        {...field}
                        {...register("provinceId")}
                        sx={{
                          borderRadius: "40px",
                        }}
                        defaultValue={watch("provinceId")}
                        value={provinceId ?? watch("provinceId")}
                        name="provinceId"
                        fullWidth
                        required
                        labelId="province-label"
                        id="province"
                        label="Tỉnh"
                        onChange={(e) => {
                          setProvinceId(e.target.value as string);
                          field.onChange(e);
                          setValue("districtId", "");
                          setValue("wardId", "");
                        }}
                      >
                        {province.map((item: IProvince) => (
                          <MenuItem key={item.provinceID} value={item.provinceID}>
                            {item.provinceName}
                          </MenuItem>
                        ))}
                      </Select>
                      {error && <FormHelperText>{error?.message}</FormHelperText>}
                    </FormControl>
                  )}
                />

                <Controller
                  name="districtId"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <FormControl variant="outlined" fullWidth>
                      <InputLabel id="district-label">Quận (Huyện)</InputLabel>
                      <Select
                        {...field}
                        {...register("districtId")}
                        sx={{
                          borderRadius: "40px",
                        }}
                        defaultValue={watch("districtId")}
                        value={districtId ?? watch("districtId")}
                        name="districtId"
                        fullWidth
                        required
                        labelId="district-label"
                        id="district"
                        label="Quận (Huyện)"
                        onChange={(e) => {
                          setDistrictId(e.target.value);
                          setWardId("");
                          setValue("districtId", e.target.value);
                          setValue("wardId", "");
                        }}
                      >
                        {district?.map((item: IDistrict, index) => (
                          <MenuItem key={item.districtID} value={item.districtID}>
                            {item.districtName}
                          </MenuItem>
                        ))}
                      </Select>
                      {error && <FormHelperText>{error?.message}</FormHelperText>}
                    </FormControl>
                  )}
                />
              </Stack>
              <Controller
                name="wardId"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <FormControl variant="outlined" fullWidth>
                    <InputLabel id="ward-label">Xã (Phường)</InputLabel>
                    <Select
                      {...field}
                      {...register("wardId")}
                      sx={{
                        borderRadius: "40px",
                      }}
                      defaultValue={watch("wardId")}
                      value={wardId ?? watch("wardId")}
                      fullWidth
                      required
                      labelId="ward-label"
                      id="ward"
                      label="Xã (Phường)"
                      onChange={(e) => {
                        setWardId(e.target.value);
                        setValue("wardId", e.target.value);
                      }}
                    >
                      {ward?.map((item: IWard, index) => (
                        <MenuItem key={item.wardID} value={item.wardID}>
                          {item.wardName}
                        </MenuItem>
                      ))}
                    </Select>
                    {error && <FormHelperText>{error?.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Stack>
          </DialogContent>
          <DialogActions
            sx={{
              justifyContent: "center",
              padding: "16px 24px 20px",
              "@media (max-width: 600px)": {
                padding: "16px",
                flexDirection: "column",
              },
            }}
          >
            <Button
              type="submit"
              fullWidth
              sx={{
                ...styles.acceptBtn,
                background: color.primary,
                "@media (max-width: 600px)": {
                  fontSize: "14px",
                  padding: "12px 20px",
                  minHeight: "48px",
                },
              }}
            >
              Cập nhật thông tin cá nhân
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    );
  }
);

export default memo(PopupEditProfile);

const styles: Record<string, React.CSSProperties> = {
  title: {
    color: COLORS.primary,
    fontWeight: 700,
    fontSize: "20px",
  },
  subTitle: {
    color: COLORS.neutral14,
    fontWeight: 400,
    fontSize: "14px",
    textAlign: "center",
  },
  inputStyle: {
    borderRadius: "40px",
  },
  inputPlaceHolder: {
    color: COLORS.black,
    marginLeft: "10px",
  },
  acceptBtn: {
    fontSize: "14px",
    fontWeight: 700,
    color: COLORS.white,
    padding: "7px 20px",
    borderRadius: "40px",
  },
};
