import {
  FormControl,
  InputAdornment,
  Stack,
  TextField,
  Box,
  IconButton,
  Typo<PERSON>,
  Drawer,
  <PERSON>,
  <PERSON>lider,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ToggleButton,
  ToggleButtonGroup,
  Chip,
} from "@mui/material";
import React, { useEffect, useState, useRef } from "react";
import ClearIcon from "@mui/icons-material/Clear";
import ViewListIcon from "@mui/icons-material/ViewList";
import ViewModuleIcon from "@mui/icons-material/ViewModule";
import { useDebounce } from "use-debounce";
import FrameContainerFull from "../../components/layout/ContainerFluid";
import ProductTwoColumn from "../../components/products/ProductTwoColumn";
import ProductOneColumn from "../../components/products/ProductOneColumn";
import { useNavigate } from "react-router-dom";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  getProductListByCategory,
  setSearchCondition,
  setSortBy,
  resetSortBy,
  clearSearchCondition,
  clearProductCache,
} from "@/redux/slices/product/productListSlice";
import { formatPrice } from "@/utils/formatPrice";
import { useConfigApp } from "@/hooks/useConfigApp";
import { FilterIcon, SearchIcon } from "../../constants/IconSvg";

const PAGE_SIZE = 20; // Số sản phẩm mỗi lần gọi API

export default function SearchProduct() {
  const [showClearIcon, setShowClearIcon] = useState("none");
  const [searchKey, setSearchKey] = useState("");
  const [debounceValue] = useDebounce(searchKey, 300);
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { color } = useConfigApp();

  // Ref để theo dõi lần mount đầu tiên
  const isFirstMount = useRef(true);

  // Toggle view state
  const [isOneColumn, setIsOneColumn] = useState(false);

  // Filter drawer state
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Filter states
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [priceRange, setPriceRange] = useState<number[]>([0, 5000000]); // dùng để filter thực sự
  const [priceRangeTemp, setPriceRangeTemp] = useState<number[]>([0, 5000000]); // dùng cho UI Slider và hiển thị giá
  const [sortOrder, setSortOrder] = useState<"asc" | "desc" | null>(null);

  // Product count after filtering
  const [filteredProductCount, setFilteredProductCount] = useState<number>(0);

  // Active filters count
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  // Thêm state và ref cho delay NoData
  const [showNoData, setShowNoData] = useState(false);
  const noDataTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Get data from Redux store
  const { list, isLoading, isInitialLoading, searchCondition, sortBy } = useSelector(
    (state: RootState) => state.productList
  );
  const { productCategoryList } = useSelector((state: RootState) => state.product);
  const { shopId } = useSelector((state: RootState) => state.appInfo);

  // Calculate active filters
  useEffect(() => {
    let count = 0;
    if (selectedCategory) count++;
    if (priceRange[0] > 0 || priceRange[1] < 5000000) count++;
    if (sortOrder !== null) count++;
    setActiveFiltersCount(count);
  }, [selectedCategory, priceRange, sortOrder]);

  // Quản lý phân trang API (lazy load từng đợt nhỏ)
  const [apiSkip, setApiSkip] = useState(0);
  const [apiHasMore, setApiHasMore] = useState(true);

  // Khi shopId hoặc điều kiện search thay đổi, reset lại phân trang
  useEffect(() => {
    setApiSkip(0);
    setApiHasMore(true);
    dispatch(clearProductCache());
  }, [shopId, searchCondition.categoryId, searchCondition.search]);

  // Gọi API từng đợt nhỏ khi mount hoặc khi apiSkip thay đổi
  useEffect(() => {
    if (shopId && apiHasMore) {
      dispatch(
        getProductListByCategory({
          ...searchCondition,
          shopId,
          skip: apiSkip,
          limit: PAGE_SIZE,
          forceReload: apiSkip === 0,
        })
      ).then((res) => {
        const loaded = res?.payload?.products?.length || 0;
        const total = res?.payload?.pagination?.total || 0;
        const displayed = (apiSkip || 0) + loaded;
        if (displayed >= total) {
          setApiHasMore(false);
        }
      });
    }
  }, [shopId, apiSkip, apiHasMore, searchCondition]);

  // Đảm bảo danh mục "Tất cả" hiển thị sản phẩm khi lần đầu load trang
  useEffect(() => {
    if (isFirstMount.current && !isLoading && list.products.length > 0) {
      setFilteredProductCount(list.products.length);
      isFirstMount.current = false;
    }
  }, [isLoading, list.products.length]);

  // Thêm useEffect để delay hiển thị 'Không có sản phẩm'
  useEffect(() => {
    // Giả sử có các biến isInitialLoading, isLoading, list, filteredProductCount
    // Nếu đang loading hoặc có sản phẩm, không hiển thị NoData
    if (isInitialLoading || isLoading || list.products.length > 0 || filteredProductCount > 0) {
      setShowNoData(false);
      if (noDataTimerRef.current) {
        clearTimeout(noDataTimerRef.current);
        noDataTimerRef.current = null;
      }
      return;
    }

    if (
      !isInitialLoading &&
      !isLoading &&
      list.products.length === 0 &&
      filteredProductCount === 0
    ) {
      if (noDataTimerRef.current) clearTimeout(noDataTimerRef.current);
      noDataTimerRef.current = setTimeout(() => {
        setShowNoData(true);
      }, 3000);
    }

    // Cleanup khi unmount
    return () => {
      if (noDataTimerRef.current) clearTimeout(noDataTimerRef.current);
    };
  }, [isInitialLoading, isLoading, list.products.length, filteredProductCount]);

  // Hàm gọi khi scroll tới cuối danh sách (gọi thêm API nếu còn)
  const handleLoadMore = () => {
    console.log("handleLoadMore được gọi khi scroll tới cuối");
    if (apiHasMore && !isLoading) {
      setApiSkip((prev) => prev + PAGE_SIZE);
    }
  };

  // Reset filters when component unmounts
  useEffect(() => {
    return () => {
      // Reset all filters in Redux when leaving the page
      dispatch(clearSearchCondition());
      dispatch(resetSortBy());
    };
  }, [dispatch]);

  // Update filtered product count from child components
  const updateFilteredProductCount = (count: number) => {
    setFilteredProductCount(count);
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    setSearchKey(event.target.value);
  };

  const handleClick = (): void => {
    setSearchKey("");
  };

  const handleBack = () => {
    // Reset filters before navigating back
    dispatch(clearSearchCondition());
    dispatch(resetSortBy());
    navigate(-1);
  };

  const [scrollPositions, setScrollPositions] = useState<{ one: number; two: number }>({
    one: 0,
    two: 0,
  });

  const handleToggleView = () => {
    // Lưu scroll position hiện tại
    const container = document.getElementById("search-product-list-container");
    if (container) {
      setScrollPositions((prev) => ({
        ...prev,
        [isOneColumn ? "one" : "two"]: container.scrollTop,
      }));
    }
    setIsOneColumn((prev) => !prev);
  };

  // Khôi phục scroll position khi chuyển đổi view
  useEffect(() => {
    const container = document.getElementById("search-product-list-container");
    if (container) {
      setTimeout(() => {
        container.scrollTop = scrollPositions[isOneColumn ? "one" : "two"] || 0;
      }, 0);
    }
  }, [isOneColumn]);

  // Sửa hàm toggleFilter để chỉ setIsFilterOpen, không reset filter/searchCondition
  const toggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  // Khi mở Drawer, đồng bộ priceRangeTemp với priceRange
  useEffect(() => {
    if (isFilterOpen) {
      setPriceRangeTemp(priceRange);
    }
  }, [isFilterOpen, priceRange]);

  const handlePriceChange = (event: Event, newValue: number | number[]) => {
    setPriceRangeTemp(newValue as number[]); // update UI và giá hiển thị
  };

  // Không filter khi kéo, chỉ filter khi bấm Áp dụng
  const handlePriceChangeCommitted = (
    event: Event | React.SyntheticEvent,
    newValue: number | number[]
  ) => {
    // Không làm gì ở đây để tránh lag
  };

  const handleSortChange = (
    event: React.MouseEvent<HTMLElement>,
    newSortOrder: "asc" | "desc" | null
  ) => {
    if (sortOrder === newSortOrder) return;

    setSortOrder(newSortOrder);
  };

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId === selectedCategory ? null : categoryId);
  };

  // Đảm bảo các hàm applyFilters và clearFilters chỉ được gọi khi user bấm nút tương ứng, không gọi khi chỉ đóng Drawer.
  const applyFilters = () => {
    // Khi bấm Áp dụng, cập nhật filter thực sự
    setPriceRange(priceRangeTemp);
    const priceChanged =
      priceRangeTemp[0] !== (searchCondition.minPrice || 0) ||
      priceRangeTemp[1] !== (searchCondition.maxPrice || 10000000);

    const categoryChanged = selectedCategory !== searchCondition.categoryId;

    const sortChanged = sortOrder === null || sortOrder !== null;

    if (!priceChanged && !categoryChanged && !sortChanged) {
      setIsFilterOpen(false);
      return;
    }

    const updatedSearchCondition = {
      ...searchCondition,
      search: searchKey,
      categoryId: selectedCategory,
      minPrice: priceRangeTemp[0],
      maxPrice: priceRangeTemp[1],
      priceFilter: true,
      skip: 0,
      limit: PAGE_SIZE, // Use PAGE_SIZE for filtering
    };

    const sortByObj: { field: string; order: "asc" | "desc" | "default" } = sortOrder
      ? { field: "price", order: sortOrder }
      : { field: "", order: "default" };

    dispatch(setSearchCondition(updatedSearchCondition));
    dispatch(setSortBy(sortByObj));

    dispatch(
      getProductListByCategory({
        ...updatedSearchCondition,
        forceReload: true, // Buộc tải lại dữ liệu từ server
      })
    );

    setIsFilterOpen(false);
  };

  const clearFilters = () => {
    const hasActiveFilters =
      selectedCategory !== null ||
      priceRange[0] > 0 ||
      priceRange[1] < 5000000 ||
      sortOrder !== null;

    if (!hasActiveFilters) {
      setIsFilterOpen(false);
      return;
    }

    const clearedFilters = {
      selectedCategory: null,
      priceRange: [0, 5000000],
      sortOrder: null,
    };

    setSelectedCategory(clearedFilters.selectedCategory);
    setPriceRange(clearedFilters.priceRange);
    setPriceRangeTemp(clearedFilters.priceRange);
    setSortOrder(clearedFilters.sortOrder);

    const updatedSearchCondition = {
      ...searchCondition,
      search: searchKey,
      categoryId: clearedFilters.selectedCategory,
      minPrice: undefined,
      maxPrice: undefined,
      priceFilter: false,
      skip: 0,
      limit: PAGE_SIZE, // Use PAGE_SIZE for clearing filters
    };

    const sortByObj: { field: string; order: "asc" | "desc" | "default" } = {
      field: "",
      order: "default",
    };

    // Cập nhật trạng thái trong Redux
    dispatch(setSearchCondition(updatedSearchCondition));
    dispatch(setSortBy(sortByObj));

    dispatch(
      getProductListByCategory({
        ...updatedSearchCondition,
        // shopId,
        forceReload: true, // Buộc tải lại dữ liệu từ server
      })
    );

    setIsFilterOpen(false);
  };

  // Search when debounced search key changes
  useEffect(() => {
    if (debounceValue !== undefined) {
      const updatedSearchCondition = {
        ...searchCondition,
        search: debounceValue,
        skip: 0,
        limit: PAGE_SIZE, // Use PAGE_SIZE for search
      };

      // Chỉ gọi API nếu từ khóa tìm kiếm thay đổi
      if (searchCondition.search !== debounceValue) {
        // Cập nhật điều kiện tìm kiếm trong Redux
        dispatch(setSearchCondition(updatedSearchCondition));

        // Gọi API chỉ với tham số tìm kiếm cơ bản
        // Lọc và sắp xếp được xử lý ở client
        dispatch(
          getProductListByCategory({
            ...updatedSearchCondition,
            // shopId,
          })
        );
      }
    }
  }, [debounceValue, shopId]);

  return (
    <FrameContainerFull
      title={"Tìm kiếm nâng cao"}
      overrideStyle={{ backgroundColor: "transparent" }}
      // leftIcon={<ArrowBackIcon onClick={handleBack} />}
    >
      <Box
        sx={{
          width: "100%",
          height:
            "calc(100vh - 120px - var(--zaui-safe-area-inset-bottom, 0px) - var(--zaui-safe-area-inset-top, 0px))",
          background: "unset",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Box sx={{ padding: "10px 12px" }}>
          <Box
            sx={{
              display: "flex",
              gap: 1,
              alignItems: "center",
              marginBottom: 2,
            }}
          >
            <FormControl fullWidth>
              <TextField
                placeholder="Tìm kiếm sản phẩm"
                size="small"
                variant="outlined"
                onChange={handleChange}
                value={searchKey}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: "#999" }} />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end" sx={{ display: "flex", alignItems: "center" }}>
                      {showClearIcon !== "none" && (
                        <IconButton onClick={handleClick} size="small" sx={{ mr: 0.5 }}>
                          <ClearIcon fontSize="small" sx={{ color: "#999" }} />
                        </IconButton>
                      )}
                      <Badge
                        badgeContent={activeFiltersCount}
                        color="primary"
                        sx={{
                          "& .MuiBadge-badge": {
                            fontSize: "10px",
                            height: "16px",
                            minWidth: "16px",
                          },
                        }}
                      >
                        <IconButton
                          onClick={toggleFilter}
                          size="small"
                          sx={{
                            padding: "4px",
                            "&:hover": {
                              backgroundColor: "#eee",
                            },
                          }}
                        >
                          <FilterIcon fillColor="#666666" />
                        </IconButton>
                      </Badge>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "10px",
                    backgroundColor: "white",
                    boxShadow: "0px 0px 5px 1px #00000015",
                    "& fieldset": {
                      borderColor: "#eee",
                    },
                    "&:hover fieldset": {
                      borderColor: "#ddd",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: color.primary,
                    },
                  },
                  "& .MuiInputBase-input": {
                    padding: "10px 0",
                    fontSize: "14px",
                  },
                }}
              />
            </FormControl>
          </Box>

          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "flex-start",
              marginBottom: 0,
            }}
          >
            <Box sx={{ display: "flex", flexDirection: "column", flex: 1 }}>
              <Typography variant="body2" sx={{ color: "#666" }}>
                {showNoData
                  ? "Không có sản phẩm"
                  : list.products.length > 0 && list.pagination.total > 0
                  ? `${list.pagination.total} sản phẩm`
                  : filteredProductCount > 0
                  ? `${filteredProductCount} sản phẩm`
                  : null}
                {isLoading && !isInitialLoading && (
                  <span style={{ marginLeft: 8 }}>- Đang tải thêm...</span>
                )}
              </Typography>
              {searchKey && searchKey.trim().length > 0 && (
                <Typography variant="caption" sx={{ color: "#888", mt: 0.5 }}>
                  Tìm kiếm được thực hiện trên toàn bộ dữ liệu ở máy chủ
                </Typography>
              )}
            </Box>

            <IconButton
              onClick={handleToggleView}
              sx={{
                color: color.primary,
                backgroundColor: "unset",
                borderRadius: "8px",
                padding: "0",
                ml: 1,
                "&:hover": {
                  backgroundColor: "#eee",
                },
              }}
            >
              {isOneColumn ? <ViewModuleIcon /> : <ViewListIcon />}
            </IconButton>
          </Box>
        </Box>

        <Box
          id="search-product-list-container"
          sx={{
            flex: 1,
            overflow: "auto",
            padding: "0 10px",
            height: "80vh",
            "&::-webkit-scrollbar": {
              display: "none",
            },
            msOverflowStyle: "none",
            scrollbarWidth: "none",
            display: isFilterOpen ? "none" : "block",
          }}
        >
          <Box
            style={{
              display: isOneColumn ? "block" : "none",
              height: "100%",
            }}
          >
            <ProductOneColumn
              onUpdateCount={updateFilteredProductCount}
              onLoadMore={handleLoadMore}
              hasMore={apiHasMore}
            />
          </Box>
          <Box
            style={{
              display: !isOneColumn ? "block" : "none",
              height: "100%",
            }}
          >
            <ProductTwoColumn
              onUpdateCount={updateFilteredProductCount}
              onLoadMore={handleLoadMore}
              hasMore={apiHasMore}
            />
          </Box>
        </Box>
      </Box>

      {/* Filter Drawer */}
      <Drawer
        anchor="bottom"
        open={isFilterOpen}
        onClose={toggleFilter}
        PaperProps={{
          sx: {
            paddingTop: "16px", // Decreased from 56px since we're coming from bottom
            paddingBottom: "32px",

            paddingX: "16px",
            overflowY: "auto",
            borderTopLeftRadius: "16px",
            borderTopRightRadius: "16px",
            boxShadow: "0px -4px 10px rgba(0, 0, 0, 0.1)",
            "&::-webkit-scrollbar": {
              display: "none",
            },
            msOverflowStyle: "none",
            scrollbarWidth: "none",
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Bộ lọc
          </Typography>
          <IconButton
            onClick={toggleFilter}
            sx={{ backgroundColor: "#f5f5f5", borderRadius: "8px" }}
          >
            <ClearIcon />
          </IconButton>
        </Box>

        <Divider sx={{ mb: 2 }} />

        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
          Danh mục
        </Typography>
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: 1,
            mb: 2,
            maxWidth: "100%",
            overflowX: "auto",
            padding: "4px 0",
            "&::-webkit-scrollbar": {
              display: "none",
            },
            msOverflowStyle: "none",
            scrollbarWidth: "none",
          }}
          className="hide-scrollbar"
        >
          {productCategoryList
            .filter((cat) => cat.parentId === null)
            .map((category) => (
              <Chip
                key={category.categoryId}
                label={category.categoryName}
                onClick={() => handleCategoryChange(category.categoryId)}
                sx={{
                  backgroundColor:
                    selectedCategory === category.categoryId ? `${color.primary}20` : "transparent",
                  border: `1px solid ${
                    selectedCategory === category.categoryId ? color.primary : "#ddd"
                  }`,
                  color: selectedCategory === category.categoryId ? color.primary : "#666",
                  fontWeight: selectedCategory === category.categoryId ? 600 : 400,
                  borderRadius: "16px",
                  padding: "8px 4px",
                  height: "32px",
                  "&:hover": {
                    backgroundColor:
                      selectedCategory === category.categoryId ? `${color.primary}30` : "#f5f5f5",
                  },
                }}
              />
            ))}
        </Box>

        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1, mt: 2 }}>
          Khoảng giá
        </Typography>
        <Box sx={{ px: 2, mt: 3, mb: 4 }}>
          <Slider
            value={priceRangeTemp}
            onChange={handlePriceChange}
            onChangeCommitted={handlePriceChangeCommitted}
            valueLabelDisplay="off"
            min={0}
            max={5000000}
            step={10000}
            sx={{
              color: color.primary,
              "& .MuiSlider-thumb": {
                height: 16,
                width: 16,
                backgroundColor: "#fff",
                border: `2px solid ${color.primary}`,
                "&:focus, &:hover, &.Mui-active, &.Mui-focusVisible": {
                  boxShadow: `0px 0px 0px 8px ${color.primary}20`,
                },
              },
              "& .MuiSlider-rail": {
                height: 4,
              },
              "& .MuiSlider-track": {
                height: 4,
              },
            }}
          />
          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {formatPrice(priceRangeTemp[0])}
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {formatPrice(priceRangeTemp[1])}
            </Typography>
          </Box>
        </Box>

        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
          Sắp xếp theo giá
        </Typography>
        <ToggleButtonGroup
          value={sortOrder}
          exclusive
          onChange={handleSortChange}
          aria-label="sort order"
          sx={{
            display: "flex",
            width: "100%",
            mb: 3,
            "& .MuiToggleButtonGroup-grouped": {
              border: `1px solid ${color.primary}40 !important`,
              borderRadius: "8px !important",
              mx: 0.5,
            },
          }}
        >
          <ToggleButton
            value="asc"
            aria-label="ascending"
            sx={{
              flex: 1,
              py: 1,
              color: sortOrder === "asc" ? "#fff" : color.primary,
              backgroundColor: sortOrder === "asc" ? color.primary : "transparent",
              "&:hover": {
                backgroundColor: sortOrder === "asc" ? color.primary : `${color.primary}10`,
              },
              "&.Mui-selected": {
                backgroundColor: color.primary,
                color: "#fff",
                "&:hover": {
                  backgroundColor: color.primary,
                },
              },
              borderRadius: "8px",
            }}
          >
            Giá thấp đến cao
          </ToggleButton>
          <ToggleButton
            value="desc"
            aria-label="descending"
            sx={{
              flex: 1,
              py: 1,
              color: sortOrder === "desc" ? "#fff" : color.primary,
              backgroundColor: sortOrder === "desc" ? color.primary : "transparent",
              "&:hover": {
                backgroundColor: sortOrder === "desc" ? color.primary : `${color.primary}10`,
              },
              "&.Mui-selected": {
                backgroundColor: color.primary,
                color: "#fff",
                "&:hover": {
                  backgroundColor: color.primary,
                },
              },
              borderRadius: "8px",
            }}
          >
            Giá cao đến thấp
          </ToggleButton>
        </ToggleButtonGroup>

        <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
          <Button
            variant="outlined"
            fullWidth
            onClick={clearFilters}
            sx={{
              py: 1.5,
              borderRadius: "8px",
              borderColor: "#ddd",
              color: "#666",
              "&:hover": {
                borderColor: "#bbb",
                backgroundColor: "#f5f5f5",
              },
            }}
          >
            Xóa bộ lọc
          </Button>
          <Button
            variant="contained"
            fullWidth
            onClick={applyFilters}
            sx={{
              py: 1.5,
              borderRadius: "8px",
              backgroundColor: color.primary,
              "&:hover": {
                backgroundColor: color.primary,
                opacity: 0.9,
              },
            }}
          >
            Áp dụng
          </Button>
        </Box>
      </Drawer>
    </FrameContainerFull>
  );
}
