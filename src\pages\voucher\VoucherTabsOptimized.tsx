import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import { Box, Tabs, Tab, TextField, InputAdornment, Typography } from "@mui/material";
import { Search } from "@mui/icons-material";
import { AppDispatch, RootState } from "@/redux/store";
import { COLORS } from "@/constants/themes";
import { PAGE_SIZE, RewardType, StatusDelivery, VoucherType } from "@/constants/Const";
import { Router } from "@/constants/Route";
import { useInfiniteScroll } from "@/hooks/useInfiniteScroll";
import InfiniteScrollContainer from "@/components/UI/InfiniteScrollContainer";
import VoucherItem, { VoucherItemCategory } from "@/components/voucher/VourcherItem";
import { Icon } from "@/constants/Assets";
import { useNavigate } from "@/utils/component-util";
import { useCart } from "@/hooks/useCart";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import { showToast } from "@/utils/common";
import LoginPopup from "@/components/LoginPopup";
import {
  VoucherDto,
  GetVoucherByShopParams,
  getListVoucherByUser,
  getListVoucherByShop,
  getListVoucherByShopPublic,
  collectVoucherPoint,
  incrementUserVoucherCount,
} from "@/redux/slices/voucher/voucherSlice";
import { getUser, updateUserPoint } from "@/redux/slices/authen/authSlice";

function TabPanel({
  children,
  value,
  index,
}: {
  children: React.ReactNode;
  value: number;
  index: number;
}) {
  return (
    <div role="tabpanel" hidden={value !== index}>
      {value === index && (
        <Box
          sx={{
            p: { xs: 1, sm: 2 },
            width: "100%",
            boxSizing: "border-box",
          }}
        >
          {children}
        </Box>
      )}
    </div>
  );
}

export default function VoucherTabsOptimized() {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { cartPayment, setCartAndSave } = useCart();
  const { showAlert } = useAlert();
  const { checkLogin, openLoginPopup, setOpenLoginPopup, onClickRegister, loading } =
    useCheckLogin();

  const [searchParams, setSearchParams] = useSearchParams();
  const tabParam = searchParams.get("tab");
  const initialTab = tabParam ? parseInt(tabParam, 10) : 0;
  const [activeTab, setActiveTab] = useState(initialTab);
  const [searchText, setSearchText] = useState("");

  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { user } = useSelector((state: RootState) => state.auth);

  // Fetch function for vouchers by shop
  const fetchVouchersByShop = async (params: GetVoucherByShopParams) => {
    // Use public API if user is not logged in, otherwise use authenticated API
    const response = user
      ? await dispatch(getListVoucherByShop(params))
      : await dispatch(getListVoucherByShopPublic(params));

    return {
      result: response.payload?.result?.result || response.payload?.result || [],
      total: response.payload?.total || 0,
      pageIndex: params.PageIndex,
    };
  };

  // Fetch function for vouchers by user
  const fetchVouchersByUser = async (params: GetVoucherByShopParams) => {
    const response = await dispatch(getListVoucherByUser(params));
    return {
      result: response.payload?.result || [],
      total: response.payload?.total || 0,
      pageIndex: params.PageIndex,
    };
  };

  // Infinite scroll for shop vouchers (tab 0)
  const shopVouchers = useInfiniteScroll<VoucherDto>({
    fetchFunction: fetchVouchersByShop,
    pageSize: PAGE_SIZE,
    initialParams: {
      shopId,
      Search: searchText,
    },
    enabled: activeTab === 0 && !!shopId,
    debounceMs: 500,
    onError: (error) => {
      console.error("Error loading shop vouchers:", error);
    },
  });

  // Infinite scroll for user vouchers (tab 1)
  const userVouchers = useInfiniteScroll<VoucherDto>({
    fetchFunction: fetchVouchersByUser,
    pageSize: PAGE_SIZE,
    initialParams: {
      shopId,
      Search: searchText,
    },
    enabled: activeTab === 1 && !!shopId,
    debounceMs: 500,
    onError: (error) => {
      console.error("Error loading user vouchers:", error);
    },
  });

  // Update params when search text changes
  useEffect(() => {
    if (activeTab === 0) {
      shopVouchers.updateParams({
        shopId,
        Search: searchText,
      });
    } else {
      userVouchers.updateParams({
        shopId,
        Search: searchText,
      });
    }
  }, [searchText, shopId, activeTab]);

  // Refresh shop vouchers when user authentication status changes
  useEffect(() => {
    if (activeTab === 0 && shopId) {
      shopVouchers.refresh();
    }
  }, [user]);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setSearchParams((prev) => {
      const params = new URLSearchParams(prev);
      params.set("tab", newValue.toString());
      return params;
    });
  };

  // Handle search change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(event.target.value);
  };

  // Clear search
  const clearSearch = () => {
    setSearchText("");
  };

  // Get current voucher data based on active tab
  const currentVoucherData = activeTab === 0 ? shopVouchers : userVouchers;

  // Handle voucher selection
  const handleSelectVoucher = async (voucher: VoucherDto) => {
    if (voucher.voucherType == VoucherType.Custom && voucher.rewardType == RewardType.Point) {
      await collectVoucher(voucher);
      return;
    }

    checkLogin(async () => {
      try {
        if (cartPayment.listItems.length > 0) {
          const voucherCode = voucher?.voucherDetails?.[0]?.voucherCode;
          if (voucher.voucherType === VoucherType.Transport) {
            if (cartPayment.statusDelivery === StatusDelivery.InHome) {
              await setCartAndSave({
                ...cartPayment,
                voucherTransport: [voucher],
                voucherCodes: voucherCode ? [voucherCode] : [],
              });
            } else {
              await setCartAndSave({
                ...cartPayment,
                statusDelivery: StatusDelivery.InHome,
                voucherTransport: [voucher],
                voucherCodes: voucherCode ? [voucherCode] : [],
              });
            }
          } else {
            await setCartAndSave({
              ...cartPayment,
              voucherPromotion: [voucher],
              voucherCodes: voucherCode ? [voucherCode] : [],
            });
          }
          navigate(Router.cartPayment);
        } else {
          showToast({
            content: "Vui lòng chọn sản phẩm trước khi áp dụng mã giảm giá",
            type: "error",
          });
        }
      } catch (error: any) {
        if (error.status === 400) {
          navigate(Router.menu);
        }
      }
    });
  };

  // Collect voucher
  const collectVoucher = async (voucher: VoucherDto) => {
    const voucherCode = voucher?.voucherDetails?.[0]?.voucherCode;
    if (!voucherCode) return;

    checkLogin(async () => {
      const res = await dispatch(collectVoucherPoint(voucherCode));

      if (res?.payload?.success) {
        currentVoucherData.refresh();
        // Increment user voucher count when successfully collected
        dispatch(incrementUserVoucherCount());
        if (res?.payload?.data?.newPoint !== undefined) {
          dispatch(updateUserPoint(res.payload.data.newPoint));
        } else {
          await dispatch(getUser());
        }
        showAlert({
          title: "Chúc mừng",
          content: res?.payload?.message,
          buttons: [
            {
              title: "Đóng",
            },
          ],
        });
      }
    });
  };

  // Render voucher item
  const renderVoucherItem = (voucher: VoucherDto, _index: number) => (
    <VoucherItem
      key={voucher.voucherId}
      item={voucher}
      category={activeTab === 0 ? VoucherItemCategory.LIST : VoucherItemCategory.MYLIST}
      onNavigateToDetail={() => {
        const voucherCode = voucher?.voucherDetails?.[0]?.voucherCode?.toString() ?? "";
        navigate(`${Router.voucher.detail.replace(":code", voucherCode)}`, {
          state: { voucher: voucher },
        });
      }}
      onSelectVoucher={() => handleSelectVoucher(voucher)}
      fetchVouchers={() => {
        currentVoucherData.refresh();
      }}
    />
  );

  // Render empty state
  const renderEmpty = () => (
    <Box
      sx={{
        textAlign: "center",
        py: 3,
        gap: 2,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <img width="70px" src={Icon.sad} alt="No data" />
      <Typography color={COLORS.neutral6} fontWeight={500}>
        {searchText ? "Không tìm thấy voucher nào" : "Không có voucher nào!"}
      </Typography>
    </Box>
  );

  return (
    <Box sx={{ width: "100%" }}>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
          <Tab label="Ưu đãi" />
          <Tab label="Ưu đãi của bạn" />
        </Tabs>
      </Box>

      <Box
        sx={{
          paddingInline: { xs: 1, sm: 2 },
          paddingTop: 2,
          width: "100%",
          boxSizing: "border-box",
        }}
      >
        <TextField
          fullWidth
          size="small"
          placeholder="Tìm kiếm voucher..."
          value={searchText}
          onChange={handleSearchChange}
          disabled={currentVoucherData.isLoading}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: searchText && (
              <InputAdornment position="end" sx={{ cursor: "pointer" }} onClick={clearSearch}>
                ✕
              </InputAdornment>
            ),
            sx: {
              borderRadius: 2,
              backgroundColor: COLORS.white,
            },
          }}
        />
      </Box>

      <TabPanel value={activeTab} index={0}>
        <InfiniteScrollContainer
          data={shopVouchers.data}
          isLoading={shopVouchers.isLoading}
          isLoadingMore={shopVouchers.isLoadingMore}
          hasMore={shopVouchers.hasMore}
          error={shopVouchers.error}
          observerRef={shopVouchers.observerRef}
          onRetry={shopVouchers.refresh}
          renderItem={renderVoucherItem}
          renderEmpty={renderEmpty}
          containerProps={{
            sx: {
              display: "flex",
              flexDirection: "column",
              gap: 1,
              width: "100%",
              overflow: "hidden",
            },
          }}
        />
      </TabPanel>

      <TabPanel value={activeTab} index={1}>
        <InfiniteScrollContainer
          data={userVouchers.data}
          isLoading={userVouchers.isLoading}
          isLoadingMore={userVouchers.isLoadingMore}
          hasMore={userVouchers.hasMore}
          error={userVouchers.error}
          observerRef={userVouchers.observerRef}
          onRetry={userVouchers.refresh}
          renderItem={renderVoucherItem}
          renderEmpty={renderEmpty}
          containerProps={{
            sx: {
              display: "flex",
              flexDirection: "column",
              gap: 1,
              width: "100%",
              overflow: "hidden",
            },
          }}
        />
      </TabPanel>

      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={loading}
      />
    </Box>
  );
}
