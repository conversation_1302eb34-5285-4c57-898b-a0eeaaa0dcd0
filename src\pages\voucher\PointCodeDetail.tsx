import PopupCommon from "@/components/common/PopupCommon";
import CheckIcon from "@/components/icon/CheckIcon";
import Frame<PERSON>ontainerFull from "@/components/layout/ContainerFluid";
import PointItemCollect from "@/components/voucher/PointItemCollect";
import { VoucherItemCategory } from "@/components/voucher/VourcherItem";
import { Router } from "@/constants/Route";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { getUser, updateUserPoint } from "@/redux/slices/authen/authSlice";
import { getProductCategoryList, getProductDetailById } from "@/redux/slices/product/productSlice";
import {
  getMyVoucherList,
  RedeemPointsFromPointVoucherCode,
  setCurVoucher,
} from "@/redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { Button, Stack, Typography } from "@mui/material";
import React, { useEffect } from "react";
import toast from "react-hot-toast";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";

export default function PointCodeDetail() {
  const [itemsOfVoucher, setItemsOfVoucher] = React.useState<string[]>([]);
  const [itemsCategoryOfVoucher, setItemsCategoryOfVoucher] = React.useState<string>("");
  const [openSuccessModal, setOpenSuccessModal] = React.useState(false);
  const [awardedPoints, setAwardedPoints] = React.useState<number | null>(null);
  const [promotionName, setPromotionName] = React.useState<string>("");

  const appConfig = useConfigApp();
  const { state } = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { myVoucherUserList } = useSelector((state: RootState) => state.vouchers);
  const voucher = state?.voucher;
  const { showAlert } = useAlert();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { user } = useSelector((state: RootState) => state.auth);
  const { checkLogin } = useCheckLogin();
  const bannerUri =
    voucher.banner?.formats?.thumbnails?.url ||
    voucher.banner?.formats?.small?.url ||
    voucher.banner?.url ||
    undefined;

  const data = { shopId: shopId, userId: user?.userId };

  useEffect(() => {
    dispatch(getMyVoucherList(data));
  }, []);

  const isHaveInMyVoucher = myVoucherUserList?.find((item) => item.voucherId === voucher.voucherId);
  const { color } = useConfigApp();

  const hasUsed = voucher?.hasUsed === true;
  const isExpired = voucher?.isExpired === true;
  const btnText = hasUsed ? "Đã sử dụng" : isExpired ? "Đã hết hạn" : "Nhận điểm";

  const onChangePointToVoucher = async () => {
    try {
      if (!user?.userId) return;

      const response = await dispatch(RedeemPointsFromPointVoucherCode(voucher.voucherCode));

      if (response.meta.requestStatus === "fulfilled") {
        // Get awarded points from response if available
        let points = null;
        if (response.payload && typeof response.payload === "object") {
          const payloadAny = response.payload as any;
          points =
            payloadAny.PointsAwarded ?? payloadAny.pointsAwarded ?? payloadAny.points ?? null;
          setPromotionName(payloadAny.PromotionName ?? payloadAny.promotionName ?? null);
        }
        setAwardedPoints(points);
        setOpenSuccessModal(true);
        if (points !== null && user?.point !== undefined) {
          const newPoint = user.point + points;
          dispatch(updateUserPoint(newPoint));
        } else {
          await dispatch(getUser());
        }
      } else if (response.payload && typeof response.payload === "object") {
        toast.error((response.payload as any).detail || "Lỗi khi nhận điểm");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi nhận điểm");
      console.error(error);
    }
  };

  const onApplyClick = () => {
    checkLogin(() => {
      showAlert({
        title: "Nhận điểm",
        content: "Bạn có muốn nhận điểm từ mã này không?",
        icon: (
          <CheckIcon
            primaryColor={appConfig.color.primary}
            secondaryColor={appConfig.bgColor.primary}
          />
        ),
        buttons: [
          {
            title: "Huỷ",
          },
          {
            title: "Xác nhận",
            action: onChangePointToVoucher,
          },
        ],
      });
    });
  };

  const fetListItemOfVoucher = async () => {
    if (voucher?.productIds.length > 0) {
      const results = await Promise.all(
        voucher.productIds.map(async (itemId) => {
          const res = await dispatch(getProductDetailById(itemId));
          return res?.payload?.itemsName;
        })
      );
      setItemsOfVoucher(results);
    }
  };

  const fetListItemCategoryOfVoucher = async () => {
    try {
      const res = await dispatch(getProductCategoryList()).unwrap();
      if (!res?.data) {
        return;
      }
      const filteredItems = res.data.filter((item: any) => item.categoryId === voucher.categoryId);
      setItemsCategoryOfVoucher(filteredItems[0].categoryName);
    } catch (error) {}
  };

  useEffect(() => {
    fetListItemOfVoucher();
    fetListItemCategoryOfVoucher();
  }, [voucher]);
  return (
    <>
      <FrameContainerFull
        title="Chi tiết mã giảm giá"
        overrideStyle={{
          background: color.secondary,
          height: "100vh",
        }}
      >
        {voucher.banner ? (
          voucher.banner?.formats ? (
            <img
              style={styles.banner}
              src={
                bannerUri
                  ? `${import.meta.env.VITE_API_URL}${bannerUri}`
                  : "/demo/images/banner.png"
              }
            />
          ) : (
            <video muted loop controls style={{ width: "100%", aspectRatio: 1 }}>
              <source src={`${import.meta.env.VITE_API_URL}${voucher?.banner?.url}`} />
            </video>
          )
        ) : (
          <img style={styles.banner} src={"/demo/images/banner.png"} />
        )}
        <Stack marginInline={2} marginTop={-7}>
          {voucher && (
            <Stack className="voucher-profile">
              <PointItemCollect item={voucher} category={VoucherItemCategory.DETAIL} />
            </Stack>
          )}
          <Stack p={1.6} marginBlock={0}>
            <Typography
              style={{
                ...styles.titleStyle,
                color: appConfig.color.primary,
              }}
            >
              Chương trình tích điểm
            </Typography>
            <Typography>
              Với voucher này bạn có thể nhận được số điểm ngẫu nhiên trên hệ thống sau khi nhấn
              nhận điểm. Điểm thưởng sẽ được cộng vào tài khoản tích điểm của bạn, bạn dùng điểm để
              mua sắm sản phẩm hoặc quy đổi voucher giảm giá khác hoặc đổi quà theo chương trình từ
              Goldgi tại mục Reward
            </Typography>
          </Stack>
          {itemsCategoryOfVoucher && (
            <Stack p={1.6} marginBlock={0}>
              <Typography
                style={{
                  ...styles.titleStyle,
                  color: appConfig.color.primary,
                }}
              >
                Áp dụng cho danh mục sản phẩm
              </Typography>
              <Typography>
                Chỉ áp dụng cho loại sản phẩm:{" "}
                <Typography sx={{ fontWeight: 600, display: "inline" }}>
                  {itemsCategoryOfVoucher}
                </Typography>
              </Typography>
            </Stack>
          )}
          {itemsOfVoucher.length > 0 && (
            <Stack p={1.6} marginBlock={0}>
              <Typography
                style={{
                  ...styles.titleStyle,
                  color: appConfig.color.primary,
                }}
              >
                Áp dụng cho sản phẩm
              </Typography>
              <Typography>
                Chỉ áp dụng cho một số sản phẩm nhất định:{" "}
                <Typography sx={{ fontWeight: 600, display: "inline" }}>
                  {itemsOfVoucher.join(", ")}
                </Typography>
              </Typography>
            </Stack>
          )}
        </Stack>
        <Stack className="bottomElementPreventScroll" style={styles.bottomBtnContainer}>
          <Button
            variant="contained"
            size="small"
            style={{
              ...styles.bottomBtn,
              backgroundColor: hasUsed || isExpired ? "#bdbdbd" : appConfig.color.primary,
              color: hasUsed || isExpired ? "#757575" : undefined,
              cursor: hasUsed || isExpired ? "not-allowed" : undefined,
            }}
            disabled={hasUsed || isExpired}
            onClick={() => {
              if (hasUsed || isExpired) return;
              if (isHaveInMyVoucher) {
                dispatch(setCurVoucher(voucher));
                navigate(`${Router.menu}`);
              } else {
                onApplyClick();
              }
            }}
          >
            {btnText}
          </Button>
        </Stack>
      </FrameContainerFull>
      <PopupCommon
        open={openSuccessModal}
        setOpen={setOpenSuccessModal}
        title={
          <img
            src="/images/GiftOpening.svg"
            alt="Gift Opening"
            style={{
              width: 140,
              height: 140,
              marginBottom: 8,
              marginTop: 8,
              background: "#fff",
              borderRadius: 16,
            }}
          />
        }
        content={
          <Stack alignItems="center" spacing={1} sx={{ width: 320, px: 2 }}>
            <Typography
              sx={{
                fontSize: 32,
                fontWeight: 700,
                color: color.primary,
                mb: 0.5,
                mt: 0,
                lineHeight: 1.1,
                letterSpacing: 0,
              }}
            >
              {awardedPoints ?? 0} điểm
            </Typography>
            <Typography
              align="center"
              sx={{
                color: "#1D1D1DAD",
                fontSize: 16,
                fontWeight: 400,
                mb: 0.5,
                lineHeight: 1.5,
              }}
            >
              Xin chúc mừng <b style={{ fontWeight: 700 }}>{user?.fullname || user?.name || ""}</b>{" "}
              đã nhận được {awardedPoints ?? 0} điểm thưởng từ chương trình{" "}
              <b style={{ fontWeight: 700 }}>{promotionName || ""}</b> của{" "}
              <b style={{ fontWeight: 700 }}>{appConfig?.shopName || "NTT"}</b>
            </Typography>
            <Typography
              align="center"
              sx={{
                color: "#2673DD",
                fontSize: 15,
                fontWeight: 400,
                mt: 0.5,
                mb: 0.5,
                lineHeight: 1.3,
              }}
            >
              Chúc bạn một ngày đầy hứng khởi!{" "}
              <span role="img" aria-label="heart">
                💙
              </span>
            </Typography>
          </Stack>
        }
        action={
          <Stack direction="row" spacing={2} justifyContent="center" sx={{ mt: 4, mb: 2 }}>
            <Button
              variant="contained"
              sx={{
                minWidth: 140,
                borderRadius: 999,
                fontWeight: 700,
                fontSize: 16,
                background: color.primary,
                color: "#fff",
                boxShadow: "none",
                px: 3,
                py: 1.2,
                "&:hover": { background: "#151A30" },
                textTransform: "none",
              }}
              onClick={() => {
                setOpenSuccessModal(false);
                navigate(Router.menu);
              }}
            >
              Mua sắm ngay
            </Button>
            <Button
              variant="outlined"
              sx={{
                minWidth: 140,
                borderRadius: 999,
                fontWeight: 700,
                fontSize: 16,
                color: color.primary,
                borderColor: color.primary,
                px: 3,
                py: 1.2,
                boxShadow: "none",
                "&:hover": { borderColor: color.primary, color: color.primary },
                textTransform: "none",
              }}
              onClick={() => {
                setOpenSuccessModal(false);
                navigate(Router.voucher.index);
              }}
            >
              Xem chi tiết
            </Button>
          </Stack>
        }
      />
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  banner: {
    objectFit: "cover",
    width: "100%",
    aspectRatio: 7 / 4,
  },
  bottomBtnContainer: {
    position: "absolute",
    bottom: "calc(var(--zaui-safe-area-inset-bottom, 0px) + 64px)",
    width: "100%",
    maxWidth: 450,
    zIndex: 100,
  },
  bottomBtn: {
    marginLeft: 11,
    width: "95%",
    padding: 12,
    fontSize: 17,
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: 700,
    marginBottom: "10px",
  },
};
