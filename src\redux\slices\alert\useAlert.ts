import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import { ICustomAlert, setAlert } from "./alert";

export const useAlert = () => {
  const { alert } = useSelector((state: RootState) => state.alert);

  const dispatch = useDispatch<AppDispatch>();

  const showAlert = (alert: ICustomAlert | null) => {
    const newAlert: ICustomAlert = { ...alert, isShow: true };
    dispatch(setAlert(newAlert));
  };

  const hideAlert = () => {
    const newAlert: ICustomAlert = { ...alert, isShow: false };
    dispatch(setAlert(newAlert));
  };

  return {
    showAlert,
    hideAlert,
  };
};
