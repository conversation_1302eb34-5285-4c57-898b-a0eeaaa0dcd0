import * as React from "react";

const GetPointIcon: React.FC<React.SVGProps<SVGSVGElement> & { fill?: string }> = ({
  fill = "#1531AD",
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="66"
    height="66"
    fill="none"
    viewBox="0 0 66 66"
    {...props}
  >
    <rect width="66" height="66" fill={fill} fillOpacity="0.08" rx="33"></rect>
    <path
      fill={fill}
      d="M33 46.6c-7.52 0-13.6-6.08-13.6-13.6S25.48 19.4 33 19.4 46.6 25.48 46.6 33 40.52 46.6 33 46.6M33 21c-6.64 0-12 5.36-12 12s5.36 12 12 12 12-5.36 12-12-5.36-12-12-12"
    ></path>
    <path fill={fill} d="M25.8 32.2h14.4v1.6H25.8z"></path>
    <path fill={fill} d="M32.2 25.8h1.6v14.4h-1.6z"></path>
  </svg>
);

export default GetPointIcon;
