import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IProductCategory } from "../../../types/product";
import { IOrder, IOrderSearchCondition } from "../../../types/order";
import { OrderStatusType } from "@/constants/Const";

interface OrderState {
  list: IOrder[];
  orderDetail: IOrder | null;
  orderList: IOrder[];
  isLoading: boolean;
  orderSearchCondition: IOrderSearchCondition;
}

const initialState: OrderState = {
  list: [],
  orderDetail: null,
  orderList: [],
  orderSearchCondition: {
    orderStatus: OrderStatusType.All,
  },
  isLoading: false,
};

export const createOrderOnServer = createAsyncThunk(
  "order/createOrder",
  async (payload, { rejectWithValue }) => {
    try {
      const response: any = await request("post", "/api/user/orderuser/createorder");
      return response;
    } catch (error) {
      const err = error as any;
      return rejectWithValue(err?.response?.data?.detail || err?.detail || "Có lỗi xảy ra");
    }
  }
);

export const trackingPayment = createAsyncThunk(
  "order/trackingPayment",
  async (data: any, { rejectWithValue }) => {
    try {
      const response: any = await request("post", "/api/request-logs", {
        data: {
          body: data,
        },
      });

      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const createMac = createAsyncThunk(
  "order/createMac",
  async (data: any, { rejectWithValue }) => {
    try {
      const response: any = await request("post", "/api/user/orderuser/createmac", {
        ...data,
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const cancelOrder = createAsyncThunk(
  "order/cancelOrder",
  async (orderId: any, { rejectWithValue }) => {
    try {
      const response: any = await request("put", `/api/user/orderuser/updateorder`, {
        orderId,
        updateAction: "CancelOrder",
      });

      return response;
    } catch (error) {
      return error;
    }
  }
);

export const getAllOrder = createAsyncThunk("order/getAllOrder", async () => {
  const response: any = await request("get", "/api/user/orderuser/listorder");
  return response;
});

export const getOrderByOrderStatus = createAsyncThunk(
  "order/getOrderByOrderStatus",
  async (StatusOrder: string) => {
    const response: any = await request("get", "/api/user/orderuser/listorder", {
      StatusOrder,
    });

    return response;
  }
);

export const getTotalStatusOrder = createAsyncThunk(
  "orderuser/totalstatus",
  async () => {
    let url = `/api/user/orderuser/totalstatus`;
    const response: any = await request("get", url);
    return response;
  }
);

export const getOrderDetail = createAsyncThunk("order/getOrderDetail", async (id: string) => {
  const response: any = await request("get", "/api/user/orderuser/orderdetail", { orderId: id });
  return response;
});

const orderSlice = createSlice({
  name: "order",
  initialState,
  reducers: {
    clearOrderSearchCondition: (state) => {
      state.orderSearchCondition = initialState.orderSearchCondition;
    },
    setOrderSearchCondition: (state, action: PayloadAction<IOrderSearchCondition>) => {
      state.orderSearchCondition = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createOrderOnServer.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createOrderOnServer.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        state.isLoading = false;
      })
      .addCase(createOrderOnServer.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getAllOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllOrder.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;

        state.orderDetail = payload.data?.map((data) => ({
          id: data.id,
          ...data.attribute,
        }));
        state.isLoading = false;
      })
      .addCase(getAllOrder.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getOrderByOrderStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrderByOrderStatus.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.orderList = [...payload.data];
        state.isLoading = false;
      })
      .addCase(getOrderByOrderStatus.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getOrderDetail.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrderDetail.fulfilled, (state, action: PayloadAction<IOrder>) => {
        const { payload } = action;
        state.orderDetail = payload;
        state.isLoading = false;
      })
      .addCase(getOrderDetail.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(cancelOrder.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        state.isLoading = false;
      });
  },
});

export const { setOrderSearchCondition, clearOrderSearchCondition } = orderSlice.actions;

export default orderSlice.reducer;
