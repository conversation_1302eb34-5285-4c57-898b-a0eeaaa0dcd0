import { <PERSON>, <PERSON>ton, CircularProgress, Stack, Typography } from "@mui/material";
import React, { useState } from "react";
import { COLORS } from "@/constants/themes";
import { AppDispatch, RootState } from "@/redux/store";
import { useDispatch, useSelector } from "react-redux";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { authZalo, getUser, getUserZalo } from "@/redux/slices/authen/authSlice";
import { mapError } from "@/utils/common";
import CheckIcon from "@/components/icon/CheckIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import PopupCommon from "@/components/common/PopupCommon";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import { Platform } from "@/config";
import { ZaloIcon } from "@/constants/IconSvg";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { StorageKeys } from "@/constants/storageKeys";
import { getItem } from "@/utils/storage";

export default function ActiveProfile() {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  const { showAlert } = useAlert();
  const dispatch = useDispatch<AppDispatch>();
  const calcCartAfterLogin = useCalcCartAfterLogin();
  const [loading, setLoading] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);

  const onClickRegister = async () => {
    setLoading(true);
    const refCode = await getItem(StorageKeys.RefCode);
    const res = await dispatch(authZalo(refCode)).unwrap();
    if (res && res.idFor) {
      if (Platform == "zalo") await dispatch(getUserZalo());
      await dispatch(getUser());
      await calcCartAfterLogin();
      showAlert({
        icon: (
          <CheckIcon
            primaryColor={appConfig.color.primary}
            secondaryColor={appConfig.color.secondary}
          />
        ),
        title: "Kích hoạt tài khoản thành công",
      });
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoading(false);
  };
  return (
    <>
      <Stack style={styles.activeAccount}>
        {loading ? (
          <CircularProgress />
        ) : (
          <>
            <Button
              variant="contained"
              style={{
                ...styles.activeAccountBtn,
                backgroundColor: appConfig.color.primary,
              }}
              onClick={() => (Platform === "zalo" ? onClickRegister() : setOpenPopup(true))}
            >
              Kích hoạt tài khoản
            </Button>
            <Typography variant="body2" textAlign={"center"} pt={2}>
              Bằng việc bấm "Kích hoạt tài khoản", chúng tôi hiểu rằng bạn đã đồng ý với điều khoản
              của {appConfig.shopName}.
            </Typography>
          </>
        )}
      </Stack>
      <PopupCommon
        open={openPopup}
        setOpen={setOpenPopup}
        content={
          <>
            <Typography fontSize={22} fontWeight={700} color={COLORS.black} mb={1} align="center">
              Yêu cầu đăng nhập
            </Typography>
            <Typography
              fontSize={14}
              align="center"
              // color="textSecondary"
              style={{ color: "#A5A5A5" }}
              mb={3}
              px={2}
            >
              Để sử dụng chức năng này, bạn cần đăng nhập để sử dụng
            </Typography>
            <Box display="flex" flexDirection="column" gap={1}>
              {Platform == "web" && (
                <Button
                  variant="contained"
                  startIcon={<AccountCircleIcon />}
                  color="primary"
                  style={{
                    color: appConfig.color.accent,
                    backgroundColor: appConfig.color.primary,
                    paddingBlock: 10,
                    height: 60,
                    fontWeight: 500,
                    fontSize: 14,
                  }}
                  fullWidth
                  onClick={() => navigate(`${Router.login}`)}
                >
                  Đăng nhập với tài khoản
                </Button>
              )}
              {Platform === "zalo" && (
                <Button
                  variant="outlined"
                  startIcon={<ZaloIcon />}
                  style={{
                    color: appConfig.color.primary,
                    backgroundColor: appConfig.color.accent,
                    paddingBlock: 10,
                    height: 60,
                    fontWeight: 500,
                    fontSize: 14,
                  }}
                  fullWidth
                  onClick={onClickRegister}
                >
                  Đăng nhập với Zalo
                </Button>
              )}
            </Box>
            {Platform === "web" && (
              <Typography align="center" color="textSecondary" fontSize={11} mt={1} mb={3}>
                Bạn chưa có tài khoản?{" "}
                <Typography
                  component="span"
                  style={{
                    cursor: "pointer",
                    textDecoration: "underline",
                    color: appConfig.color.primary,
                  }}
                  onClick={() => navigate(`${Router.register}`)}
                >
                  Đăng ký
                </Typography>
              </Typography>
            )}
          </>
        }
      />
    </>
  );
}
const styles: Record<string, React.CSSProperties> = {
  activeAccount: {
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    height: 500,
    borderRadius: 20,
  },
  activeAccountBtn: {
    color: COLORS.white,
  },
  sectionContainer: {
    background: "#fff",
    padding: 0,
    borderRadius: 10,
    color: "#000",
  },
  accountItem: {
    padding: 2,
    justifyContent: "space-between",
    alignItems: "center",
  },
  logoutBtn: {
    fontWeight: 400,
    color: "#000",
    borderRadius: 99,
    background: "#fff",
    padding: "10px 16px",
    justifyContent: "center",
    alignItems: "center",
  },
};
