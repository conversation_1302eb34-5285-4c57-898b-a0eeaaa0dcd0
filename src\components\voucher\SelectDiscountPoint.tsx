import React, { memo, useEffect, useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import {
  Box,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { COLORS, theme } from "../../constants/themes";
import { Icon } from "../../constants/Assets";
import { formatPrice } from "../../utils/formatPrice";

type FormData = {
  amount: number | null;
};

interface ISelectDiscountPoint {
  selectedDiscountPoint: (points: number) => void;
  initPoint: number;
}

const SelectDiscountPoint = memo(
  ({ selectedDiscountPoint, initPoint }: ISelectDiscountPoint) => {
    const { user } = useSelector((state: RootState) => state.auth);
    const [open, setOpen] = useState(false);
    const [selectedPoint, setSelectedPoint] = useState(0);
    const validationSchema = Yup.object().shape({
      amount: Yup.number()
        .required("Vui lòng nhập số điểm tích luỹ muốn sử dụng")
        .transform((value) => (Number.isNaN(value) ? null : value))
        .nullable()
        .max(user?.bonusPoint || 0, "Vượt quá số điểm tích luỹ bạn có"),
    });

    const formOptions = { resolver: yupResolver(validationSchema) };

    const { handleSubmit, register, formState, reset } =
      useForm<FormData>(formOptions);
    const { errors } = formState;

    const handleClosePopup = () => {
      setOpen(false);
      reset();
    };

    const handleClickOpen = () => {
      setOpen(true);
    };

    useEffect(() => {
      setSelectedPoint(initPoint);
    }, [initPoint]);

    const onSelectDiscountPoint = (values) => {
      const realDiscountPoint =
        initPoint > 0 && initPoint < values.amount ? initPoint : values.amount;
      setSelectedPoint(realDiscountPoint);
      selectedDiscountPoint(values.amount);
      handleClosePopup();
    };

    return (
      <Box>
        {selectedPoint ? (
          <Stack
            direction="row"
            justifyContent={"space-between"}
            onClick={handleClickOpen}
          >
            <Stack direction="row" gap={3}>
              <img src={Icon.voucher} />
              <Stack>
                <Typography style={styles.contentTitle}>
                  Đã lựa chọn quy đổi {selectedPoint} điểm
                </Typography>
                <Typography style={styles.contentText}>
                  Áp dụng giảm giá trực tiếp đơn hàng
                </Typography>
              </Stack>
            </Stack>
            <IconButton>
              <KeyboardArrowRightIcon />
            </IconButton>
          </Stack>
        ) : (
          <Stack
            direction="row"
            justifyContent={"space-between"}
            onClick={handleClickOpen}
          >
            <Stack direction="row" gap={3}>
              <img src={Icon.voucher} />
              <Stack>
                <Typography style={styles.contentTitle}>
                  Số dư ví điểm ({formatPrice(`${user?.bonusPoint}`)})
                </Typography>
                <Typography style={styles.contentText}>
                  Áp dụng giảm giá trực tiếp đơn hàng
                </Typography>
              </Stack>
            </Stack>
            <IconButton>
              <KeyboardArrowRightIcon />
            </IconButton>
          </Stack>
        )}
        <Dialog
          open={open}
          onClose={handleClosePopup}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <form onSubmit={handleSubmit(onSelectDiscountPoint)}>
            <DialogContent>
              <Stack gap={2}>
                <Typography style={styles.titleStyle}>
                  Sử dụng điểm tích luỹ
                </Typography>
                <Typography style={styles.titleStyle}>
                  Số điểm khả dụng: {formatPrice(`${user?.bonusPoint}`)}
                </Typography>
                <Stack direction={"row"} justifyContent="center" width={"100%"}>
                  <img src={Icon.icon_withdrawal_money} />
                </Stack>
                <TextField
                  style={{ width: 200 }}
                  type="Number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">đ</InputAdornment>
                    ),
                  }}
                  inputProps={{ inputMode: "numeric" }}
                  required
                  id="outlined-required"
                  label="Nhập số điểm tích luỹ"
                  {...register("amount")}
                />
                {errors.amount && (
                  <Typography
                    style={{ width: 200, color: theme.palette.error.main }}
                  >
                    {errors.amount.message}
                  </Typography>
                )}
              </Stack>
            </DialogContent>
            <DialogActions>
              <Button
                style={styles.firstBtnSyle}
                onClick={handleClosePopup}
                variant="contained"
              >
                Huỷ
              </Button>
              <Button type="submit" style={styles.secondBtnSyle}>
                Xác nhận
              </Button>
            </DialogActions>
          </form>
        </Dialog>
      </Box>
    );
  }
);

export default memo(SelectDiscountPoint);

const styles: Record<string, React.CSSProperties> = {
  contentTitle: {
    fontWeight: 700,
    color: COLORS.neutral2,
  },
  contentText: {
    color: COLORS.neutral5,
    paddingTop: 4,
  },
  titleStyle: {
    color: theme.palette.primary.main,
    fontWeight: 700,
    marginBottom: 2,
    textAlign: "center",
  },
  firstBtnSyle: {
    background: "#E8E8EA",
    color: "#666666",
    boxShadow: "none",
    width: "50%",
    marginInline: 8,
    marginBottom: 8,
  },
  secondBtnSyle: {
    background: COLORS.accent1,
    color: "#FFFFFF",
    boxShadow: "none",
    width: "50%",
    marginInline: 8,
    marginBottom: 8,
  },
};
