import { StorageKeys } from "@/constants/storageKeys";
import { request } from "@/utils/request";
import { getItem } from "@/utils/storage";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

interface MembershipLevelState {
  configPoint: any | null;
  ranks: any[];
  isLoading: boolean;
  error: string | null;
}

const initialState: MembershipLevelState = {
  configPoint: null,
  ranks: [],
  isLoading: false,
  error: null,
};

export const fetchConfigPoint = createAsyncThunk(
  "membershiplevel/fetchConfigPoint",
  async (params: { shopId: string }, { rejectWithValue }) => {
    try {
      const { shopId } = params;
      const token = await getItem(StorageKeys.AccessToken);
      const response: any = await request(
        "get",
        `/api/user/membershipleveluser/configpoint/${shopId}`,
        undefined,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Error fetching config point");
    }
  }
);  

export const fetchMembershipRanks = createAsyncThunk(
  "membershiplevel/fetchMembershipRanks",
  async (params: { shopId: string; skip?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const { shopId, skip = 0, limit = 10 } = params;
      const token = await getItem(StorageKeys.AccessToken);
      const response: any = await request(
        "get",
        `/api/user/membershipleveluser/ranks/${shopId}`,
        { skip, limit },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Error fetching membership ranks");
    }
  }
);

const membershiplevelSlice = createSlice({
  name: "membershiplevel",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchConfigPoint.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchConfigPoint.fulfilled, (state, action: PayloadAction<any>) => {
        state.configPoint = action.payload?.data || null;
        state.isLoading = false;
      })
      .addCase(fetchConfigPoint.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchMembershipRanks.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMembershipRanks.fulfilled, (state, action: PayloadAction<any>) => {
        state.ranks = action.payload?.data || [];
        state.isLoading = false;
      })
      .addCase(fetchMembershipRanks.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export default membershiplevelSlice.reducer;
