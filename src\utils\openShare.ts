import { openShareSheet as openShareSheetZalo } from "zmp-sdk/apis";
import { Platform } from "../config";
import { copy, isMobile } from "./common";

export function openShareSheet(link: string) {
  if (Platform === "zalo") {
    openShareSheetZalo({
      type: "link",
      data: {
        link,
        chatOnly: false,
      },
    });
  } else {
    if (isMobile()) {
      navigator.share({
        title: "Share",
        text: "Select to share",
        url: link,
      });
    } else {
      copy(link, "link giới thiệu");
    }
  }
}
