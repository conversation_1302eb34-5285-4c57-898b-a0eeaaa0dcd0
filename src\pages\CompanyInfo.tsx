import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Typography, useMediaQuery } from "@mui/material";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { AppDispatch, RootState } from "../redux/store";
import { useAlert } from "../redux/slices/alert/useAlert";
import { authZalo, getUser, getUserZalo, updateMe } from "../redux/slices/authen/authSlice";
import { Icon } from "../constants/Assets";
import { mapError } from "../utils/common";
import { OPEN_TIME } from "../constants";
import { handleFollowOA } from "../utils/followOA";
import { Router } from "../constants/Route";
import { COLORS } from "../constants/themes";
import { getBranchList } from "../redux/slices/branch/branchSlice";
import ClockIcon from "../components/icon/ClockIcon";
import CheckIcon from "../components/icon/CheckIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Platform } from "@/config";
import { StorageKeys } from "@/constants/storageKeys";
import { getItem } from "@/utils/storage";

const CompanyInfo = () => {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { list } = useSelector((state: RootState) => state.branch);
  const { color } = useConfigApp();
  const { showAlert } = useAlert();
  const isSmall = useMediaQuery("(max-width:385px)");
  const firstItem = list.length > 0 ? list.filter((item) => item?.id === 1)[0]?.attributes : null;
  const URLImage = appConfig.shopLogo?.data?.attributes?.url;

  useEffect(() => {
    if (user) {
      dispatch(getBranchList());
    }
  }, [user]);

  const onClickRegister = async (onFollowOA?: () => Promise<void>) => {
    const refCode = await getItem(StorageKeys.RefCode);
    const res = await dispatch(authZalo(refCode)).unwrap();
    if (res && res.idFor) {
      if (Platform == "zalo") await dispatch(getUserZalo());
      await dispatch(getUser());

      showAlert({
        icon: <CheckIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
        title: "Kích hoạt tài khoản thành công",
        buttons: [
          {
            title: "OK",
            action: onFollowOA,
          },
        ],
      });
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
  };

  const onClickFollowOA = () => {
    if (!user) {
      showAlert({
        icon: <CheckIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
        title: `Cảm ơn bạn đã quan tâm OA ${appConfig?.shopName}`,
        titleStyle: {
          color: color.primary,
        },
        contentStyle: {
          color: color.primary,
        },
        content: `Vui lòng kích hoạt tài khoản để tiếp tục.`,
        buttons: [
          {
            title: "Đóng",
          },
          {
            title: "Kích hoạt",
            action: async () => {
              await onClickRegister(() =>
                handleFollowOA(appConfig.oaId || "", onUpdateAfterFollowOA)
              );
            },
          },
        ],
      });
    } else {
      handleFollowOA(appConfig.oaId || "", onUpdateAfterFollowOA);
    }
  };

  const onUpdateAfterFollowOA = async () => {
    const res = await dispatch(updateMe({ isZaloOA: true })).unwrap();
    if (res) {
      showAlert({
        icon: <CheckIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
        title: `Cảm ơn bạn đã quan tâm OA ${appConfig?.shopName}`,
        content: `Mời bạn mua sắm và trải nghiệm dịch vụ của ${appConfig?.shopName}.`,
        buttons: [
          {
            title: "Xem sản phẩm",
            action: () => {
              navigate(Router.menu);
            },
          },
        ],
      });
      dispatch(getUser());
    }
  };

  return (
    <Box>
      <Stack
        sx={{
          boxShadow: "0px 0px 10px 0px rgba(217, 217, 217, 0.50);",
          paddingX: 1,
          paddingY: 2,
          borderRadius: 3,
          backgroundColor: COLORS.white,
          marginBottom: 2,
        }}
      >
        <Stack
          direction="row"
          gap={2}
          alignContent="center"
          justifyContent="center"
          alignItems="center"
          // sx={{
          //   boxShadow: "0px 0px 10px 0px rgba(217, 217, 217, 0.50);",
          //   paddingX: 1,
          //   paddingY: 2,
          //   borderRadius: 3,
          //   backgroundColor: COLORS.white,
          //   marginBottom: 2,
          // }}
        >
          {/* <img width={90} height={90} src="/images/logo.png" /> */}
          <img
            width={isSmall ? 40 : 90}
            height={isSmall ? 40 : 90}
            style={{ borderRadius: "50%" }}
            src={`${import.meta.env.VITE_API_URL}${URLImage}`}
          />
          <Stack direction="column" gap={1}>
            {appConfig?.shopInfo && (
              <Box
                style={styles.description}
                dangerouslySetInnerHTML={{
                  __html: appConfig?.shopInfo,
                }}
              />
            )}
            <Stack
              direction="row"
              alignContent="center"
              justifyContent="space-between"
              alignItems="center"
              gap="4px"
              // display={isSmall ? "none" : "flex"}
            >
              <Stack
                direction="row"
                gap="4px"
                style={{
                  alignItems: "center",
                }}
              >
                <Stack
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginRight: 3,
                  }}
                >
                  <ClockIcon fillColor={color.primary} />
                </Stack>
                <Box
                  sx={{
                    backgroundColor: color.primary,
                    border: `1px solid ${color.primary}`,
                    color: COLORS.white,
                    padding: "1px 6px",
                    borderRadius: "5px",
                    fontSize: "12px",
                  }}
                >
                  {appConfig.openTime?.slice(0, 5) || OPEN_TIME.start}
                </Box>
                <Box>-</Box>
                <Box
                  sx={{
                    backgroundColor: color.primary,
                    border: `1px solid ${color.primary}`,
                    color: COLORS.white,
                    padding: "1px 6px",
                    borderRadius: "5px",
                    fontSize: "12px",
                  }}
                >
                  {appConfig.closeTime?.slice(0, 5) || OPEN_TIME.end}
                </Box>
              </Stack>
              <Stack>
                <Button
                  onClick={onClickFollowOA}
                  style={{
                    ...styles.OABtnFooter,
                    fontSize: "12px",
                    backgroundColor: color.secondary,
                    color: color.accent,
                  }}
                >
                  Quan tâm
                </Button>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
        {/* <Stack
          direction="row"
          alignContent="center"
          justifyContent="space-between"
          alignItems="center"
          gap="4px"
          marginTop={"5px"}
          display={isSmall ? "flex" : "none"}
        >
          <Stack
            direction="row"
            gap="4px"
            style={{
              alignItems: "center",
            }}
          >
            <Stack
              style={{
                display: "flex",
                alignItems: "center",
                marginRight: 2,
              }}
            >
              <img src={Icon.icon_clock} />
            </Stack>
            <Box
              sx={{
                backgroundColor: COLORS.primary,
                border: `1px solid ${COLORS.primary}`,
                color: COLORS.white,
                padding: "3px 6px",
                borderRadius: "5px",
                fontSize: "11px",
              }}
            >
              {firstItem?.timeOpen || OPEN_TIME.start}
            </Box>
            <Box>-</Box>
            <Box
              sx={{
                backgroundColor: COLORS.primary,
                border: `1px solid ${COLORS.primary}`,
                color: COLORS.white,
                padding: "3px 6px",
                borderRadius: "5px",
                fontSize: "11px",
              }}
            >
              {firstItem?.timeClose || OPEN_TIME.end}
            </Box>
          </Stack>
          <Stack>
            <Button
              onClick={onClickFollowOA}
              style={{
                ...styles.OABtnFooter,
                fontSize: "11px",
              }}
            >
              Quan tâm
            </Button>
          </Stack>
        </Stack> */}
      </Stack>
    </Box>
  );
};

export default CompanyInfo;

const styles: Record<string, React.CSSProperties> = {
  OABtn: {
    color: "#FFF",
    borderRadius: 5,
    fontSize: 12,
    width: 100,
  },
  OABtnFooter: {
    background: COLORS.primary2,
    color: "#FFF",
    borderRadius: 5,
  },
};
