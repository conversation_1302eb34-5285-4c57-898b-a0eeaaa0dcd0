import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const ArrowLeftIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  return (
    <svg
      width="11"
      height="17"
      viewBox="0 0 11 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 1L1.01743 8.49998L9.03487 16"
        stroke={fillColor}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ArrowLeftIcon;
