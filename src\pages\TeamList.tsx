import React, { useEffect, useState } from "react";
import FrameContainer from "../components/layout/Container";
import {
  <PERSON>ton,
  <PERSON>ack,
  Typography,
  Grid,
  FormControl,
  TextField,
  InputAdornment,
} from "@mui/material";
import { Box } from "@mui/system";
import DropdownCollabHistory from "../components/dropdown/DropdownCollabHistory";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { Router } from "../constants/Route";
import { useNavigate } from "../utils/component-util";
import { DefaultFilter } from "../constants/Const";
import MemberItem from "../components/MemberItem";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../redux/store";
import { getMember } from "../redux/slices/team/team";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ marginBlock: 2 }}>{children}</Box>}
    </div>
  );
}

export default function TeamListPage() {
  const [keyword, setKeyword] = useState("");
  const navigator = useNavigate();
  const { list } = useSelector((state: RootState) => state.team);
  const dispatch = useDispatch<AppDispatch>();

  const [showClearIcon, setShowClearIcon] = useState("none");

  const handleChangeSearch = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    setKeyword(event.target.value);
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
  };

  const handleClick = (): void => {
    setKeyword("");
  };
  // handle dropdown
  const [sortBy, setSortBy] = useState("11/3/2023");
  const [status, setStatus] = useState("Cấp bậc");

  const handleSortByChange = (value) => {
    setSortBy(value);
  };

  useEffect(() => {
    dispatch(getMember(keyword));
  }, [keyword]);

  const onClickAddMember = () => {
    navigator(Router.refer.index);
  };

  const handleStatusChange = (value) => {
    setStatus(value);
  };

  const dates = [
    DefaultFilter,
    { name: "4/2024", id: 1 },
    { name: "3/2024", id: 2 },
    { name: "2/2024", id: 3 },
    { name: "1/2024", id: 4 },
  ];

  const levels = [DefaultFilter, { name: "F1", id: 1 }, { name: "F2", id: 2 }];

  return (
    <FrameContainer title="Danh sách đội nhóm" style={{ paddingTop: "10px" }}>
      {/* Search field */}
      <Stack>
        <FormControl>
          <TextField
            placeholder="Nhập tên, SĐT,... "
            style={{ width: "100%" }}
            size="small"
            variant="outlined"
            value={keyword}
            onChange={handleChangeSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  {<SearchIcon />}
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment
                  position="end"
                  style={{ display: showClearIcon }}
                  onClick={handleClick}
                >
                  <ClearIcon />
                </InputAdornment>
              ),
            }}
          />
        </FormControl>
      </Stack>
      {/* dropdown sort */}
      <Grid
        container
        direction="row"
        justifyContent="flex-start"
        display="flex"
        style={{
          paddingTop: "15px",
          width: "100%",
          display: "flex",
          gap: "8px",
        }}
      >
        {/* <Grid item style={{ flex: 1 }}>
          <DropdownCollabHistory
            label=""
            options={dates}
            defaultValue={dates[0].id}
            onChange={handleSortByChange}
          />
        </Grid>
        <Grid item style={{ flex: 1 }}>
          <DropdownCollabHistory
            label=""
            options={levels}
            defaultValue={levels[0].id}
            onChange={handleStatusChange}
          />
        </Grid> */}
        <Grid container direction={"column"}>
          <Button
            style={{
              color: "#fff",
              fontSize: "16px",
              fontWeight: 400,
              border: "1px solid transparent",
              borderRadius: 10,
            }}
            color="secondary"
            variant="contained"
            onClick={onClickAddMember}
          >
            Thêm người
          </Button>
        </Grid>
      </Grid>

      {list.length > 0 ? (
        list.map((item, index) => (
          <MemberItem item={item} key={String(index)} />
        ))
      ) : (
        <Typography
          style={{
            fontSize: 16,
            fontWeight: 400,
            color: "#666666",
            textAlign: "center",
          }}
          pt={4}
        >
          Đội nhóm của bạn chưa có thành viên nào
        </Typography>
      )}
    </FrameContainer>
  );
}
