import { Box, Stack, Typography } from "@mui/material";
import React from "react";
import { COLORS } from "@/constants/themes";
import { IMenu } from "@/types/menu";
import { Icon } from "@/constants/Assets";
import { useConfigApp } from "@/hooks/useConfigApp";
import LazyImage from "../UI/LazyImage";

export default function MenuCategoryItemRetail({
  item,
  style,
  shopInfo,
}: {
  item?: IMenu;
  style?: { radius?: number; fontSize?: number; fontWeight?: number };
  shopInfo?: { businessType: string };
}) {
  if (!item) return null;
  const handleError = (e) => {
    e.target.src = Icon.errorImage; // Đặt URL ảnh thay thế
  };
  const appConfig = useConfigApp();
  let imageSrc = item.image && item.image.link ? item.image.link : item.icon;

  if (!imageSrc) imageSrc = Icon.errorImage;

  return (
    <Stack sx={styles.container}>
      <Stack
        sx={{
          width: `62px`,
          height: `62px`,
          alignItems: "center",
          justifyContent: "center",
          background:
            shopInfo?.businessType === "HomeGarden" ? `${appConfig.color.accent}` : undefined,
        }}
      >
        <LazyImage
          src={imageSrc}
          alt={item.title}
          style={{
            ...styles.iconStyle,
            borderRadius: style?.radius ? `${style.radius}px` : "0px",
          }}
          aspectRatio="1"
        />
      </Stack>
      <Typography
        style={{
          ...styles.titleStyle,
          fontSize: style?.fontSize ? `${style.fontSize}px` : "12px",
          fontWeight: style?.fontWeight ? `${style.fontWeight}` : "700",
        }}
      >
        {item.title}
      </Typography>
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    marginTop: 0.5,
    padding: "0px 5px",
  },
  iconBox: {
    backgroundColor: COLORS.second,
    borderRadius: 2,
    width: "100%",
  },
  iconStyle: {
    width: "60px",
    height: "60px",
    aspectRatio: "1 / 1",
    objectFit: "cover",
  },

  titleStyle: {
    marginTop: 5,
    // fontSize: 12,
    textAlign: "center",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 2,
    overflow: "hidden",
    textOverflow: "ellipsis",
    wordBreak: "break-word",
    lineHeight: "1.5em",
    maxHeight: "3em",
    color: "#000000",
  },
};
