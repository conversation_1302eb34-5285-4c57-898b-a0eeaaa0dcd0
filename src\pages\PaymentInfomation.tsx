import React, { useState } from "react";
import FrameContainer from "../components/layout/Container";
import {
  <PERSON>ton,
  <PERSON>ack,
  Tab,
  Tabs,
  Typography,
  Grid,
  TextField,
  List,
  ListItem,
} from "@mui/material";
import PopupEditInfo from "../components/UI/PopupEditInfo";
import { useTheme } from "@mui/material/styles";

const PaymentInfomation = () => {
  const theme = useTheme();

  //   const [formData, setFormData] = useState({
  //     fullName: "",
  //     accountNumber: "",
  //   });

  //   const handleChange = (event) => {
  //     const { name, value } = event.target;
  //     setFormData({
  //       ...formData,
  //       [name]: value,
  //     });
  //   };

  //   const handleSubmit = (event) => {
  //     event.preventDefault();
  //     // Xử lý dữ liệu khi form được gửi đi
  //     console.log("Form submitted:", formData);
  //   };

  return (
    <FrameContainer title="Thông tin thanh toán" style={{ paddingTop: "5px" }}>
      <Stack
        style={{ background: "#fff" }}
        padding={2}
        borderRadius={4}
        marginBlock={1}
      >
        <span
          style={{
            fontWeight: 700,
            fontSize: 16,
            marginBottom: 12,
            color: theme.palette.primary.main,
          }}
        >
          Thông tin thanh toán
        </span>
        {/* <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label="Họ tên"
            name="fullName"
            value={formData.fullName}
            onChange={handleChange}
            variant="outlined"
            margin="normal"
          />
          <TextField
            fullWidth
            label="Số tài khoản"
            name="accountNumber"
            value={formData.accountNumber}
            onChange={handleChange}
            variant="outlined"
            margin="normal"
          />
          <Button type="submit" variant="contained" color="primary">
            Gửi
          </Button> */}
        {/* <List>
          <ListItem>
            <Stack
              width="100%"
              direction="row"
              justifyContent="flex-start"
              display="flex"
            >
              <div style={{ color: "#2677E8", flex: 1.5, textAlign: "left" }}>
                Họ và tên
              </div>
              <div
                style={{
                  color: "#000000",
                  flex: 2,
                  backgroundColor: "#EDF7FE",
                  borderRadius: "4px",
                  padding: "2px 8px 2px 30px",
                  fontWeight: 400,
                }}
              >
                Nguyễn Văn Tâm
              </div>
            </Stack>
          </ListItem>
          <ListItem>
            <Stack
              width="100%"
              direction="row"
              justifyContent="flex-start"
              display="flex"
            >
              <div style={{ color: "#2677E8", flex: 1.5, textAlign: "left" }}>
                Số tài khoản
              </div>
              <div
                style={{
                  color: "#000000",
                  flex: 2,
                  backgroundColor: "#EDF7FE",
                  borderRadius: "4px",
                  padding: "2px 8px 2px 30px",
                  fontWeight: 400,
                }}
              >
                1234368888
              </div>
            </Stack>
          </ListItem>
          <ListItem>
            <Stack
              width="100%"
              direction="row"
              justifyContent="flex-start"
              display="flex"
            >
              <div style={{ color: "#2677E8", flex: 1.5, textAlign: "left" }}>
                Ngân hàng
              </div>
              <div
                style={{
                  color: "#000000",
                  flex: 2,
                  backgroundColor: "#EDF7FE",
                  borderRadius: "4px",
                  padding: "2px 8px 2px 30px",
                  fontWeight: 400,
                }}
              >
                MB Quân Đội
              </div>
            </Stack>
          </ListItem>
          <ListItem>
            <Stack
              width="100%"
              direction="row"
              justifyContent="flex-start"
              display="flex"
            >
              <div style={{ color: "#2677E8", flex: 1.5, textAlign: "left" }}>
                Chi nhánh
              </div>
              <div
                style={{
                  color: "#000000",
                  flex: 2,
                  backgroundColor: "#EDF7FE",
                  borderRadius: "4px",
                  padding: "2px 8px 2px 30px",
                  fontWeight: 400,
                }}
              >
                Hà Nội
              </div>
            </Stack>
          </ListItem>
        </List>
        <Stack direction="row" justifyContent="flex-end">
          <PopupEditInfo
            view={
              <Stack gap={2}>
                <TextField
                  required
                  id="outlined-required"
                  label="Họ và tên"
                  defaultValue="Nguyễn Văn Tâm"
                />
                <TextField
                  required
                  id="outlined-required"
                  label="Số tài khoản"
                  defaultValue="1234368888"
                />
                <TextField
                  required
                  id="outlined-required"
                  label="Ngân hàng"
                  defaultValue="MB Quân Đội"
                />
                <TextField
                  required
                  id="outlined-required"
                  label="Chi nhánh"
                  defaultValue="Ha Noi"
                />
              </Stack>
            }
          />
        </Stack> */}
        {/* </form> */}
      </Stack>
    </FrameContainer>
  );
};

export default PaymentInfomation;
