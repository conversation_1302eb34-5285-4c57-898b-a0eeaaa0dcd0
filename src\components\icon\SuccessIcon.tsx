import React from "react";
export interface IconCustomProps {
  primaryColor?: string;
  secondaryColor?: string;
  className?: string;
}
const SuccessIcon: React.FC<IconCustomProps> = ({
  primaryColor,
  secondaryColor,
  className,
}) => {
  return (
    <svg
      width="82"
      height="82"
      viewBox="0 0 82 82"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M42.263 5.19076L41 3.21963L39.737 5.19076L33.8619 14.3601L24.1892 9.35679L22.1098 8.28124L22.0016 10.6198L21.4982 21.4982L10.6198 22.0016L8.28124 22.1098L9.35679 24.1892L14.3601 33.8619L5.19076 39.737L3.21963 41L5.19076 42.263L14.3601 48.1381L9.35679 57.8108L8.28124 59.8902L10.6198 59.9984L21.4982 60.5018L22.0016 71.3802L22.1098 73.7188L24.1892 72.6432L33.8619 67.6399L39.737 76.8092L41 78.7804L42.263 76.8092L48.1381 67.6399L57.8108 72.6432L59.8902 73.7188L59.9984 71.3802L60.5018 60.5018L71.3802 59.9984L73.7188 59.8902L72.6432 57.8108L67.6399 48.1381L76.8092 42.263L78.7804 41L76.8092 39.737L67.6399 33.8619L72.6432 24.1892L73.7188 22.1098L71.3802 22.0016L60.5018 21.4982L59.9984 10.6198L59.8902 8.28124L57.8108 9.35679L48.1381 14.3601L42.263 5.19076Z"
        fill={primaryColor}
        stroke={secondaryColor}
        strokeWidth="3"
      />
      <path d="M31 40.8333L38.7368 49L52 35" stroke="white" strokeWidth="4" />
    </svg>
  );
};

export default SuccessIcon;
