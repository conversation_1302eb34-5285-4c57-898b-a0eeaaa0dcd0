import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const SearchIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 25 25"
      fill="none"
    >
      <path
        d="M11.9792 21.8752C17.4445 21.8752 21.875 17.4446 21.875 11.9793C21.875 6.51401 17.4445 2.0835 11.9792 2.0835C6.51389 2.0835 2.08337 6.51401 2.08337 11.9793C2.08337 17.4446 6.51389 21.8752 11.9792 21.8752Z"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.9167 22.9168L20.8334 20.8335"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SearchIcon;
