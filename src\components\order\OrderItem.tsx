import React from "react";
import { Env, OrderPrefix, OrderStatusText } from "../../constants/Const";
import { COLORS, commonStyle } from "../../constants/themes";
import { formatPrice } from "../../utils/formatPrice";
import { <PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import { useNavigate } from "../../utils/component-util";
import { Router } from "../../constants/Route";
import { IOrder } from "../../types/order";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { useConfigApp } from "@/hooks/useConfigApp";

interface IOrderItem {
  order: IOrder;
  isShowLevel?: boolean;
}

export default function OrderItem({ order, isShowLevel }: IOrderItem) {
  const navigate = useNavigate();
  const appConfig = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const firstProduct = order.orderData.items[0];
  const url =
    firstProduct.product?.image?.[0]?.url ||
    Env.BackendUrl + firstProduct.product?.image?.data?.[0]?.attributes?.url;
  const price =
    firstProduct.product.price - firstProduct.product?.discount || 0;

  return (
    <Stack
      key={order.id}
      style={commonStyle.shadowBorder}
      padding={2}
      borderRadius={4}
      marginBlock={0.5}
      onClick={() => {
        if (user?.id === order.creator?.id) {
          navigate(`${Router.order.index}/${order.id}`);
        } else {
          navigate(`${Router.order.f1Detail}`, {
            state: { order },
          });
        }
      }}
    >
      <Stack direction="row" justifyContent={"space-between"}>
        <Typography
          style={{
            fontSize: 16,
            fontWeight: 700,
            color: COLORS.primary1,
          }}
        >
          {order.receiver.userName}
        </Typography>
        {isShowLevel && (
          <Typography sx={{ color: COLORS.primary1 }}>
            {`${order.orderId}${
              order.creator?.fLevel ? ` - F${order.creator.fLevel}` : ""
            }`}
          </Typography>
        )}
      </Stack>
      <Stack
        direction="row"
        gap={2}
        alignItems={"center"}
        key={firstProduct.product.id}
        paddingTop={1}
      >
        <img width={60} src={url} alt="" />
        <Stack gap={1}>
          <Typography style={{ fontSize: 16, color: COLORS.primary1 }}>
            {firstProduct.product.name}
          </Typography>
          <Typography
            style={{
              fontSize: 16,
              fontWeight: 700,
              color: COLORS.primary1,
            }}
          >
            {formatPrice(price)} x{firstProduct.quantity}
          </Typography>
        </Stack>
      </Stack>

      <Stack direction="row" mt={2} gap={2} justifyContent={"center"}>
        <Button
          style={{ color: "#fff", borderRadius: 99 }}
          color="secondary"
          variant="contained"
        >
          Xem sản phẩm
        </Button>
        <Button
          style={{ color: "#fff",  borderRadius: 99, backgroundColor: appConfig.color.accent }}
          color="secondary"
          variant="contained"
        >
          {OrderStatusText[order.orderStatus]}
        </Button>
      </Stack>
    </Stack>
  );
}
