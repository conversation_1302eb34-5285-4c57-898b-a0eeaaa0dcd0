import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../redux/store";
import { useAlert } from "../redux/slices/alert/useAlert";
import { useNavigate } from "react-router-dom";
import { useConfigApp } from "./useConfigApp";
import { useState } from "react";
import { authZalo, getUser, getUserZalo } from "@/redux/slices/authen/authSlice";
import { useCalcCartAfterLogin } from "./useCalcCartAfterLogin";
import { Platform } from "@/config";
import { mapError } from "@/utils/common";
import { StorageKeys } from "@/constants/storageKeys";
import { getItem } from "@/utils/storage";
import CheckIcon from "@/components/icon/CheckIcon";

export const useCheckLogin = () => {
  const appConfig = useConfigApp();
  const { color } = appConfig;
  const { user, loading } = useSelector((state: RootState) => state.auth);
  const { showAlert } = useAlert();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const calcCartAfterLogin = useCalcCartAfterLogin();

  const [loadingRegister, setLoadingRegister] = useState(false);
  const [openLoginPopup, setOpenLoginPopup] = useState(false);

  const onClickRegister = async () => {
    setLoadingRegister(true);
    const refCode = await getItem(StorageKeys.RefCode);
    const res = await dispatch(authZalo(refCode)).unwrap();
    if (res && res.idFor) {
      if (Platform === "zalo") await dispatch(getUserZalo());
      await dispatch(getUser());
      await calcCartAfterLogin();
      showAlert({
        icon: CheckIcon,
        title: "Kích hoạt tài khoản thành công",
        buttons: [
          {
            title: "OK",
          },
        ],
      });
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoadingRegister(false);
  };

  const checkLogin = (callback: () => void) => {
    if (!user) {
      showAlert({
        title: appConfig.shopName,
        content: "Vui lòng kích hoạt tài khoản để tiếp tục!",
        icon: "⚠️",
        buttons: [
          {
            title: "Đóng",
            action: () => {},
          },
          {
            title: "Kích hoạt",
            action: () => {
              Platform === "zalo" ? onClickRegister() : setOpenLoginPopup(true);
            },
          },
        ],
      });
    } else {
      callback();
    }
  };

  return {
    checkLogin,
    openLoginPopup,
    setOpenLoginPopup,
    onClickRegister,
    loading,
  };
};
