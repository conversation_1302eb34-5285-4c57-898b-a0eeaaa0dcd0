import React, { useState, useEffect } from "react";
import { Dialog, DialogContent, IconButton, Box, Button } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useNavigate } from "@/utils/component-util";
import { useSelector } from "react-redux";
import { ShopCategory } from "@/types/shop";
import { useLocation } from "react-router-dom";
import { RootState } from "@/redux/store";
import dayjs from "dayjs";
import { setItem, getItem } from "@/utils/storage";

export interface IAdvertisement {
  advertiseId: string;
  name: string;
  image: { link: string; type: string };
  link?: string;
  articleId?: string;
  startDate?: string;
  endDate?: string;
  typePosition?: string;
  categoryId?: string;
  categoryName?: string;
  typeNavigate?: string;
}

interface PopupAdvertiseProps {
  advertisements: IAdvertisement[];
}

const HIDE_AD_KEY = "hide_ad";

export default function PopupAdvertise({ advertisements }: PopupAdvertiseProps) {
  const navigate = useNavigate();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [activeItems, setActiveItems] = useState<IAdvertisement[]>([]);
  const location = useLocation();
  const categoryId = location.state?.categoryId;
  const { shopInfo } = useSelector((state: RootState) => state.appInfo);

  const isValidAd = async (ad, now) => {
    let isHidden = await getItem(HIDE_AD_KEY);
    if (!isHidden) isHidden = "";
    const hiddenIds = isHidden.split(",");
    if (hiddenIds.includes(ad.advertiseId)) return false;
    if (ad.typePosition && ad.typePosition !== "HOME") return false;
    if (ad.startDate && now.isBefore(dayjs(ad.startDate))) return false;
    if (ad.endDate && now.isAfter(dayjs(ad.endDate))) return false;
    return true;
  };

  useEffect(() => {
    const now = dayjs();
    const filterAds = async () => {
      const results: IAdvertisement[] = [];
      for (const ad of advertisements) {
        if (await isValidAd(ad, now)) results.push(ad);
      }
      setActiveItems(results);
      setIsOpen(results.length > 0);
    };
    filterAds();
  }, [advertisements]);

  const items = activeItems;

  const handleClose = () => {
    if (currentIndex < items.length - 1) setCurrentIndex((idx) => idx + 1);
    else setIsOpen(false);
  };

  const handleClick = async () => {
    const ad = items[currentIndex];
    if (ad.typeNavigate === "LINK" && ad.link) {
      window.open(ad.link, "_blank");
    } else if (ad.typeNavigate === "ARTICLE" && ad.articleId) {
      navigate(`/posts/${ad.articleId}`);
    } else if (ad.typeNavigate === "CATEGORY" && ad.categoryId) {
      if (shopInfo?.businessType === ShopCategory.FB) {
        navigate("/menu", { state: { categoryId: ad.categoryId } });
      } else {
        navigate("/product-category", {
          state: { categoryId: ad.categoryId, categoryName: ad.categoryName },
        });
      }
    }
  };

  const handleDoNotShow = async () => {
    const ad = items[currentIndex];
    let isHidden = await getItem(HIDE_AD_KEY);
    if (!isHidden) isHidden = "";
    const hiddenIds = isHidden.split(",");
    if (!hiddenIds.includes(ad.advertiseId)) {
      hiddenIds.push(ad.advertiseId);
      await setItem(HIDE_AD_KEY, hiddenIds.join(","));
    }
    const next = items.filter((_, i) => i !== currentIndex);
    setActiveItems(next);
    if (currentIndex >= next.length) setIsOpen(false);
    else setCurrentIndex(currentIndex);
  };

  if (!isOpen || !items.length) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={handleClose}
      PaperProps={{
        style: {
          background: "transparent",
          boxShadow: "none",
          maxWidth: "unset",
          maxHeight: "unset",
          overflow: "visible",
        },
      }}
      sx={{
        "& .MuiDialog-paper": {
          background: "transparent",
          boxShadow: "none",
          maxWidth: "unset",
          maxHeight: "unset",
          overflow: "visible",
        },
      }}
    >
      <DialogContent style={{ padding: 0, background: "transparent", overflow: "visible" }}>
        <Box
          position="relative"
          display="flex"
          flexDirection="column"
          alignItems="center"
          p={0}
          m={0}
        >
          <IconButton
            sx={{
              position: "absolute",
              top: -10,
              right: -10,
              zIndex: 2,
              background: "#eee",
              borderRadius: "50%",
              boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
              border: "1px solid #eee",
              width: 20,
              height: 20,
              "&:hover": { background: "#f2f2f2" },
            }}
            onClick={handleClose}
          >
            <CloseIcon sx={{ color: "#222", fontSize: 22, fontWeight: 500 }} />
          </IconButton>

          <Box
            onClick={handleClick}
            sx={{
              cursor: "pointer",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <img
              src={items[currentIndex].image.link}
              alt={items[currentIndex].name}
              style={{
                display: "block",
                maxWidth: "360px",
                maxHeight: "360px",
                objectFit: "contain",
                background: "transparent",
              }}
            />
          </Box>

          <Box mt={-0.5}>
            <Button
              size="small"
              onClick={handleDoNotShow}
              sx={{
                fontSize: 17,
                color: "#fff",
                textDecoration: "underline",
                textTransform: "none",
                "&:hover": { backgroundColor: "transparent" },
              }}
            >
              Không hiển thị lại
            </Button>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
