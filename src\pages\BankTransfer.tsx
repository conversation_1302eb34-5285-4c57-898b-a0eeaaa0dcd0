import React from "react";
import { <PERSON><PERSON>, <PERSON>rid, IconButton, Stack, Typography } from "@mui/material";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import { IOrder } from "../types/order";
import { Router } from "../constants/Route";
import { useLocation } from "react-router-dom";
import { useNavigate } from "../utils/component-util";
import { copy } from "../utils/common";

//TODO: Update for Gia tin
const Receiver = {
  name: "",
  accountNumber: "",
  bank: "",
};

export default function BankTransfer() {
  const location = useLocation();
  const order = location.state as IOrder;

  const transferMessage = `GiaTin${order.id}`;
  const qrUrl = `https://apiqr.web2m.com/api/generate/${Receiver.bank}/${
    Receiver.accountNumber
  }/${Receiver.name.replace(/ /g, "%20")}?amount=${
    order.finalPrice
  }&memo=${transferMessage}&is_mask=0&bg=0`;
  const navigate = useNavigate();

  const renderCopy = (text: string) => {
    return (
      <IconButton onClick={() => copy(text)}>
        <FileCopyIcon />
      </IconButton>
    );
  };

  return (
    <Stack alignItems={"center"} padding={2}>
      <Typography variant="h5" fontWeight={"bold"}>
        Đặt hàng thành công
      </Typography>
      <Typography>
        {" "}
        Vui lòng chuyển khoản vào tài khoản với nội dung bên dưới. <br />
        Sau khoảng 1, 2 phút, đơn hàng sẽ được chuyển thành trạng thái{" "}
        <b>Đã thanh toán</b>.
      </Typography>
      <img src={qrUrl} width={300} height="auto" style={{ marginTop: 30 }} />
      <Grid
        container
        justifyContent="center"
        padding={1}
        marginBottom={1}
        direction="column"
        alignItems="center"
      >
        <Grid item container justifyContent="center" alignItems="center">
          <Typography>Số tài khoản:</Typography>
          <Typography fontWeight="bold" marginLeft={1}>
            {Receiver.accountNumber}
          </Typography>
          {renderCopy(Receiver.accountNumber)}
        </Grid>
        <Grid item container justifyContent="center" alignItems="center">
          <Typography>Tên người nhận:</Typography>
          <Typography fontWeight="bold" marginLeft={1}>
            {Receiver.name}
          </Typography>
          {renderCopy(Receiver.name)}
        </Grid>
        <Grid item container justifyContent="center" alignItems="center">
          <Typography>Nội dung cú pháp: </Typography>
          <Typography fontWeight="bold" marginLeft={1}>
            {transferMessage}
          </Typography>
          {renderCopy(transferMessage)}
        </Grid>
      </Grid>
      <Button variant="contained" onClick={() => navigate(Router.order.index)}>
        Tôi đã chuyển
      </Button>
    </Stack>
  );
}
