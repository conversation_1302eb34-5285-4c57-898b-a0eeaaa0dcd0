import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { INotification } from "../../../types/INotification";

interface NotificationState {
  data: INotification[];
  isLoading: boolean;
  error: string | null;
}

const initialState: NotificationState = {
  data: [],
  isLoading: true,
  error: null,
};

export const getNotifications = createAsyncThunk(
  "notification/get",
  async () => {
    const response: any = await request("get", "/api/my/notification", {
      ["sort[0]"]: "createdAt:desc",
    });
    return response;
  },
);

const notificationSlice = createSlice({
  name: "notification",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getNotifications.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getNotifications.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.data = payload.data;
          state.isLoading = false;
        },
      )
      .addCase(getNotifications.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export default notificationSlice.reducer;
