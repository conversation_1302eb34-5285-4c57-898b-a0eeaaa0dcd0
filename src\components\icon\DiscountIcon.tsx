import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const DiscountIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
    >
      <path
        d="M7.01042 20.521C7.86459 19.6043 9.16667 19.6772 9.91667 20.6772L10.9688 22.0835C11.8125 23.1981 13.1771 23.1981 14.0208 22.0835L15.0729 20.6772C15.8229 19.6772 17.125 19.6043 17.9792 20.521C19.8333 22.5002 21.3438 21.8439 21.3438 19.0731V7.3335C21.3542 3.13558 20.375 2.0835 16.4375 2.0835H8.5625C4.625 2.0835 3.64584 3.13558 3.64584 7.3335V19.0627C3.64584 21.8439 5.16667 22.4897 7.01042 20.521Z"
        fill={fillColor}
        stroke={fillColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.375 13.5415L15.625 7.2915"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.6193 13.5418H15.6286"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.36928 7.81234H9.37863"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default DiscountIcon;
