import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CloseIcon from "@mui/icons-material/Close";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import ControlPointIcon from "@mui/icons-material/ControlPoint";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Divider,
  FormControl,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  Slide,
  Stack,
  Typography,
} from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";
import React, { useEffect } from "react";
import { useState } from "react";
import { Icon } from "../../constants/Assets";
import { useDispatch, useSelector } from "react-redux";
import {
  getAddressList,
  setCurrentAddress,
} from "../../redux/slices/address/addressSlice";
import { AppDispatch, RootState } from "../../redux/store";
import { updateUser } from "../../redux/slices/authen/authSlice";
import { useTheme } from "@mui/material/styles";
import { Router } from "../../constants/Route";
import { useCheckLogin } from "../../hooks/useCheckLogin";
import { useNavigate } from "../../utils/component-util";
import { COLORS, commonStyle } from "../../constants/themes";
import SmallCheckIcon from "../icon/SmallCheckIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { IAddress } from "@/types/address";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function PopupChangeBranch() {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);
  const listAddress = useSelector((state: RootState) => state.address.list);

  const { defaultBranch, list } = useSelector(
    (state: RootState) => state.branch,
  );
  const [open, setOpen] = React.useState(false);
  const [newAddressId, setNewAddressId] = useState<string>();

  useEffect(() => {
    if (!newAddressId && defaultBranch?.branchId) {
      setNewAddressId(defaultBranch.branchId);
    }
  }, [defaultBranch]);

  const handleClose = () => {
    setOpen(false);
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClickFormAddressOpen = () => {
    handleClickOpen();
  };

  const handleChangeAddress = async () => {
    if (!Array.isArray(listAddress)) return;

    setOpen(false);
    // if (user && newAddressId && user.addressId !== newAddressId) {
    //   const res = await dispatch(
    //     updateUser({
    //       id: user.id,
    //       addressId: newAddressId,
    //     }),
    //   );

    //   if (res) {
    //     const newAddress: any = listAddress.find(
    //       (item) => item.id === newAddressId,
    //     );

    //     dispatch(setCurrentAddress(newAddress));
    //   }
    // }
  };

  const onClickBranch = () => {
    navigate(Router.branch.index, { state: { shouldGoBack: true } });
  };

  const defaultBranchAddress = [
    defaultBranch?.address,
    defaultBranch?.wardName,
    defaultBranch?.districtName,
    defaultBranch?.provinceName,
  ]
    .filter(Boolean)
    .join(" - ");

  return (
    <>
      {defaultBranch ? (
        <Stack
          direction="row"
          justifyContent={"space-between"}
          onClick={onClickBranch}
          paddingTop={1}
        >
          <Stack direction="row" gap={2}>
            <img src={Icon.map} />
            <Stack>
              <Typography style={styles.contentTitle}>
                {defaultBranch.branchName} - {defaultBranch.phoneNumber}
              </Typography>
              <Typography style={styles.contentText}>
                {defaultBranchAddress}
              </Typography>
            </Stack>
          </Stack>
          <IconButton>
            <img src={Icon.arrow_right} alt="" />
          </IconButton>
        </Stack>
      ) : (
        <Stack
          direction="row"
          justifyContent={"space-between"}
          gap={2}
          onClick={onClickBranch}
          paddingTop={1}
        >
          <Stack direction="row" gap={2}>
            <img src={Icon.map} />
            <Stack>
              <Typography style={styles.contentTitle}>
                Danh sách cửa hàng
              </Typography>
              <Typography style={styles.contentText}>
                Chọn cửa hàng nhận hàng của bạn
              </Typography>
            </Stack>
          </Stack>
          <IconButton>
            <img src={Icon.arrow_right} alt="" />
          </IconButton>
        </Stack>
      )}

      <Dialog
        className="popup-add-to-cart"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogContent id="alert-dialog-slide-description">
          <Stack direction="row" gap={2} style={{ fontSize: 16 }}>
            <FormControl style={{ width: "100%" }}>
              <Stack gap={2}>
                <RadioGroup
                  row
                  aria-labelledby="demo-form-control-label-placement"
                  name="position"
                  defaultValue="top"
                >
                  <Stack gap={2} width={"100%"}>
                    <FormControl>
                      <RadioGroup
                        aria-labelledby="demo-radio-buttons-group-label"
                        defaultValue={user?.addressId}
                        name="radio-buttons-group"
                      >
                        {listAddress?.map((item, index) => (
                          <Stack key={`address-${index}`}>
                            <Stack
                              direction={"row"}
                              sx={styles.addressContainer}
                              onClick={() => setNewAddressId(item.id)}
                            >
                              <Stack direction={"row"}>
                                <img src={Icon.map} width={24} />
                                <Stack ml={3}>
                                  <Typography
                                    style={{
                                      ...commonStyle.headline14,
                                      color: appConfig.color.primary,
                                    }}
                                  >
                                    {item?.fullName} - {item?.phoneNumber}
                                  </Typography>
                                  <Typography style={{ fontSize: 12 }}>
                                    {item?.address} - {item?.wardName} -{" "}
                                    {item?.districtName} - {item?.provinceName}
                                  </Typography>
                                </Stack>
                              </Stack>
                              <FormControlLabel
                                value={item.id.toString()}
                                control={
                                  <Radio
                                    checkedIcon={
                                      <SmallCheckIcon
                                        fillColor={appConfig.color.primary}
                                      />
                                    }
                                  />
                                }
                                labelPlacement="start"
                                label=""
                                checked={newAddressId == item.id}
                                onChange={async (e: any) => {
                                  setNewAddressId(e.target.value);
                                }}
                              />
                            </Stack>
                            {index !== listAddress.length - 1 && (
                              <Divider sx={{ margin: "10px 0" }} />
                            )}
                          </Stack>
                        ))}
                      </RadioGroup>
                    </FormControl>
                  </Stack>
                </RadioGroup>
              </Stack>
              <Stack direction={"row"} justifyContent={"center"}>
                <Button
                  onClick={onClickBranch}
                  style={{
                    ...styles.addBtn,
                    background: appConfig.color.primary,
                  }}
                  startIcon={<ControlPointIcon />}
                  variant="contained"
                >
                  Thêm địa chỉ
                </Button>
              </Stack>
            </FormControl>
          </Stack>
        </DialogContent>
        <DialogActions sx={styles.bottomContainer}>
          <Button
            style={{
              ...styles.bottomBtn,
              background: COLORS.accent4,
              color: appConfig.color.primary,
            }}
            onClick={() => setOpen(false)}
            startIcon={<CloseIcon />}
          >
            Hủy bỏ
          </Button>
          <Button
            style={{
              ...styles.bottomBtn,
              background: appConfig.color.primary,
              color: appConfig.color.accent,
            }}
            startIcon={<CheckCircleOutlineIcon />}
            onClick={() => handleChangeAddress()}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  addressContainer: {
    width: "100%",
    justifyContent: "space-between",
  },
  bottomContainer: {
    justifyContent: "center",
    gap: 2,
    marginBlock: 2,
  },
  contentTitle: {
    fontWeight: 500,
    fontSize: 15,
    color: "#828282",
  },
  contentText: {
    color: "#C5C5C5",
    fontWeight: 400,
    fontSize: 12,
    paddingTop: 1,
  },
  bottomBtn: {
    margin: 0,
    height: "100%",
    padding: "10px 30px",
    borderRadius: 99,
    display: "flex",
    gap: "8px",
  },
  addBtn: {
    background: COLORS.accent1,
    color: "#FFF",
    borderRadius: 99,
    width: "fit-content",
    padding: "10px 60px",
    marginBlock: 16,
  },
};
