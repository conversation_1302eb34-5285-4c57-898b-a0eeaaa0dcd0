import React from "react";
import { Box, Grid, useTheme } from "@mui/material";
import UserCard from "../../components/user-card";
import { LevelIcon, LevelName } from "../../constants/Const";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { COLORS, commonStyle } from "../../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";

export default function ProfileHeader() {
  const { color } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const theme = useTheme();

  return (
    <Grid container>
      <Grid item xs={7}>
        <Box sx={styles.container}>
          <Box p={0.1} component="section" sx={styles.iconContainer}>
            <img
              height={25}
              width={25}
              src={LevelIcon[user?.level || 0]}
              alt=""
            />
          </Box>
          <Box style={{ ...commonStyle.headline12, width: "100%" }}>
            <p
              style={{
                // marginTop: 10,
                color: COLORS.black,
                ...styles.noMargin,
              }}
            >
              Tài khoản xác thực
            </p>
            <p style={{ ...styles.neutral1, ...styles.noMargin }}>
              Hạng:{" "}
              <span style={{ color: color.secondary }}>
                {user?.level !== undefined && LevelName[user.level || 0]}
              </span>
            </p>
          </Box>
        </Box>
      </Grid>
      <Grid item xs={5}>
        <UserCard />
      </Grid>
    </Grid>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    display: "flex",
    gap: 1,
    alignItems: "center",
  },
  iconContainer: {
    borderRadius: "50%",
    background: "white",
    display: "flex",
    alignItems: "center",
  },
  rankText: {
    marginTop: -12,
    color: "#EE4D2D",
  },
  noMargin: {
    marginBlockStart: 0,
    marginBlockEnd: 0,
  },
};
