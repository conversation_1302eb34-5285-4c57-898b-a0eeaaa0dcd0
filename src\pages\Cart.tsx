import ListSlider from "@/components/home/<USER>";
import BottomVerifyCart from "@/components/products/BottomVerifyCart";
import ProductItem from "@/components/products/item/ProductItemFnB";
import VariantSelector from "@/components/products/VariantSelector";
import { Icon } from "@/constants/Assets";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getCart, setCart, setCartPayment } from "@/redux/slices/cart/cartSlice";
import { getProductDetail } from "@/redux/slices/product/productSlice";
import { showToast } from "@/utils/common";
import AddIcon from "@mui/icons-material/Add";
import HorizontalRuleIcon from "@mui/icons-material/HorizontalRule";
import {
  Backdrop,
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Divider,
  FormControlLabel,
  FormGroup,
  IconButton,
  Popover,
  Typography,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { Stack } from "@mui/system";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import FrameContainer from "../components/layout/Container";
import NoDataView from "../components/UI/NoDataView";
import { Router } from "../constants/Route";
import { COLORS } from "../constants/themes";
import { useCart } from "../hooks/useCart";
import { AppDispatch, RootState } from "../redux/store";
import { IProduct, IProductVariant } from "../types/product";
import { useNavigate } from "../utils/component-util";
import { formatPrice } from "../utils/formatPrice";

export default function Cart() {
  const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  const [products, setProducts] = useState<IProduct[]>([]);
  const { color, ...appConfig } = useConfigApp();
  const {
    cart,
    cartPayment,
    clearCart,
    changeProductQuantity,
    removeProductFromCart,
    createOrder,
  } = useCart();
  const navigate = useNavigate();

  const [isEmpty, setIsEmpty] = useState(true);
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    const checkEmpty = products?.reduce((acc, item) => {
      return acc + item.quantity;
    }, 0);

    if (checkEmpty > 0) {
      setIsEmpty(false);
    }
  }, [products]);

  useEffect(() => {
    if (Array.isArray(cart?.listItems) && cart?.listItems.length > 0) {
      const fetchProducts = async () => {
        const updatedItems = await Promise.all(
          cart.listItems.map(async (item) => {
            const product = await dispatch(getProductDetail(item.itemsCode)).unwrap();
            return { ...item, listVariant: product.listVariant };
          })
        );
        setProducts(updatedItems);
      };

      fetchProducts();
    } else {
      setProducts([]);
    }
  }, [cart, dispatch]);

  const { hotProductList, list } = useSelector((state: RootState) => state.productList);

  const reduce = (item, quantity) => {
    if (quantity === 1) {
      removeProductFromCart(item);
    } else {
      changeProductQuantity(item, quantity - 1);
    }
  };
  const increase = (item, quantity) => {
    changeProductQuantity(item, quantity + 1);
  };
  // const setQuantity = (item, quantity) => {
  //   changeProductQuantity(item, quantity);
  // };

  const handleCheckboxChange = (event, item) => {
    let updatedListItems;
    if (event.target.checked) {
      updatedListItems = [...cartPayment.listItems, item];
    } else {
      updatedListItems = cartPayment.listItems.filter(
        (i) =>
          !(
            i.itemsCode === item.itemsCode &&
            i.variantNameOne === item.variantNameOne &&
            i.variantValueOne === item.variantValueOne &&
            i.variantNameTwo === item.variantNameTwo &&
            i.variantValueTwo === item.variantValueTwo &&
            i.variantNameThree === item.variantNameThree &&
            i.variantValueThree === item.variantValueThree
          )
      );
    }

    const updatedPrice = updatedListItems.reduce(
      (total, curr) => total + curr.price * curr.quantity,
      0
    );

    dispatch(
      setCartPayment({
        ...cartPayment,
        listItems: updatedListItems,
        price: updatedPrice,
      })
    );
  };

  const [anchorElMap, setAnchorElMap] = useState<{
    [key: number]: HTMLElement | null;
  }>({});

  const [selectedVariant, setSelectedVariant] = useState<IProductVariant | null>(null);

  const handleOpen = (event: React.MouseEvent<HTMLElement>, item: IProduct, index: number) => {
    setAnchorElMap((prev) => ({ ...prev, [index]: event.currentTarget }));
  };

  const handleClose = (index: number) => {
    setAnchorElMap((prev) => ({ ...prev, [index]: null }));
  };

  const handleVariantChange = (updatedVariant: IProductVariant, product: IProduct) => {
    const foundVariant = product.listVariant.find(
      (variant) =>
        variant.variantNameOne === updatedVariant.variantNameOne &&
        variant.variantValueOne === updatedVariant.variantValueOne &&
        variant.variantNameTwo === updatedVariant.variantNameTwo &&
        variant.variantValueTwo === updatedVariant.variantValueTwo &&
        variant.variantNameThree === updatedVariant.variantNameThree &&
        variant.variantValueThree === updatedVariant.variantValueThree
    );

    if (foundVariant) {
      setSelectedVariant(foundVariant);
    }
  };

  const changeVariant = async (index: number) => {
    if (!selectedVariant) {
      showToast({
        content: "Vui lòng chọn thuộc tính sản phẩm",
        type: "error",
      });
      return;
    }

    const updatedItems = cart.listItems.map((item, i) =>
      i === index
        ? {
            ...item,
            variantNameOne: selectedVariant.variantNameOne,
            variantValueOne: selectedVariant.variantValueOne,
            variantNameTwo: selectedVariant.variantNameTwo,
            variantValueTwo: selectedVariant.variantValueTwo,
            variantNameThree: selectedVariant.variantNameThree,
            variantValueThree: selectedVariant.variantValueThree,
            price: selectedVariant?.price,
            priceReal: selectedVariant?.priceReal,
            priceCapital: selectedVariant?.priceCapital,
            itemsId: selectedVariant?.itemsId,
          }
        : item
    );

    await dispatch(setCart({ ...cart, listItems: updatedItems }));
    await dispatch(getCart());
    setSelectedVariant(null);
    handleClose(index);
  };

  return (
    <>
      <FrameContainer title="Giỏ hàng">
        <Stack direction="row" sx={styles.headerSection} p={2}>
          Mô tả sản phẩm
        </Stack>
        <FormGroup>
          <Stack gap={2}>
            {products?.length > 0 && !isEmpty ? (
              products?.map((item, index) => (
                <Stack
                  key={index}
                  direction="row"
                  alignItems={"center"}
                  bgcolor={COLORS.white}
                  py={1}
                >
                  <FormControlLabel
                    style={{
                      width: "15%",
                      justifyContent: "center",
                      margin: 0,
                    }}
                    key={item.itemsCode}
                    control={
                      <Checkbox
                        checked={cartPayment?.listItems?.some(
                          (i) =>
                            i.itemsCode === item.itemsCode &&
                            item.variantNameOne === item.variantNameOne &&
                            i.variantValueOne === item.variantValueOne &&
                            i.variantNameTwo === item.variantNameTwo &&
                            i.variantValueTwo === item.variantValueTwo &&
                            i.variantNameThree === item.variantNameThree &&
                            i.variantValueThree === item.variantValueThree
                        )}
                        onChange={(e) => handleCheckboxChange(e, item)}
                        sx={{
                          "& .MuiSvgIcon-root": {
                            color: color.primary, // Màu khi không chọn
                          },
                          "&.Mui-checked .MuiSvgIcon-root": {
                            // fill: color.secondary,
                            // stroke: color.primary,
                            color: color.primary, // Màu khi đã chọn
                          },
                        }}
                      />
                    }
                    label=""
                  />
                  <Stack width={"85%"} direction={"row"} alignItems={"start"} gap={2}>
                    <img
                      onClick={() => navigate(`/product-detail/${item.itemsCode}`)}
                      style={styles.imageProduct}
                      src={item.images[0].link}
                    />
                    <Stack direction={"column"} width={"calc(100% - 132px)"}>
                      <Typography color={"#5E5E5E"} fontSize={13} fontWeight={500} height={40}>
                        {item.itemsName}
                      </Typography>
                      {item.isVariant && (
                        <>
                          <Stack direction={"row"} alignItems={"center"}>
                            <Typography
                              style={{
                                width: "fit-content",
                                borderRadius: 2,
                                fontSize: 11,
                              }}
                              onClick={(event) => handleOpen(event, item, index)}
                            >
                              {[item.variantValueOne, item.variantValueTwo, item.variantValueThree]
                                .filter(Boolean)
                                .join(", ")}
                            </Typography>
                            <img src={Icon.arrow_down} width={14} />
                          </Stack>
                          <Popover
                            open={Boolean(anchorElMap[index])}
                            anchorEl={anchorElMap[index]}
                            onClose={() => handleClose(index)}
                            anchorOrigin={{
                              vertical: "bottom",
                              horizontal: "center",
                            }}
                            transformOrigin={{
                              vertical: "top",
                              horizontal: "center",
                            }}
                          >
                            <Box paddingInline={2} paddingBottom={2}>
                              <VariantSelector
                                productVariants={item.listVariant}
                                onVariantChange={handleVariantChange}
                                selected={item}
                              />
                              <Box sx={styles.bottomBtnContainer}>
                                <Button
                                  style={{
                                    ...styles.bottomBtn,
                                    background: "#e7e7e7",
                                    color: "#a8a8a8",
                                    flexGrow: 1,
                                  }}
                                  onClick={() => handleClose(index)}
                                >
                                  Trở lại
                                </Button>
                                <Button
                                  style={{
                                    ...styles.bottomBtn,
                                    background: color.primary,
                                    color: "#fff",
                                    flexGrow: 1,
                                  }}
                                  onClick={() => changeVariant(index)}
                                >
                                  Xác nhận
                                </Button>
                              </Box>
                            </Box>
                          </Popover>
                        </>
                      )}

                      <Stack width={"100%"} direction={"row"} mt={1}>
                        <Box width={"60%"}>
                          <Typography color={color.primary} fontSize={18} fontWeight={700}>
                            {formatPrice(item.price)}
                          </Typography>
                          {/* {item.product.attributes &&
                            item.product.attributes.discount > 0 &&
                            item.product.attributes.price > 0 && (
                              <Typography
                                fontSize={12}
                                style={{ textDecoration: "line-through" }}
                              >
                                {formatPrice(item.product.attributes.price)}
                              </Typography>
                            )} */}
                        </Box>
                        <Box
                          width={"40%"}
                          display={"flex"}
                          justifyContent={"center"}
                          alignItems={"center"}
                        >
                          <IconButton
                            style={{
                              ...styles.reduceBtn,
                              border: "1px solid " + color.primary,
                              background: "transparent",
                            }}
                            aria-label="fingerprint"
                            onClick={() => reduce(item, item.quantity)}
                          >
                            <HorizontalRuleIcon style={{ color: color.primary, height: 15 }} />
                          </IconButton>
                          <Stack direction="row" alignItems={"center"} height={20}>
                            <input
                              style={{
                                ...styles.qualityInput,
                                border: "1px solid " + color.primary,
                                color: color.primary,
                                borderInline: "none",
                                borderRadius: 0,
                              }}
                              onChange={(e) => {
                                // setQuantity(item, Number(e.target.value));
                              }}
                              value={item.quantity}
                            />
                          </Stack>
                          <IconButton
                            style={{
                              ...styles.increaseBtn,
                              border: "1px solid " + color.primary,
                            }}
                            aria-label="fingerprint"
                            onClick={() => increase(item, item.quantity)}
                          >
                            <AddIcon style={{ color: color.primary, height: 15 }} />
                          </IconButton>
                        </Box>
                      </Stack>
                    </Stack>
                  </Stack>
                </Stack>
              ))
            ) : (
              <NoDataView content=" Giỏ hàng trống" />
            )}
          </Stack>
          <Divider />
        </FormGroup>
        <Stack direction="row" sx={styles.headerSection} p={2}>
          Có thể bạn cũng thích
        </Stack>
        <Box p={2} bgcolor={COLORS.white}>
          <ListSlider
            sliceConfig={{
              dots: false,
              infinite: false,
              slidesToShow: 2.2,
              autoplay: false,
              arrows: false,
            }}
            overrideStyle={{ marginInline: -8 }}
          >
            {Array.isArray(hotProductList) && hotProductList.length > 0 ? (
              hotProductList.map((item: IProduct) => (
                <Box key={item.itemsCode} className="box-product-item">
                  <ProductItem item={item} />
                </Box>
              ))
            ) : (
              <NoDataView content="Không có sản phẩm nào" />
            )}
          </ListSlider>
        </Box>
      </FrameContainer>
      {Array.isArray(products) && products.length > 0 && (
        <BottomVerifyCart
          cart={cart}
          cartPayment={cartPayment}
          handlerCreatePayment={() => navigate(Router.cartPayment)}
        />
      )}
      <Backdrop sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }} open={isLoading}>
        <CircularProgress color="inherit" />
      </Backdrop>
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  headerSection: {
    color: "#343434",
    fontSize: 17,
    fontWeight: 500,
    alignItems: "center",
    gap: 2,
  },
  contentTitle: {
    fontWeight: 500,
    fontSize: 15,
    color: "#828282",
  },
  contentTitleDisabled: {
    fontWeight: 700,
    color: COLORS.neutral4,
  },
  contentText: {
    color: "#C5C5C5",
    fontWeight: 400,
    fontSize: 12,
    paddingTop: 1,
  },
  contentContainer: {
    justifyContent: "flex-start",
    gap: 2,
  },
  paymentItemContainer: {
    width: "100%",
    justifyContent: "flex-start",
    border: "1px solid #D9D9D9",
    padding: 8,
  },
  paymentItemLabel: {
    flex: 1,
    display: "flex",
    justifyContent: "space-between",
  },
  paymentMethods: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    gap: 2,
  },
  policyText: {
    color: "#D2D2D2",
    textAlign: "center",
    fontWeight: 11,
    paddingInline: 10,
  },
  inputStyle: {
    fontSize: 15,
    fontWeight: 400,
    color: "#828282",
  },
  imageProduct: {
    borderRadius: 5,
    width: 100,
    height: 100,
    minWidth: 80,
    objectFit: "cover",
  },
  reduceBtn: {
    borderRadius: "2px 0px 0px 2px",
    width: 20,
    height: 20,
  },
  increaseBtn: {
    borderRadius: "0px 2px 2px 0px",
    width: 20,
    height: 20,
  },
  qualityInput: {
    width: 40,
    height: 20,
    textAlign: "center",
    fontSize: 16,
  },
  bottomBtnContainer: {
    display: "flex",
    justifyContent: "space-around",
    gap: 1,
  },
  bottomBtn: {
    margin: 0,
    height: "100%",
    display: "flex",
    fontSize: 12,
  },
};
