import { Box } from "@mui/material";
import React from "react";
import Slider from "react-slick";
import { IProduct } from "../../types/product";
import ProductItem from "./item/ProductItemFnB";
import ProductItemSkeleton from "./ProductItemSkeleton";

interface IProductSlider {
  productList: Array<IProduct>;
  isLoading?: boolean;
}

export default function ProductSlider({ productList, isLoading = false }: IProductSlider) {
  const productListing = {
    dots: false,
    infinite: false,
    // speed: 4000,
    slidesToShow: 2,
    // slidesToScroll: 1,
    autoplay: false,
    arrows: false,
  };

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", gap: 1.5, overflow: "hidden", marginInline: -8 }}>
        {[...Array(4)].map((_, index) => (
          <Box key={index} sx={{ minWidth: "50%", flexShrink: 0 }}>
            <ProductItemSkeleton />
          </Box>
        ))}
      </Box>
    );
  }

  return (
    <Slider {...productListing} style={{ marginInline: -8 }}>
      {productList?.map((product: IProduct, index) => (
        <Box key={product.itemsCode} className="box-product-item">
          <ProductItem item={product} />
        </Box>
      ))}
    </Slider>
  );
}
