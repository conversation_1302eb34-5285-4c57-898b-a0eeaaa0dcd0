import FrameContainerNoIcon from "../components/layout/ContainerNoIconHeader";
import { useNavigate } from "../utils/component-util";
import { Box, Button, Stack, Typography } from "@mui/material";
import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";
import ReportProblemIcon from "@mui/icons-material/ReportProblem";
import { useCart } from "../hooks/useCart";
import { AppEnv } from "../config";
import SuccessIcon from "@/components/icon/SuccessIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useSearchParams } from "react-router-dom";
export default function CheckoutResult() {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  const { state } = useLocation();
  const { clearCart } = useCart();

  const urlParams = new URLSearchParams(state?.path);
  const success =
    AppEnv !== "production" ||
    urlParams.get("vnp_ResponseCode") === "00" ||
    (state?.resultCode === 1 && state?.status === "SUCCESS") ||
    state?.path.includes("appTransID");

  useEffect(() => {
    if (success) {
      // clearCart();
    }
  }, []);

  const renderContent = () => {
    // if (success) {
    return (
      <>
        <SuccessIcon
          primaryColor={appConfig.color.primary}
          secondaryColor={appConfig.color.accent}
        />
        <Typography style={{ ...styles.titleStyle, color: appConfig.textColor.primary }}>
          Đặt hàng thành công
        </Typography>
        <Typography style={{ ...styles.textStyle, color: appConfig.textColor.secondary }}>
          Cảm ơn bạn đã đặt hàng tại{" "}
          <span
            style={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: "160px",
              display: "inline-block",
              verticalAlign: "bottom",
            }}
            title={appConfig?.shopName}
          >
            {appConfig?.shopName}
          </span>
        </Typography>
      </>
    );
    // }
    return (
      <>
        <ReportProblemIcon color="error" sx={{ fontSize: 100 }} />
        <Typography style={{ textAlign: "center" }} variant="h5">
          Thanh toán không thành công
        </Typography>
        <Typography style={{ textAlign: "center" }}>
          Vui lòng kiểm tra lại thông tin thẻ và thực hiện thanh toán lại
        </Typography>
      </>
    );
  };

  return (
    <FrameContainerNoIcon title="Kết quả thanh toán" overrideStyle={{ background: "#e9ebed" }}>
      <Stack py={5} alignItems="center" borderRadius={2} sx={{ backgroundColor: "white" }}>
        {renderContent()}
        <BottomMenu />
      </Stack>
    </FrameContainerNoIcon>
  );
}

const BottomMenu = () => {
  const navigate = useNavigate();
  const appConfig = useConfigApp();
  const [searchParams] = useSearchParams();

  const orderId = searchParams.get("orderId");

  return (
    <div style={styles.bottomCartElement}>
      <Stack direction="row" gap={2} sx={{ ...styles.btnContainer, width: "100%" }}>
        <Button
          sx={{
            color: appConfig.textColor.secondary,
            borderRadius: 1,
            background: appConfig.bgColor.primary,
            flexGrow: 1,
            p: 1.5,
            width: "50%",
            opacity: 0.99,
            "&:hover": {
              opacity: 1,
              background: appConfig.bgColor.primary,
            },
          }}
          variant="contained"
          onClick={() => {
            navigate(`/`);
          }}
        >
          Trang chủ
        </Button>
        <Button
          sx={{
            borderRadius: 1,
            color: "#fff",
            background: appConfig.color.primary,
            flexGrow: 1,
            p: 1,
            width: "50%",
            opacity: 0.9,
            "&:hover": {
              opacity: 1,
              background: appConfig.color.primary,
            },
          }}
          onClick={() => {
            navigate(`/order`);
          }}
          variant="contained"
        >
          Quản lý đơn hàng
        </Button>
      </Stack>
    </div>
  );
};
const styles: Record<string, React.CSSProperties> = {
  bottomCartElement: {
    padding: "20px",
    paddingTop: "25px",
    width: "100vw",
    maxWidth: "450px",
    position: "fixed",
    bottom: "0px",
    zIndex: 1000,
    height: "auto",
    borderTop: "1px solid rgb(223, 223, 223)",
    background: "rgb(255, 255, 255)",
  },
  titleStyle: {
    color: "#1D1D1D",
    fontSize: 23,
    fontWeight: 700,
  },
  textStyle: {
    color: "#8C8C8C",
    fontSize: 14,
    fontWeight: 400,
  },
};
/*
- trường hợp vào vnpay xong huỷ thanh toán
  state = {
    amount: 568000
    appId: "1666682950662020156"
    createAt: *************
    err: 0
    extradata: "{"storeName":"Kho tổng","storeId":"1","orderId":91,"notes":null}"
    isCustom: false
    method: "VNPAY_SANDBOX"
    msg: undefined
    orderId: "221720729763810016918725488_1717549624996"
    path: "/checkout-result"
    resultCode: 0
    status: "PENDING"
    transId: "240605_0807049962773438459351224118349"
    transTime: 0
    updateAt: *************
    zmpOrderId: "221720729763810016918725488_1717549624996"
  }

  - trường hợp thanh toán VNPAY thành công:
  state={
    path: "/checkout-result?env=DEVELOPMENT
      &version=zdev-ab0db573
      &appTransID=240605_0839083352773438459351224118266
      &vnp_Amount=********
      &vnp_BankCode=NCB
      &vnp_BankTranNo=VNP********
      &vnp_CardType=ATM
      &vnp_OrderInfo=thanh+toan+568+000d
      &vnp_PayDate=**************
      &vnp_ResponseCode=00
      &vnp_TmnCode=NUWA0001
      &vnp_TransactionNo=********
      &vnp_TransactionStatus=00
      &vnp_TxnRef=240605_0839083352773438459351224118266
      &vnp_SecureHash=7ea08ab3214db3e444cf7806fd75e666105516799a80b1bc083da958e12198e13f290586eb6f5b561e4308e90d7b5692465b4a812dc53c115f728be83099d89b"}

  - trường hợp thanh toán vi VNPAY thanh cong:
  state = {
    amount: 1000
    appId: "1666682950662020156"
    createAt: *************
    err: 0
    extradata: "{"storeName":"Kho tổng","storeId":"1","orderId":121,"notes":null}"
    isCustom: false
    method: "VNPAY"
    msg: "Giao dịch thành công"
    orderId: "221720729763810016918725488_1718155970222"
    path: "/checkout-result"
    resultCode: 1
    resultMessage: "Giao dịch thành công"
    status: "SUCCESS"
    transId: "240612_0832502222773438459351224118384"
    transTime: 1718155998000
    updateAt: 1718155999188
    zmpOrderId: "221720729763810016918725488_1718155970222"
  }
*/
