import { Icon } from "@/constants/Assets";
import { AppLink } from "@/constants/Const";
import { Copy, ZaloIcon } from "@/constants/IconSvg";
import { COLORS, commonStyle } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useShareReferLink } from "@/hooks/useShareReferLink";
import { RootState } from "@/redux/store";
import { getReferLink, showToast } from "@/utils/common";
import { Box, Button, Dialog, DialogContent, Stack, Typography } from "@mui/material";
import React from "react";
import { useSelector } from "react-redux";

export default function PopUpShareLink({ isOpen, setIsOpen }) {
  const { shareLink } = useShareReferLink();
  const { color, ...appConfig } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const isLoggedIn = !!user?.referralCode;
  const copyReferLink = () => {
    const referLink = isLoggedIn ? getReferLink(user?.referralCode) : AppLink;
    navigator.clipboard
      .writeText(referLink)
      .then(() => {
        showToast({
          content: "Copy thành công",
          type: "success",
        });
      })
      .catch((err) => {
        console.error("Failed to copy text: ", err);
      });
  };

  return (
    <Dialog
      fullWidth
      open={isOpen}
      onClose={() => setIsOpen(false)}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: "5px",
          padding: "0",
        },
      }}
    >
      <DialogContent
        sx={{
          padding: 0,
          backgroundColor: COLORS.bgColor.secondary,
        }}
      >
        <Box>
          <img src={Icon.BannerSharePoupup} width={"100%"} />
        </Box>
        <Stack style={styles.container}>
          {isLoggedIn && (
            <Stack
              direction={"row"}
              justifyContent={"space-between"}
              alignItems={"center"}
              style={{
                background: color.primary,
                padding: 5,
                borderRadius: 5,
              }}
            >
              <Typography
                style={{
                  ...styles.referCode,
                  borderRight: "1px solid rgba(255, 255, 255, 0.30)",
                }}
              >
                Mã giới thiệu của bạn
              </Typography>
              <Typography style={{ ...styles.referCode, fontSize: 20, fontWeight: 700 }}>
                {user?.referralCode}
              </Typography>
            </Stack>
          )}

          <Stack
            justifyContent={"center"}
            alignItems={"center"}
            marginTop={"12px"}
            padding={"0 20px"}
          >
            <Typography style={styles.title}>Chia sẻ ngay</Typography>
            <Typography style={styles.subTitle}>
              Hãy chia sẻ link này cho bạn bè của bạn để nhận hoa hồng trọn đời bất cứ khi nào bạn
              bè mua hàng tại{" "}
              <span
                style={{
                  fontWeight: 700,
                }}
              >
                {appConfig?.shopName}
              </span>
            </Typography>
          </Stack>

          <Stack
            direction={"row"}
            justifyContent={"space-between"}
            alignItems={"center"}
            gap={2}
            marginTop={"32px"}
          >
            <Button
              style={{
                ...styles.button,
                background: "transparent",
                border: `1.5px solid ${color.primary}`,
                color: color.primary,
                boxSizing: "border-box",
              }}
              onClick={copyReferLink}
            >
              <Copy />
              <Typography>Sao chép link</Typography>
            </Button>
            <Button
              style={{
                ...styles.button,
                background: color.primary,
              }}
              onClick={shareLink}
            >
              <ZaloIcon />
              <Typography>Chia sẻ ngay</Typography>
            </Button>
          </Stack>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    background: COLORS.white,
    padding: "20px 15px",
  },
  referCode: {
    padding: "12px 5px",
    // width: "50%",
    textAlign: "center",
    color: COLORS.white,
    fontSize: 15,
    fontWeight: 500,
  },
  title: {
    ...commonStyle.headline18,
    color: COLORS.neutral1,
    lineHeight: "27px",
  },
  subTitle: {
    fontSize: "12px",
    color: COLORS.neutral4,
    fontWeight: 400,
    textAlign: "center",
  },
  button: {
    fontSize: "14px",
    fontWeight: 500,
    borderRadius: "5px",
    width: "50%",
    padding: "13px 5px",
    textAlign: "center",
    color: COLORS.white,
    display: "flex",
    gap: "10px",
  },
};
