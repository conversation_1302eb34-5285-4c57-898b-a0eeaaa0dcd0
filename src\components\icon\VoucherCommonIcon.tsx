import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";

export interface IconCustomProps {
  fillColor?: string;
  strokeColor?: string;
  className?: string;
}

const VoucherCommonIcon: React.FC<IconCustomProps> = ({
  fillColor,
  strokeColor,
}) => {
  const appConfig = useConfigApp();

  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill={fillColor || "none"}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.3125 13.0207C20.3125 11.5832 21.4792 10.4165 22.9167 10.4165V9.37484C22.9167 5.20817 21.875 4.1665 17.7084 4.1665H7.29171C3.12504 4.1665 2.08337 5.20817 2.08337 9.37484V9.89567C3.52087 9.89567 4.68754 11.0623 4.68754 12.4998C4.68754 13.9373 3.52087 15.104 2.08337 15.104V15.6248C2.08337 19.7915 3.12504 20.8332 7.29171 20.8332H17.7084C21.875 20.8332 22.9167 19.7915 22.9167 15.6248C21.4792 15.6248 20.3125 14.4582 20.3125 13.0207Z"
        stroke={strokeColor || appConfig.color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.375 15.3647L15.625 9.11475"
        stroke={strokeColor || appConfig.color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.6192 15.3646H15.6286"
        stroke={strokeColor || appConfig.color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.36924 9.63558H9.37859"
        stroke={strokeColor || appConfig.color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default VoucherCommonIcon;
