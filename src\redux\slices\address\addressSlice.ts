import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IAddress, IBank, IDistrict, IProvince, IWard } from "../../../types/address";
import axios from "axios";

interface AuthState {
  list: IAddress[] | null;
  defaultAddress: IAddress | null;
  currentAddress: IAddress | null;
  isLoading: boolean;
  province: IProvince[];
  district: IDistrict[];
  ward: IWard[];
  bank: IBank[];
}

const initialState: AuthState = {
  list: null,
  defaultAddress: null,
  currentAddress: null,
  isLoading: true,
  province: [],
  district: [],
  ward: [],
  bank: [],
};

export const getAddressList = createAsyncThunk("address/getAddressList", async () => {
  const response: any = await request(
    "get",
    "/api/user/shippingaddressuser/listaddress?skip=0&limit=99"
  );
  return response;
});

export const deleteAddressList = createAsyncThunk(
  "address/deleteAddressList",
  async (shippingAddressId: string) => {
    const response: any = await request(
      "delete",
      `/api/user/shippingaddressuser/delete?shippingAddressId=${shippingAddressId}`
    );
    return response;
  }
);

export const createAddress = createAsyncThunk("address/createAddress", async (data: any) => {
  const response: any = await request("post", "/api/user/shippingaddressuser/create", data);

  return response;
});

export const updateAddress = createAsyncThunk("address/updateAddress", async (data: any) => {
  const response: any = await request("put", "/api/user/shippingaddressuser/update", data);

  return response;
});

// export const getCurrentAddress = createAsyncThunk(
//   "address/getCurrentAddress",
//   async (id: any) => {
//     const response: any = await request("get", `/api/my/address/${id}`);

//     return response;
//   },
// );

export const getProvince = createAsyncThunk("address/getProvince", async () => {
  const response: any = await request("get", "/api/user/province/provinces");
  return response;
});

export const getDistrict = createAsyncThunk("address/getDistrict", async (provinceId: any) => {
  const response: any = await request(
    "get",
    `/api/user/province/districts?provinceId=${provinceId}`
  );
  return response;
});

export const getWard = createAsyncThunk("address/getWard", async (districtId: any) => {
  const response: any = await request("get", `/api/user/province/wards?districtId=${districtId}`);
  return response;
});

export const getBank = createAsyncThunk("address/getBank", async (shortName?: string) => {
  // let query = "";
  // if (shortName) {
  //   query = `?shortName=${shortName}`;
  // }

  const response: any = await request("get", `/api/partner/bank`);

  return response;
});

const addressSlice = createSlice({
  name: "address",
  initialState,
  reducers: {
    setCurrentAddress: (state, action: PayloadAction<IAddress>) => {
      state.currentAddress = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAddressList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAddressList.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        let current = null;
        current = payload.data.find((address: IAddress) => address.isDefault === true);

        if (!current && payload.data.length > 0) {
          current = payload.data[0];
        }

        state.list = payload.data;
        state.defaultAddress = current;
        state.isLoading = false;
      })
      .addCase(getAddressList.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(createAddress.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createAddress.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        state.isLoading = false;
      })
      .addCase(createAddress.rejected, (state, action) => {
        state.isLoading = false;
      })

      // .addCase(getCurrentAddress.pending, (state) => {
      //   state.isLoading = true;
      // })
      // .addCase(
      //   getCurrentAddress.fulfilled,
      //   (state, action: PayloadAction<Partial<any>>) => {
      //     const { payload } = action;
      //     state.isLoading = false;
      //     if (!payload?.data?.id) {
      //       return;
      //     }

      //     state.currentAddress = {
      //       id: payload.data.id,
      //       ...payload.data,
      //     };
      //   },
      // )
      // .addCase(getCurrentAddress.rejected, (state, action) => {
      //   state.isLoading = false;
      // })
      .addCase(getProvince.fulfilled, (state, action: PayloadAction<IProvince[]>) => {
        const { payload } = action;
        state.province = payload;
      })
      .addCase(getDistrict.fulfilled, (state, action) => {
        const { payload } = action;
        state.district = payload;
      })
      .addCase(getWard.fulfilled, (state, action) => {
        const { payload } = action;
        state.ward = payload;
      })
      .addCase(getBank.fulfilled, (state, action) => {
        const { payload } = action;
        state.bank = payload;
      });
  },
});

export const { setCurrentAddress } = addressSlice.actions;
export default addressSlice.reducer;
