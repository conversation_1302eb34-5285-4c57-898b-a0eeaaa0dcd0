import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IPaymentMethod } from "../../../types/paymentMethod";
import { PaymentMethodList } from "@/constants/Const";

interface AuthState {
  listPaymentMethod: IPaymentMethod[];
  isLoading: boolean;
}

const initialState: AuthState = {
  listPaymentMethod: [],
  isLoading: true,
};
export interface DataGetListPayment {
  platform?: string;
  shopId: string;
}

export const getPaymentMethodList = createAsyncThunk(
  "paymentMethod/getPaymentMethodList",
  async (data: DataGetListPayment) => {
    if (data.shopId) {
      const response: any = await request(
        "get",
        `/api/user/paymentuser?ShopId=${data.shopId}&Platform=${
          data.platform || "ZaloMiniApp"
        }&skip=0&limit=99`
      );
      return response;
    }
  }
);

const paymentMethodSlice = createSlice({
  name: "paymentMethod",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getPaymentMethodList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPaymentMethodList.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.listPaymentMethod = payload.data;
        state.isLoading = false;
      })
      .addCase(getPaymentMethodList.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

// export const { setCurrentAddress } = paymentMethodSlice.actions;
export default paymentMethodSlice.reducer;
