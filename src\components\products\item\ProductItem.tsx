import React from "react";
import { Box, Typography } from "@mui/material";
import { IProduct } from "@/types/product";
import { formatPrice } from "@/utils/formatPrice";
import { useNavigate } from "@/utils/component-util";
import { COLOR, COLORS } from "@/constants/themes";
import PopupIconAddToCart from "../../UI/PopupIconAddToCart";
import { Icon } from "@/constants/Assets";
import { getUrlBeImage, formatNumberToK } from "@/utils/common";
import { useConfigApp } from "@/hooks/useConfigApp";
import RatingStars from "../../UI/RatingStars";
import LazyImage from "../../UI/LazyImage";
import AddToCartIcon from "../../icon/AddToCartIcon";

interface ProductItemProps {
  item: IProduct;
  discountType?: string;
}

export const getMinPriceInListVariant = (variants) => {
  if (!variants || variants.length === 0) return 0;

  const availableVariants = variants.filter((variant) => variant.quantity > 0);

  if (availableVariants.length === 0) return 0;

  return Math.min(...availableVariants.map((variant) => variant.price));
};

const ProductItem: React.FC<ProductItemProps> = ({ item, discountType }) => {
  const navigate = useNavigate();
  const { color, container, home } = useConfigApp();
  const appConfig = useConfigApp();

  const containerSystem = container as any;
  const backgroundProduct = containerSystem?.backgroundProduct || "";

  const soldConfig = home?.find(
    (item) => item.type === "ProductList2" && item.style?.sold !== undefined
  )?.style?.sold;

  const salePrice = item?.price || getMinPriceInListVariant(item?.listVariant) || 0;
  const priceReal =
    item?.priceReal ||
    getMinPriceInListVariant(item?.listVariant.map((v) => ({ ...v, price: v.priceReal }))) ||
    0;

  const discountPercent =
    priceReal > 0 ? Math.round(((priceReal - salePrice) / priceReal) * 100) : 0;

  const onNavigateToDetail = () => {
    navigate(`/product-detail/${item.itemsCode}`);
  };

  let URLImage = item?.images && item?.images.length > 0 ? item?.images[0]?.link : "";

  return (
    <Box style={{ ...styles.container, backgroundColor: backgroundProduct }}>
      <Box style={styles.imageContainer}>
        <LazyImage
          src={URLImage}
          alt={item?.itemsName || ""}
          aspectRatio="1"
          style={styles.imageProduct}
          onClick={onNavigateToDetail}
          // loading="lazy"
        />
        {priceReal !== salePrice &&
          discountPercent > 0 &&
          (discountType === "flag" ? (
            <Box
              sx={{
                position: "absolute",
                top: 10,
                left: 10,
                width: 30,
                height: 32,
                display: "flex",
                alignItems: "flex-start",
              }}
            >
              <Box
                sx={{
                  width: 48,
                  height: 52,
                  backgroundColor: "#FFC107",
                  clipPath: "polygon(0 0, 100% 0, 100% 70%, 50% 50%, 0 70%)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Typography
                  sx={{
                    color: "#fff",
                    fontWeight: 400,
                    fontSize: 14,
                    textAlign: "center",
                    marginBottom: 2.5,
                  }}
                >
                  -{discountPercent}%
                </Typography>
              </Box>
            </Box>
          ) : (
            <Typography
              style={{
                position: "absolute",
                top: 10,
                left: 10,
                paddingBlock: "2px",
                paddingInline: "7px",
                textAlign: "center",
                color: "#fff",
                backgroundColor: color.primary,
                fontSize: 12,
                fontWeight: 700,
                borderRadius: 10,
                lineHeight: "17px",
              }}
            >
              -{discountPercent}%
            </Typography>
          ))}
      </Box>

      <Box style={styles.contentContainer}>
        <Box onClick={onNavigateToDetail}>
          <Box style={styles.productTitle}>{item?.itemsName}</Box>
          {soldConfig !== false && (
            <Typography style={styles.soldText}>{`Đã bán ${formatNumberToK(
              item?.sold
            )}`}</Typography>
          )}
        </Box>

        <Box style={{ display: "flex", flexDirection: "column", minHeight: 40 }}>
          {(!item?.isVariant && item?.quantity > 0) ||
          (item?.isVariant && item?.listVariant?.some((variant) => variant.quantity > 0)) ? (
            <>
              <Typography style={{ ...styles.salePriceText, color: color.primary }}>
                {formatPrice(salePrice)}
              </Typography>
              {priceReal !== salePrice ? (
                <Typography style={styles.discountPrice}>{formatPrice(priceReal)}</Typography>
              ) : (
                <Box style={styles.discountPricePlaceholder} />
              )}
            </>
          ) : (
            <Typography style={{ ...styles.salePriceText, color: color.primary }}>
              Liên hệ
            </Typography>
          )}
        </Box>
        <Box
          sx={{
            ...styles.discountPriceTextContainer,
          }}
        >
          <Box
            style={{ ...styles.rateContainer }}
            display={"flex"}
            flexWrap={"wrap-reverse"}
            className="rate-container"
          >
            <RatingStars rating={4.9} />
          </Box>
          {(!item?.isVariant && item?.quantity > 0) ||
          (item?.isVariant && item?.listVariant?.some((variant) => variant.quantity > 0)) ? (
            <PopupIconAddToCart product={item} />
          ) : (
            <AddToCartIcon fillColor={COLORS.neutral12} secondaryColor={COLORS.white} />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default React.memo(ProductItem);

const styles: Record<string, React.CSSProperties> = {
  container: {
    backgroundColor: COLOR.bg.product_item,
    borderRadius: 5,
    cursor: "pointer",
    position: "relative",
  },
  discountText: {
    position: "absolute",
    top: 10,
    left: 10,
    paddingBlock: 2,
    paddingInline: 7,
    textAlign: "center",
    color: COLOR.text.white,
    backgroundColor: COLOR.bg.product_discount,
    fontSize: 12,
    fontWeight: 700,
    borderRadius: 10,
    lineHeight: "17px",
  },
  discountPricePlaceholder: {
    height: 16,
    visibility: "hidden",
    display: "inline-block",
  },
  imageContainer: {
    position: "relative",
    width: "100%",
  },
  imageProduct: {
    borderRadius: 5,
    background: "#EBEBEB",
  },
  contentContainer: {
    paddingInline: 10,
    paddingBlock: 10,
    display: "flex",
    flexDirection: "column",
  },
  priceContainer: {
    display: "flex",
    alignItems: "baseline",
    gap: 4,
    flexWrap: "wrap",
    minHeight: 30,
  },
  productTitle: {
    color: COLOR.text.product_item,
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 2,
    cursor: "pointer",
    fontWeight: 500,
    lineHeight: "18px",
    letterSpacing: "0.14px",
    fontSize: 15,
    minHeight: 38,
  },
  productPriceText: {
    display: "flex",
    color: COLORS.primary1,
    fontWeight: 700,
    fontStyle: "normal",
    fontSize: 14,
    alignItems: "center",
  },
  discountPriceTextContainer: {
    display: "flex",
    fontWeight: 700,
    alignItems: "center",
    justifyContent: "space-between",
    color: COLORS.primary1,
  },
  addBtn: {
    alignSelf: "flex-end",
    padding: 0,
    minWidth: 20,
  },
  discountPrice: {
    textDecoration: "line-through",
    fontSize: 11,
    fontWeight: 400,
    color: COLOR.text.product_price,
  },
  salePriceText: {
    fontSize: 16,
    fontWeight: 700,
  },
  rateContainer: {
    display: "flex",
    alignItems: "baseline",
    gap: 2,
  },
  rateText: {
    background: "#E6E6E6",
    color: "#747474",
    fontStyle: "normal",
    paddingInline: 2,
    paddingBlock: 2,
    borderRadius: 3,
    display: "flex",
    alignItems: "baseline",
    gap: 1,
  },
  soldText: {
    color: COLOR.text.product_sold,
    fontSize: 11,
    fontWeight: 400,
    fontStyle: "normal",
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 2,
    minHeight: 16,
  },
};

// Thêm keyframes animation vào cuối file
const styleSheet = document.createElement("style");
styleSheet.textContent = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;
document.head.appendChild(styleSheet);
