import React from "react";
export const Loading = ({ progress }: { progress: number }) => {
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        background: "rgba(255,255,255,0.85)",
        zIndex: 1000,
        overflow: "hidden",
      }}
    >
      <div style={{ marginBottom: 24 }}>
        <div
          style={{
            width: 60,
            height: 60,
            border: "6px solid #F8C7C7",
            borderTop: "6px solid #FF4B4B",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
          }}
        />
      </div>
      <div style={{ fontSize: 28, fontWeight: 700, color: "#FF4B4B", letterSpacing: 1 }}>
        Loading {Math.round(progress * 100)}%
      </div>
      <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
    </div>
  );
};
