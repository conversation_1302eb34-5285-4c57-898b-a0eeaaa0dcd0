import React, { useEffect, useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { getUser, updateMe } from "@/redux/slices/authen/authSlice";
import { showToast } from "@/utils/common";
import { Autocomplete, Box, Popper, Stack, TextField, Typography } from "@mui/material";
import { COLORS } from "@/constants/themes";
import { getBank } from "@/redux/slices/address/addressSlice";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { useConfigApp } from "@/hooks/useConfigApp";

const validationSchema = Yup.object().shape({
  bankAccountName: Yup.string().required("Chủ sở hữu không được để trống"),
  bankAccountNumber: Yup.string().required("Số tài khoản không được để trống"),
  bankName: Yup.string().required("Ngân hàng không được để trống"),
  // branch: Yup.string().required("Chi nhánh không được để trống"),
  identityCardNumber: Yup.string().required("Mã số thuế không được để trống"),
  taxCode: Yup.string(),
});

type FormData = {
  bankAccountName?: string;
  bankAccountNumber?: string;
  bankName?: string;
  // branch?: string;
  identityCardNumber?: string;
  taxCode?: string;
};

export default function PopupEditBank() {
  const { color } = useConfigApp();
  const [open, setOpen] = useState(false);
  const user = useSelector((state: RootState) => state.auth.user);
  const { bank } = useSelector((state: RootState) => state.address);

  const dispatch = useDispatch<AppDispatch>();

  const formOptions: any = {
    resolver: yupResolver(validationSchema),
    values: {
      bankAccountName: user?.bankAccountName,
      bankName: user?.bankName,
      bankAccountNumber: user?.bankAccountNumber,
      identityCardNumber: user?.identityCardNumber,
      taxCode: user?.taxCode,
    },
  };

  const { control, handleSubmit, register, reset } = useForm<FormData>(formOptions);

  useEffect(() => {
    if (!bank || bank.length == 0) {
      dispatch(getBank());
    }
  }, [bank]);

  const onSubmit = async (values) => {
    const res: any = await dispatch(updateMe(values));
    if (!res.error) {
      showToast({
        content: "Cập nhật thông tin thành công",
        type: "success",
      });
      await dispatch(getUser());
      setOpen(false);
      reset();
    } else {
      showToast({
        content: "Quá trình cập nhật lỗi. Vui lòng thử lại",
        type: "error",
      });
    }
  };

  const handleClose = () => {
    setOpen(false);
    reset();
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  return (
    <Box width={"100%"} paddingInline={5}>
      <Button
        onClick={handleClickOpen}
        fullWidth
        variant="outlined"
        sx={{ borderRadius: 99, color: color.primary, borderColor: color.primary }}
      >
        Cập nhật
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: "12px",
            paddingBottom: "20px",
          },
        }}
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogContent>
            <Stack
              alignItems={"center"}
              gap={0.6}
              style={{
                marginBottom: 12,
              }}
            >
              <Typography style={styles.title}>Thông tin cơ bản</Typography>
              <Typography style={styles.subTitle}>
                Cập nhật thông tin chính xác giúp chúng tôi có thông tin và hỗ trợ chăm sóc bạn tốt
                hơn
              </Typography>
            </Stack>
            <Stack gap={2}>
              <Controller
                name="bankName"
                control={control}
                defaultValue={(bank as Array<any>).find(
                  (o) => `${o.vn_name} (${o.shortName})` === user?.bankName
                )}
                render={({ field }) => (
                  <Autocomplete
                    disablePortal
                    options={bank}
                    fullWidth
                    PopperComponent={(props) => (
                      <Popper
                        {...props}
                        style={{
                          ...props.style,
                          maxHeight: 200,
                          overflowY: "auto",
                        }}
                      />
                    )}
                    getOptionLabel={(option: any) => `${option.vn_name} (${option.shortName})`}
                    onChange={(e, value: any) => {
                      if (value) {
                        field.onChange(value?.shortName ?? "");
                      }
                    }}
                    renderInput={(params) => <TextField label="Ngân hàng" {...params} />}
                    defaultValue={(bank as Array<any>).find((o) => o.shortName === user?.bankName)}
                    isOptionEqualToValue={(option, value) => {
                      return option.shortName == value;
                    }}
                  />
                )}
              />

              <TextField
                id="outlined-required"
                label="Số tài khoản"
                {...register("bankAccountNumber")}
                sx={{
                  "& .MuiInputBase-root": {
                    ...styles.inputStyle,
                  },
                }}
              />

              <TextField
                id="outlined-required"
                label="Chủ tài khoản"
                {...register("bankAccountName")}
                sx={{
                  "& .MuiInputBase-root": {
                    ...styles.inputStyle,
                  },
                }}
              />

              {/* <TextField
                id="outlined-required"
                label="Chi nhánh"
                {...register("branch")}
                sx={{
                  "& .MuiInputBase-root": {
                    ...styles.inputStyle,
                  },
                }}
              /> */}
              <TextField
                id="outlined-required"
                label="Số CCCD"
                {...register("identityCardNumber")}
                sx={{
                  "& .MuiInputBase-root": {
                    ...styles.inputStyle,
                  },
                }}
              />
              <TextField
                id="outlined-required"
                label="Mã số thuế"
                {...register("taxCode")}
                sx={{
                  "& .MuiInputBase-root": {
                    ...styles.inputStyle,
                  },
                }}
              />
            </Stack>
          </DialogContent>
          <DialogActions
            style={{
              justifyContent: "center",
            }}
          >
            <Button
              type="submit"
              style={{
                ...styles.acceptBtn,
                background: color.primary,
              }}
            >
              Xác nhận
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  title: {
    color: COLORS.black,
    fontWeight: 700,
    fontSize: "15px",
  },
  subTitle: {
    color: COLORS.neutral14,
    fontWeight: 400,
    fontSize: "11px",
    textAlign: "center",
  },
  inputStyle: {
    borderRadius: "8px",
  },
  inputPlaceHolder: {
    color: COLORS.black,
  },
  acceptBtn: {
    fontSize: "14px",
    fontWeight: 700,
    color: COLORS.white,
    padding: "7px 20px",
    borderRadius: "8px",
  },
};
