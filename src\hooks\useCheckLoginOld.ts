import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { useAlert } from "../redux/slices/alert/useAlert";
import { Icon } from "../constants/Assets";
import { useConfigApp } from "./useConfigApp";
import CheckIcon from "@/components/icon/CheckIcon";
import { useNavigate } from "react-router-dom";
import { Router } from "@/constants/Route";

export const useCheckLogin = () => {
  const appConfig = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const { showAlert } = useAlert();
  const navigate = useNavigate();

  const checkLogin = (callback: () => void) => {
    if (!user) {
      showAlert({
        title: appConfig?.shopName,
        content: "Vui lòng kích hoạt tài khoản để tiếp tục!",
        icon: "⚠️",
        buttons: [
          {
            title: "OK",
            action: () => {
              navigate(Router.profile.index);
            },
          },
        ],
      });
    } else {
      callback();
    }
  };

  return {
    checkLogin,
  };
};
