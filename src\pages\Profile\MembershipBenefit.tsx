import FrameContainerFull from "@/components/layout/ContainerFluid";
import LoginPopup from "@/components/LoginPopup";
import { Platform } from "@/config";
import { StorageKeys } from "@/constants/storageKeys";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { authZalo, getUser, getUserZalo } from "@/redux/slices/authen/authSlice";
import { fetchMembershipRanks } from "@/redux/slices/membershiplevel/membershiplevelSlice";
import type { AppDispatch, RootState } from "@/redux/store";
import { mapError } from "@/utils/common";
import { getItem } from "@/utils/storage";
import { Box, CircularProgress, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

export default function MembershipBenefit() {
  const appConfig = useConfigApp();
  const color = appConfig.color;
  const dispatch = useDispatch<AppDispatch>();
  const { showAlert } = useAlert();
  const calcCartAfterLogin = useCalcCartAfterLogin();
  const [loading, setLoading] = useState(false);
  const [openLoginPopup, setOpenLoginPopup] = useState(false);

  const shopId = useSelector((state: RootState) => state.appInfo.shopId);
  const { ranks, isLoading, error } = useSelector((state: RootState) => state.membershiplevel);
  const user = useSelector((state: RootState) => state.auth.user);

  const onClickRegister = async () => {
    setLoading(true);
    const refCode = await getItem(StorageKeys.RefCode);
    const res = await dispatch(authZalo(refCode)).unwrap();
    if (res && res.idFor) {
      if (Platform === "zalo") await dispatch(getUserZalo());
      await dispatch(getUser());
      await calcCartAfterLogin();
      showAlert({
        icon: "✅",
        title: "Kích hoạt tài khoản thành công",
      });
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoading(false);
  };

  const onClickToActiveProfile = () => {
    showAlert({
      title: appConfig.shopName,
      content: "Vui lòng kích hoạt tài khoản để tiếp tục!",
      icon: "⚠️",
      buttons: [
        {
          title: "Đóng",
          action: () => {},
        },
        {
          title: "Kích hoạt",
          action: () => {
            Platform === "zalo" ? onClickRegister() : setOpenLoginPopup(true);
          },
        },
      ],
    });
  };

  useEffect(() => {
    if (!user) {
      onClickToActiveProfile();
      return;
    }
    if (shopId) {
      dispatch(fetchMembershipRanks({ shopId }));
    }
  }, [shopId, dispatch, user]);

  return (
    <>
      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={loading}
      />
      <FrameContainerFull title="Quyền lợi của bạn">
        <Box sx={{ bgcolor: appConfig.container?.backgroundColor, minHeight: "100vh", p: 0 }}>
          <Box sx={{ p: 2, pt: 3 }}>
            <Typography fontWeight={700} fontSize={18} color={color.primary} mb={2}>
              Chính sách điểm thành viên
            </Typography>
            {isLoading ? (
              <Stack alignItems="center" py={4}>
                <CircularProgress />
              </Stack>
            ) : error ? (
              <Typography color="error">{error}</Typography>
            ) : (
              <Stack gap={2}>
                {ranks.map((item: any, idx: number) => {
                  const isCurrent =
                    user?.membershipLevel?.levelName &&
                    item.levelName === user.membershipLevel.levelName;
                  return (
                    <Box
                      key={`${item.levelName || ""}_${item.name || ""}_${idx}`}
                      sx={{
                        borderRadius: 3,
                        bgcolor: isCurrent ? "#FFFEF3" : "#fff",
                        boxShadow: "0 2px 12px #0000000A",
                        p: 2.5,
                        position: "relative",
                        minHeight: 120,
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      <Box
                        sx={{
                          width: 56,
                          height: 56,
                          borderRadius: "50%",
                          bgcolor: "#fff",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          boxShadow: "0 0 6px #0001",
                          mr: 2,
                        }}
                      >
                        <img
                          src={item.image?.link || "/images/error-image.png"}
                          alt={item.levelName || "rank image"}
                          style={{ width: 56, height: 56 }}
                        />
                      </Box>
                      <Box flex={1} width="80%">
                        <Typography
                          sx={{
                            fontWeight: 700,
                            fontSize: 18,
                            color: color.primary,
                            width: "75%",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                          }}
                        >
                          {item.levelName || item.name}
                          {isCurrent && (
                            <Box
                              component="span"
                              fontWeight={500}
                              fontSize={14}
                              color="#2677E8"
                              ml={2}
                            >
                              Hạng hiện tại
                            </Box>
                          )}
                        </Typography>
                        <Typography fontSize={14} color="#222" mt={0.5}>
                          Chi tiêu tối thiểu:{" "}
                          <span style={{ color: color.primary, fontWeight: 600 }}>
                            {item.spendingThreshold?.toLocaleString?.() || 0} VND
                          </span>
                        </Typography>
                        <Typography fontSize={14} color="#222" mt={0.5}>
                          Quyền lợi:{" "}
                          <span style={{ color: color.primary, fontWeight: 600 }}>
                            Tích điểm {item.pointRate} %
                          </span>
                        </Typography>
                        {item.isCurrent && (
                          <>
                            <Typography fontSize={14} color="#2677E8" mt={1}>
                              Bạn cần chi tiêu thêm{" "}
                              <b>{item.needToNext?.toLocaleString?.() || 0} VND</b> để nâng hạng
                            </Typography>
                            <Box
                              sx={{
                                width: "100%",
                                height: 8,
                                bgcolor: "#E0E7EF",
                                borderRadius: 4,
                                mt: 1,
                              }}
                            >
                              <Box
                                sx={{
                                  width: `${item.progress || 0}%`,
                                  height: 8,
                                  bgcolor: "#44B57B",
                                  borderRadius: 4,
                                }}
                              />
                            </Box>
                          </>
                        )}
                      </Box>
                      <Box
                        sx={{
                          position: "absolute",
                          right: 0,
                          top: "50%",
                          transform: "translateY(-50%)",
                          height: "100%",
                          zIndex: 0,
                          overflow: "hidden",
                          pointerEvents: "none",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <Box
                          component="img"
                          src={item.image?.link || "/images/error-image.png"}
                          alt={item.levelName}
                          sx={{
                            width: 130,
                            height: "auto",
                            opacity: 0.2,
                            objectFit: "cover",
                            objectPosition: "right center",
                            transform: "translateX(40%)",
                            userSelect: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </Box>
                    </Box>
                  );
                })}
              </Stack>
            )}
          </Box>
        </Box>
      </FrameContainerFull>
    </>
  );
}
