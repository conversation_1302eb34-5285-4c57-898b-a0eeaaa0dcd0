import React, { useState, useEffect } from "react";
import { Box, Typography, Button, Tabs, Tab } from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { getIncomeHistory, getSpendingHistory } from "@/redux/slices/point/pointHistorySlice";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import { useConfigApp } from "@/hooks/useConfigApp";

const mockGift = [];

function PointHistoryItem({ icon, title, date, desc, point }: any) {
  const { color } = useConfigApp();
  return (
    <Box
      sx={{
        bgcolor: "#fff",
        borderRadius: 3,
        p: 2,
        mb: 2,
        display: "flex",
        alignItems: "center",
        boxShadow: "0 1px 4px 0 rgba(0,0,0,0.03)",
      }}
    >
      <Box
        sx={{
          width: 48,
          height: 48,
          borderRadius: "50%",
          bgcolor: "#f6f9ff",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          mr: 2,
        }}
      >
        <img
          src={icon}
          alt={title}
          style={{ width: 48, height: 48, objectFit: "contain", display: "block" }}
        />
      </Box>
      <Box sx={{ flex: 1 }}>
        <Typography sx={{ fontWeight: 700, fontSize: 16, color: color.primary }}>
          {title}
        </Typography>
        <Typography sx={{ fontSize: 12, color: "#888" }}>{date}</Typography>
        <Typography sx={{ fontSize: 13, color: "#161616" }}>{desc}</Typography>
      </Box>
      <Box
        sx={{
          ml: 2,
          display: "flex",
          alignItems: "center",
          position: "relative",
          width: 72,
          height: 72,
        }}
      >
        <img
          src={point > 0 ? "/images/addpoint.svg" : "/images/deductpoint.svg"}
          alt={point > 0 ? "+" : "-"}
          style={{ width: 72, height: 72, position: "absolute", left: 0, top: 0 }}
        />
        <Typography
          sx={{
            color: point > 0 ? "#00B14F" : "#FFA9A9",
            fontWeight: 700,
            fontSize: 19,
            position: "absolute",
            left: -2,
            top: 21,
            width: 72,
            textAlign: "center",
          }}
        >
          {point > 0 ? `+${point}` : point}
        </Typography>
      </Box>
    </Box>
  );
}

export default function PointHistory() {
  const navigate = useNavigate();
  const [tab, setTab] = useState(0);
  const dispatch = useDispatch<AppDispatch>();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { incomeHistory, spendingHistory, isLoading, error } = useSelector(
    (state: RootState) => state.pointHistory
  );
  const { color } = useConfigApp();
  const appConfig = useConfigApp();

  useEffect(() => {
    if (tab === 0 && shopId) {
      dispatch(getIncomeHistory({ shopId, skip: 0, limit: 99 }));
    } else if (tab === 1 && shopId) {
      dispatch(getSpendingHistory({ shopId, skip: 0, limit: 99 }));
    }
  }, [tab, shopId, dispatch]);

  const tabData = [
    incomeHistory.map((item: any) => ({
      icon: "/images/diemdonhang.png",
      title: item.type === "Redeem" ? "Nhận điểm" : item.type,
      date: item.created ? new Date(item.created).toLocaleString("vi-VN") : "",
      desc: item.note,
      point: item.pointsEarned,
    })),
    spendingHistory.map((item: any) => ({
      icon: "/images/quydoi.svg",
      title: item.type === "Redeem" ? "Tiêu điểm" : item.type,
      date: item.created ? new Date(item.created).toLocaleString("vi-VN") : "",
      desc: item.note,
      point: -Math.abs(item.pointsEarned),
    })),
    mockGift,
  ];
  const tabLabels = ["Lượt nhận", "Lượt chi", "Quà của tôi"];

  return (
    <FrameContainerFull title="Lịch sử đổi điểm">
      <Box
        sx={{
          bgcolor: appConfig.container?.backgroundColor,
          minHeight: "100vh",
          height: "100dvh",
          display: "flex",
          flexDirection: "column",
          p: 0,
        }}
      >
        {/* Tabs */}
        <Box sx={{ px: 2, pt: 2, bgcolor: "transparent" }}>
          <Box sx={{ display: "flex", gap: 0, alignItems: "center", whiteSpace: "nowrap" }}>
            {tabLabels.map((label, idx) => (
              <React.Fragment key={label}>
                <Button
                  variant={tab === idx ? "contained" : "outlined"}
                  sx={{
                    borderRadius: 99,
                    fontWeight: 500,
                    fontSize: 15,
                    color: tab === idx ? "#fff" : color.primary,
                    bgcolor: tab === idx ? color.primary : "#fff",
                    borderColor: color.primary,
                    boxShadow: "none",
                    px: 3,
                    textTransform: "none",
                    minWidth: 0,
                    height: 40,
                  }}
                  onClick={() => setTab(idx)}
                >
                  {label}
                </Button>
                {idx < tabLabels.length - 1 && (
                  <Box
                    sx={{
                      mx: 1,
                      color: "#D8D8D8",
                      fontWeight: 400,
                      fontSize: 22,
                      userSelect: "none",
                    }}
                  >
                    |
                  </Box>
                )}
              </React.Fragment>
            ))}
          </Box>
        </Box>
        {/* List */}
        <Box sx={{ flex: 1, overflowY: "auto", p: 2, pt: 2, pb: 8 }}>
          {tabData[tab].length === 0 ? (
            tab === 2 ? (
              <Box
                sx={{
                  textAlign: "center",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "100%",
                  minHeight: 360,
                  transform: "translateY(-30px)",
                }}
              >
                <img
                  src="/images/nogift.svg"
                  alt="no gift"
                  style={{ width: 160, height: 160, objectFit: "contain", display: "block" }}
                />
                <Typography sx={{ color: "#888", mt: 2, mb: 2, fontSize: 16 }}>
                  Bạn chưa có quà
                </Typography>
                <Button
                  variant="text"
                  sx={{
                    color: color.primary,
                    fontWeight: 500,
                    fontSize: 15,
                    textDecoration: "underline",
                  }}
                  onClick={() => navigate("/voucher")}
                >
                  Đổi Stars để lấy quà ngay!
                </Button>
              </Box>
            ) : (
              <Typography sx={{ color: "#888", textAlign: "center", mt: 4 }}>
                Chưa có dữ liệu
              </Typography>
            )
          ) : (
            tabData[tab].map((item, idx) => <PointHistoryItem key={idx} {...item} />)
          )}
        </Box>
      </Box>
    </FrameContainerFull>
  );
}
