import { Box, Grid, Stack, Typography } from "@mui/material";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import FrameContainerFull from "../../components/layout/ContainerFluid";
import NewsItem from "../../components/news/NewsItem";
import { getListNewsUser } from "../../redux/slices/news/newsListSlice";
import { AppDispatch, RootState } from "../../redux/store";
import { INews } from "../../types/news";
export default function Posts() {
  const dispatch = useDispatch<AppDispatch>();
  const newsList = useSelector((state: RootState) => state.newsList.userNewsList);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const location = useLocation();

  useEffect(() => {
    if (shopId) dispatch(getListNewsUser(shopId));
  }, [shopId]);

  const pageTitle = location.state?.title || "Tin tức";

  return (
    <FrameContainerFull title={pageTitle} overrideStyle={{ background: "transparent" }}>
      <Stack style={styles.stackStyle}>
        <Typography style={styles.headingStyle}>Danh mục bài viết</Typography>
        <Grid container>
          {newsList && newsList.length > 0 ? (
            newsList.map((news: INews, index) => {
              return (
                <NewsItem
                  key={index}
                  news={news}
                  containerStyles={{ paddingLeft: 5, paddingRight: 5 }}
                  titleFromParent={pageTitle}
                />
              );
            })
          ) : (
            <Stack direction="row" justifyContent={"center"} padding={2} width={"100%"}>
              Không có dữ liệu
            </Stack>
          )}
        </Grid>
      </Stack>
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  stackStyle: {
    background: "#FFF",
    margin: 10,
    padding: 10,
    borderRadius: "5px",
  },
  headingStyle: {
    color: "#1D1D1D",
    fontSize: "18px",
    fontStyle: "normal",
    fontWeight: 700,
    lineHeight: "normal",
    letterSpacing: "0.18px",
    marginBlock: 10,
    marginLeft: 10,
  },
  loadingBoxStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    padding: 4,
  },
};
