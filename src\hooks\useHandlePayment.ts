import { Router } from "../constants/Route";
import { useNavigate } from "../utils/component-util";
import { useEffect } from "react";
import { events, EventName } from "zmp-sdk";

export const useHandlePayment = () => {
  const navigate = useNavigate();

  useEffect(() => {
    events.on(EventName.OpenApp, (data) => {
      // trường hợp thanh toán COD thành công
      // data.path = /checkout-result?env=DEVELOPMENT&version=zdev-655f4b9a&appTransID=240601_1923507902773438459351224118883
      // console.log("🚀 ~ events.on ~ OpenApp:", data);
      if (data?.path) {
        navigate(data?.path, {
          state: data,
        });
      }
    });

    events.on(EventName.OnDataCallback, (resp) => {
      // trường hợp đang ở popup thanh toán thì bấm quay lại hoặc đóng
      // console.log("🚀 ~ events.on ~ OnDataCallback:", resp);
      const { appTransID, eventType } = resp || {};
      if (appTransID || eventType === "PAY_BY_CUSTOM_METHOD") {
        navigate("/result", {
          state: resp,
        });
      }
    });

    events.on(EventName.PaymentClose, (data = {}) => {
      console.log("🚀 ~ events.on ~ data:", data);
      // console.log("🚀 ~ events.on ~ PaymentClose:", data);
      navigate(Router.checkoutResult, {
        state: data,
      });
    });
  }, []);
};

/*
  trường hợp tt vnpay thành công:
  PaymentClose:
  data = {
    amount: 1000
    appId: "1666682950662020156"
    createAt: 1718155973043
    err: 0
    extradata: "{"storeName":"Kho tổng","storeId":"1","orderId":121,"notes":null}"
    isCustom: false
    method: "VNPAY"
    msg: "Giao dịch thành công"
    orderId: "221720729763810016918725488_1718155970222"
    path: "/checkout-result"
    resultCode: 1
    resultMessage: "Giao dịch thành công"
    status: "SUCCESS"
    transId: "240612_0832502222773438459351224118384"
    transTime: 1718155998000
    updateAt: 1718155999188
    zmpOrderId: "221720729763810016918725488_1718155970222"
  }
*/
