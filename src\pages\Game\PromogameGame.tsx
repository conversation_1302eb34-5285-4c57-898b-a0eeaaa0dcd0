import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { axiosInstance, request } from "@/utils/request";
import { RootState, AppDispatch } from "@/redux/store";
import { EventName, PromogamePlayer, PromogamePlayerHandle } from "promogame-player";
import { Box, Typography, Button } from "@mui/material";
import ArrowLeftIcon from "@/components/icon/ArrowLeftIcon";
import HomeIcon from "@/components/icon/HomeIcon";
import { COLORS } from "@/constants/themes";
import { Loading } from "./GameLoading";
import zmpSdk from "zmp-sdk";
import { fetchGamificationData } from "@/redux/slices/gamification/gamificationSlice";
import { Router } from "@/constants/Route";
import { AppEnv, Platform } from "@/config";
import { openShareSheet } from "zmp-sdk/apis";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import { getUser } from "@/redux/slices/authen/authSlice";
import { getItem } from "@/utils/storage";
import { StorageKeys } from "@/constants/storageKeys";

const PromogameGame: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch<AppDispatch>();
  const { user, loading } = useSelector((state: RootState) => state.auth);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { checkLogin } = useCheckLogin();
  const promogamePlayerRef = useRef<PromogamePlayerHandle>(null);
  const {
    campaignId,
    apiUrl,
    checksum,
    requestedAt,
    thumbnailLink,
    isLoading,
    error,
    isAvailable,
    status,
    message,
    startTime,
    endTime,
  } = useSelector((state: RootState) => state.gamification);

  useEffect(() => {
    if (!shopId) return;
    if (loading) return;
    if (!user) {
      Platform === "zalo" ? checkLogin(() => {}) : navigate(Router.homepage);
      return;
    }
    dispatch(fetchGamificationData());
  }, [shopId, user, loading]);

  useEffect(() => {
    if (shopId) {
      const init = async () => {
        const token = await getItem(StorageKeys.AccessToken);

        if (token) {
          axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
          await dispatch(getUser());
        } else {
          dispatch({ type: "auth/setLoading", payload: false });
        }
      };
      init();
    }
  }, [shopId]);

  const externalUserId = user?.userId;
  const avatar = user?.avatar;
  const displayName = user?.fullname;
  const email = user?.email;
  const phone = user?.phoneNumber;

  const onShareGame = async () => {
    const path =
      AppEnv === "dev"
        ? `/game?env=TESTING&refCode=${user?.referCode}`
        : `/game?env=TESTING&refCode=${user?.referCode}`;
    const data = await openShareSheet({
      type: "zmp_deep_link",
      data: {
        title: "Chơi là trúng",
        description: "Vui game trúng thưởng, cùng tham gia nhận quà",
        thumbnail: thumbnailLink ?? "",
        path,
      },
    });
    console.log(data);
  };

  const handleNavigation = (route: string) => {
    setTimeout(() => {
      navigate(route);
    }, 100);
  };

  const handleBack = () => {
    setTimeout(() => {
      navigate(-1);
    }, 100);
  };

  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString);
    return date.toLocaleString("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const renderGameStatus = () => {
    // Only show game status if not loading and game is not available
    if (!isLoading && !isAvailable) {
      return (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          height="100vh"
          padding={3}
          textAlign="center"
          className="game-bg"
        >
          <Box
            sx={{
              background: "rgba(255, 255, 255, 0.95)",
              borderRadius: "20px",
              padding: "20px 30px",
              boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              maxWidth: "400px",
              width: "100%",
              position: "relative",
              zIndex: 1,
            }}
          >
            <Typography
              variant="body1"
              color={COLORS.neutral2}
              fontWeight={700}
              mb={2}
              fontSize={20}
            >
              {message}
            </Typography>

            {startTime && endTime && (
              <Box mt={2}>
                <Typography variant="body2" color={COLORS.neutral3} fontWeight={500} mb={1}>
                  Thời gian diễn ra:
                </Typography>
                <Typography variant="body2" color={COLORS.neutral2} fontWeight={600}>
                  {formatDateTime(startTime)} - {formatDateTime(endTime)}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      );
    }
    return null;
  };
  return (
    <Box
      height={"100vh"}
      position="relative"
      sx={{
        overflow: "hidden",
        width: "100%",
        maxWidth: "100vw",
      }}
    >
      <div
        style={{
          position: "absolute",
          top: 50,
          left: 10,
          zIndex: 10,
          background: "#F8C7C7",
          borderRadius: 32,
          display: "flex",
          alignItems: "center",
          border: "2px solid #fff",
          height: 35,
          minWidth: 80,
          padding: "0 8px",
        }}
      >
        <button
          onClick={handleBack}
          style={{
            background: "none",
            border: "none",
            outline: "none",
            display: "flex",
            alignItems: "center",
            padding: 5,
            cursor: "pointer",
          }}
        >
          <ArrowLeftIcon fillColor={COLORS.neutral2} />
        </button>
        <div style={{ width: 1, height: 20, background: COLORS.neutral4, margin: "0 5px" }} />
        <button
          onClick={() => handleNavigation(Router.homepage)}
          style={{
            background: "none",
            border: "none",
            outline: "none",
            display: "flex",
            alignItems: "center",
            padding: 5,
            cursor: "pointer",
          }}
        >
          <HomeIcon fillColor={COLORS.neutral2} />
        </button>
      </div>

      {/* Show game status if game is not available or not started/ended */}
      {renderGameStatus()}

      {/* Game player - only show if game is available and active */}
      {isAvailable &&
        status !== "NOT_STARTED" &&
        status !== "ENDED" &&
        campaignId &&
        checksum &&
        apiUrl &&
        requestedAt && (
          <PromogamePlayer
            ref={promogamePlayerRef}
            campaignId={campaignId}
            externalUserId={externalUserId}
            requestedAt={requestedAt}
            checksum={checksum}
            userAvatar={avatar}
            userDisplayName={displayName}
            userEmail={email}
            userPhone={phone}
            loadingComponent={Loading}
            className={"promo-game"}
            apiUrl={apiUrl}
            zmpSdk={zmpSdk}
            onEventReceived={(eventName, params) => {
              if (eventName === EventName.OPEN_GIFT_LINK) {
                promogamePlayerRef.current?.unload();
                setTimeout(() => {
                  navigate(Router.voucher.index + "?tab=1");
                }, 100);
              } else if (eventName === EventName.OPEN_SHARE) {
                promogamePlayerRef.current?.unload();
                onShareGame();
              }
            }}
            registerEvents={[EventName.OPEN_GIFT_LINK, EventName.OPEN_SHARE]}
          />
        )}
    </Box>
  );
};

export default PromogameGame;
