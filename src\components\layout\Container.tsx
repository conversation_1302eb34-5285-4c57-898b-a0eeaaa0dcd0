import { Box, Container, Grid, Stack, Typography } from "@mui/material";
import React, { ReactNode, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "zmp-ui";
import styles from "../../css/styles.module.css";
import { COLORS } from "../../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import ArrowLeftIcon from "../icon/ArrowLeftIcon";
import { useCart } from "@/hooks/useCart";

export default function FrameContainer({
  children,
  title,
  overrideStyle,
  childOrdStl,
  onBackClick,
  rightComponent,
}: {
  children: ReactNode;
  title: string;
  overrideStyle?: React.CSSProperties;
  childOrdStl?: React.CSSProperties;
  onBackClick?: () => void;
  rightComponent?: ReactNode;
}) {
  const { color, bgColor, header } = useConfigApp();
  const [positionCss, setPositionCss] = useState({});
  const { cartPayment, setCartAndSave } = useCart();
  const appConfig = useConfigApp();
  const handleScroll = (e) => {
    const bottom = e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
    if (bottom) {
      // setPositionCss({
      //     position: 'fixed',
      // })
    }
  };

  return (
    <Page style={{ backgroundColor: appConfig.container?.backgroundColor, ...overrideStyle }}>
      <Header
        backgroundColor={appConfig.header?.backgroundColor || color.secondary}
        textColor={COLORS.black}
        title={title}
        style={{ ...positionCss, fontWeight: 700 }}
        onBackClick={onBackClick}
        backIcon={<ArrowLeftIcon fillColor="black" secondaryColor="white" />}
      />
      <Box
        position={"fixed"}
        top={17}
        right={{
          xs: "16px",
          sm: "calc((100vw - 450px) / 2 + 22px)",
        }}
        zIndex={1000}
        sx={{
          maxWidth: { xs: "calc(100vw - 120px)", sm: "auto" },
        }}
      >
        {rightComponent}
      </Box>
      <div className={styles.pageContent}>
        <Container onScroll={handleScroll} style={{ paddingBottom: 64, ...childOrdStl }}>
          <Stack>{children}</Stack>
        </Container>
      </div>
    </Page>
  );
}
