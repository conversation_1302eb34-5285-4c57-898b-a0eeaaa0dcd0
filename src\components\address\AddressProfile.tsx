import {
  Box,
  <PERSON>ton,
  Divider,
  FormControlLabel,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import PopupAddAddress from "../UI/PopupAddAddress";
import store, { AppDispatch, RootState } from "../../redux/store";
import {
  getAddressList,
  getDistrict,
  getProvince,
  getWard,
  setCurrentAddress,
} from "../../redux/slices/address/addressSlice";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { useNavigate } from "../../utils/component-util";
import { COLORS, commonStyle } from "../../constants/themes";
import { IAddress } from "../../types/address";
import SmallCheckIcon from "../icon/SmallCheckIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Icon } from "@/constants/Assets";
import { i } from "vite/dist/node/types.d-aGj9QkWt";

export default function AddressProfile() {
  const { list } = useSelector((state: RootState) => state.address);
  const { user } = useSelector((state: RootState) => state.auth);
  const location = useLocation();
  const navigate = useNavigate();
  const { color } = useConfigApp();
  const dispatch = useDispatch<AppDispatch>();
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState<"add" | "edit">("add");
  const [selectedAddress, setSelectedAddress] = useState<IAddress | undefined>(
    undefined,
  );
  const { currentAddress } = useSelector((state: RootState) => state.address);

  useEffect(() => {
    if (!Array.isArray(list) || !list.length) {
      store.dispatch(getAddressList());
    }
  }, []);

  const handleAddAddress = () => {
    setDialogMode("add");
    setSelectedAddress(undefined);
    setOpenDialog(true);
  };

  const handleEditAddress = async (address: IAddress) => {
    setDialogMode("edit");
    await dispatch(getProvince());
    await dispatch(getDistrict(address.provinceId));
    await dispatch(getWard(address.districtId));
    setSelectedAddress(address);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedAddress(undefined);
  };

  const renderAddress = (item: IAddress) => {
    return (
      <Box>
        <Typography style={styles.infoStyle}>
          <Typography style={styles.nameStyle}>{item.fullName}</Typography>
          <Divider orientation="vertical" flexItem style={{ height: 18 }} />
          <Typography style={styles.phoneStyle}>{item.phoneNumber}</Typography>
          {item.isDefault && (
            <Typography
              color={color.primary}
              borderColor={color.primary}
              style={styles.defaultStyle}
            >
              Mặc định
            </Typography>
          )}
        </Typography>
        <Typography style={styles.addressStyle}>{item.address}</Typography>
        <Typography style={styles.addressStyle}>
          {item?.wardName}, {item?.districtName}, {item?.provinceName}
        </Typography>
      </Box>
    );
  };

  return (
    <Stack gap={1} sx={styles.contentContainer}>
      {Array.isArray(list) && list.length > 0 && (
        <Box style={styles.container} p={1}>
          {!location.state?.shouldGoBack ? (
            // Chỉ hiển thị renderAddress khi shouldGoBack là false
            list.map((item, index) => (
              <Stack p={1} gap={1} key={index} width={"100%"}>
                {renderAddress(item)}
                <Typography
                  color={color.primary}
                  style={{ marginTop: 3, fontSize: 12 }}
                  onClick={(e) => {
                    handleEditAddress(item);
                  }}
                >
                  Sửa
                </Typography>
                {index !== list.length - 1 && (
                  <Divider style={{ paddingTop: 8 }} />
                )}
              </Stack>
            ))
          ) : (
            // Sử dụng RadioGroup khi shouldGoBack không phải là false
            <RadioGroup
              row
              aria-labelledby="demo-form-control-label-placement"
              name="position"
              defaultValue="top"
            >
              {list.map((item, index) => (
                <Stack key={index} width={"100%"}>
                  <Stack p={1} gap={1} width={"100%"}>
                    <FormControlLabel
                      value={item.id.toString()}
                      control={
                        <Radio
                          checkedIcon={
                            <SmallCheckIcon fillColor={color.primary} />
                          }
                          icon={<img src={Icon.tick_circle} alt="" />}
                          onChange={async (e: any) => {
                            await dispatch(setCurrentAddress(item));
                            location.state?.shouldGoBack && navigate(-1);
                          }}
                        />
                      }
                      label={renderAddress(item)}
                      checked={item.id == currentAddress?.id}
                      labelPlacement="start"
                      sx={{ justifyContent: "space-between", ml: 0 }}
                    />
                  </Stack>
                  <Stack paddingInline={1} width={"100%"}>
                    <Typography
                      color={color.primary}
                      style={{ fontSize: 12 }}
                      onClick={(e) => {
                        handleEditAddress(item);
                      }}
                    >
                      Sửa
                    </Typography>
                    {index !== list.length - 1 && (
                      <Divider style={{ paddingTop: 8 }} />
                    )}
                  </Stack>
                </Stack>
              ))}
            </RadioGroup>
          )}
        </Box>
      )}
      <Button
        style={{
          background: color.primary,
          color: COLORS.white,
        }}
        variant="outlined"
        onClick={handleAddAddress}
      >
        Thêm Địa Chỉ
      </Button>
      {openDialog && (
        <PopupAddAddress
          mode={dialogMode}
          address={dialogMode === "edit" ? selectedAddress : undefined}
          onClose={handleCloseDialog}
        />
      )}
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    marginTop: 1.25,
    marginBottom: 4,
  },
  container: {
    backgroundColor: "#FFF",
    borderRadius: 10,
  },
  infoStyle: {
    display: "flex",
    gap: 10,
  },
  nameStyle: {
    fontSize: 14,
    fontWeight: 500,
  },
  phoneStyle: {
    fontSize: 14,
    color: "#828282",
  },
  addressStyle: {
    fontSize: 12,
    color: "#828282",
  },
  defaultStyle: {
    fontSize: 12,
    border: "1px solid",
    display: "inline-block",
    paddingInline: "3px",
  },
};
