import React from "react";
import { <PERSON>, Button, Grid, <PERSON>ack, Typography, useTheme } from "@mui/material";
import UserCard from "@/components/user-card";
import { LevelIcon, LevelName } from "@/constants/Const";
import { COLORS, commonStyle } from "@/constants/themes";
import LocationIcon from "@/components/icon/LocationIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { IBranchItem } from "@/types/branch";
import ArrowRightIcon from "@/components/icon/ArrowRightIcon";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";

export default function BranchNow({ branch }: { branch: IBranchItem }) {
  const { color, bgColor } = useConfigApp();
  const navigate = useNavigate();

  return (
    <Grid container sx={{ display: "flex", justifyContent: "space-between" }}>
      <Grid item xs={1.5}>
        <Box height={"100%"} display={"flex"} alignItems={"center"}>
          <LocationIcon primaryColor={color.primary} secondaryColor={color.secondary} />
        </Box>
      </Grid>
      {branch && (
        <Grid item xs={9}>
          <Stack direction={"column"} alignItems={"start"}>
            <Typography
              py={2}
              fontSize={13}
              fontWeight={400}
              lineHeight={1.5}
              paddingBottom={0}
              sx={{
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "normal",
              }}
            >
              {[
                branch?.branchName,
                branch?.address,
                branch?.wardName,
                branch?.districtName,
                branch?.provinceName,
              ]
                .filter(Boolean)
                .join(", ")}
            </Typography>
            <Button
              onClick={() => navigate(Router.branch.index)}
              color="info"
              style={{ ...styles.btnSeeMore, marginTop: "2px", marginBottom: "4px" }}
            >
              Xem thêm
              <KeyboardArrowRightIcon style={{ color: "#959595", fontSize: 22 }} />
            </Button>
          </Stack>
        </Grid>
      )}
      <Grid item xs={1.5}>
        <Box
          sx={{
            backgroundRepeat: "no-repeat",
            backgroundSize: "cover",
            color: color.primary,
            fontSize: 13,
          }}
          height={"100%"}
          display={"flex"}
          alignItems={"center"}
        >
          {/* {"500m"} */}
        </Box>
      </Grid>
    </Grid>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    position: "relative",
    display: "flex",
    gap: 1,
    alignItems: "center",
  },
  btnSeeMore: {
    fontSize: 12,
    fontWeight: 400,
    color: "#959595",
    padding: 0,
  },
};
