import React, { useEffect, useRef, useState } from "react";

const InfiniteScroll = ({ children, loader, fetchMore, hasMore, endMessage, className }) => {
  const pageEndRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (hasMore) {
      // Lấy đúng container scroll
      const scrollContainer = document.getElementById("search-product-list-container");
      const observer = new window.IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasMore && !isLoading) {
            setIsLoading(true);
            Promise.resolve(fetchMore())
              .then(() => setIsLoading(false))
              .catch(() => setIsLoading(false));
          }
        },
        {
          root: scrollContainer || null,
          rootMargin: "0px 0px 300px 0px", // <-- <PERSON>ad sớm hơn khi còn cách 300px
          threshold: 0.1,
        }
      );
      if (pageEndRef.current) {
        observer.observe(pageEndRef.current);
      }
      return () => {
        if (pageEndRef.current) {
          observer.unobserve(pageEndRef.current);
        }
      };
    }
  }, [hasMore, isLoading, fetchMore]);

  return (
    <div className={className}>
      {children}
      {hasMore ? (
        <div ref={pageEndRef} style={{ minHeight: 40 }}>
          {loader}
        </div>
      ) : (
        endMessage
      )}
    </div>
  );
};

export default InfiniteScroll;
