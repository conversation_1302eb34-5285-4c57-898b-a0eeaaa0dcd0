import React from "react";

export interface IconCustomProps {
  fillColor?: string;
  className?: string;
}

const SearchHeader: React.FC<IconCustomProps> = ({ fillColor }) => {
  return (
    <svg
      width="45"
      height="45"
      viewBox="0 0 45 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="22.5" cy="22.5" r="22.5" fill="#EBEBEB" />
      <path
        d="M20.2679 28.7499C24.9525 28.7499 28.7501 24.9523 28.7501 20.2678C28.7501 15.5832 24.9525 11.7856 20.2679 11.7856C15.5834 11.7856 11.7858 15.5832 11.7858 20.2678C11.7858 24.9523 15.5834 28.7499 20.2679 28.7499Z"
        stroke={fillColor}
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M33.6904 33.6904L28.6904 28.6904"
        stroke={fillColor}
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle cx="19" cy="16" r="2" fill={fillColor} />
      <circle cx="23" cy="16" r="1" fill={fillColor}/>
    </svg>
  );
};

export default SearchHeader;
