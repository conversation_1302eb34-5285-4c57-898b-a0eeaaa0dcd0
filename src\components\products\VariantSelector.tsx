import { <PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { IProduct, IProductVariant, ISelectedVariant } from "../../types/product";
import { useConfigApp } from "@/hooks/useConfigApp";

interface VariantSelectorProps {
  productVariants: IProductVariant[];
  // selected?: IProductVariant | null;
  onVariantChange: (variant: ISelectedVariant, product?: IProduct | null) => void;
  open?: boolean;
}
const VariantSelector: React.FC<VariantSelectorProps> = ({
  productVariants,
  // selected,
  onVariantChange,
  open,
}) => {
  const appConfig = useConfigApp();
  const [selectedVariant, setSelectedVariant] = useState<ISelectedVariant | null>(null);

  useEffect(() => {
    if (!open) {
      const minPriceIndex = productVariants.reduce((minIndex, current, currentIndex, array) => {
        if (current.quantity <= 0) return minIndex;

        if (array[minIndex].quantity <= 0) return currentIndex;

        return current.price < array[minIndex].price ? currentIndex : minIndex;
      }, 0);

      setSelectedVariant(productVariants[minPriceIndex]);
    }
  }, [open, productVariants]);

  const groupVariantsByName = (variants: IProductVariant[]): Record<string, string[]> => {
    return variants.reduce((acc, variant) => {
      const variantGroups = [
        { name: variant.variantNameOne, value: variant.variantValueOne },
        { name: variant.variantNameTwo, value: variant.variantValueTwo },
        { name: variant.variantNameThree, value: variant.variantValueThree },
      ];

      variantGroups.forEach(({ name, value }) => {
        if (name && value) {
          if (!acc[name]) {
            acc[name] = [];
          }
          if (!acc[name].includes(value)) {
            acc[name].push(value);
          }
        }
      });
      return acc;
    }, {} as Record<string, string[]>);
  };

  const groupedVariants = groupVariantsByName(productVariants);

  const isOptionDisabled = (variantName: string, value: string): boolean => {
    if (!selectedVariant) return false;

    const selectedValues = [
      {
        name: selectedVariant.variantNameOne,
        value: selectedVariant.variantValueOne,
      },
      {
        name: selectedVariant.variantNameTwo,
        value: selectedVariant.variantValueTwo,
      },
      {
        name: selectedVariant.variantNameThree,
        value: selectedVariant.variantValueThree,
      },
    ];

    const filteredSelectedValues = selectedValues.filter(
      (selected) => selected.name !== variantName && selected.value
    );

    const isValidCombination = productVariants.some((variant) => {
      if (
        (variantName === variant.variantNameOne && value === variant.variantValueOne) ||
        (variantName === variant.variantNameTwo && value === variant.variantValueTwo) ||
        (variantName === variant.variantNameThree && value === variant.variantValueThree)
      ) {
        return filteredSelectedValues.every((selected) => {
          if (selected.name === variant.variantNameOne) {
            return selected.value === variant.variantValueOne;
          }
          if (selected.name === variant.variantNameTwo) {
            return selected.value === variant.variantValueTwo;
          }
          if (selected.name === variant.variantNameThree) {
            return selected.value === variant.variantValueThree;
          }
          return true;
        });
      }
      return false;
    });

    return !isValidCombination;
  };

  const handleVariantSelect = (variantName: string, value: string) => {
    let newSelectedVariant = {
      variantNameOne: selectedVariant?.variantNameOne ?? "",
      variantValueOne: selectedVariant?.variantValueOne ?? "",
      variantNameTwo: selectedVariant?.variantNameTwo ?? "",
      variantValueTwo: selectedVariant?.variantValueTwo ?? "",
      variantNameThree: selectedVariant?.variantNameThree ?? "",
      variantValueThree: selectedVariant?.variantValueThree ?? "",
    };

    const isAlreadySelected =
      (newSelectedVariant.variantNameOne === variantName &&
        newSelectedVariant.variantValueOne === value) ||
      (newSelectedVariant.variantNameTwo === variantName &&
        newSelectedVariant.variantValueTwo === value) ||
      (newSelectedVariant.variantNameThree === variantName &&
        newSelectedVariant.variantValueThree === value);

    if (isAlreadySelected) {
      if (newSelectedVariant.variantNameOne === variantName) {
        newSelectedVariant.variantNameOne = "";
        newSelectedVariant.variantValueOne = "";
      } else if (newSelectedVariant.variantNameTwo === variantName) {
        newSelectedVariant.variantNameTwo = "";
        newSelectedVariant.variantValueTwo = "";
      } else if (newSelectedVariant.variantNameThree === variantName) {
        newSelectedVariant.variantNameThree = "";
        newSelectedVariant.variantValueThree = "";
      }
    } else {
      productVariants.forEach((variant) => {
        if (variantName === variant.variantNameOne && variant.variantValueOne === value) {
          newSelectedVariant.variantNameOne = variantName;
          newSelectedVariant.variantValueOne = value;
        } else if (variantName === variant.variantNameTwo && variant.variantValueTwo === value) {
          newSelectedVariant.variantNameTwo = variantName;
          newSelectedVariant.variantValueTwo = value;
        } else if (
          variantName === variant.variantNameThree &&
          variant.variantValueThree === value
        ) {
          newSelectedVariant.variantNameThree = variantName;
          newSelectedVariant.variantValueThree = value;
        }
      });
    }

    setSelectedVariant(newSelectedVariant);
    // onVariantChange(newSelectedVariant, selected);
    onVariantChange(newSelectedVariant);
  };

  return (
    <Box paddingBlock={1}>
      {Object.entries(groupedVariants).map(([variantName, variantValues]) => (
        <Stack key={variantName} style={styles.variantContainer} gap={1}>
          <Box>
            <Typography style={{ ...styles.variantName, display: "block" }}>
              <span
                style={{
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  maxWidth: "97%",
                  display: "inline-block",
                  verticalAlign: "middle",
                }}
              >
                {variantName}
              </span>
              <span
                style={{
                  color: "red",
                  marginLeft: 4,
                  fontSize: "inherit",
                  lineHeight: "inherit",
                  verticalAlign: "middle",
                }}
              >
                *
              </span>
            </Typography>
          </Box>
          <Box>
            {[...variantValues]
              .sort((a, b) => {
                const aNum = Number(a),
                  bNum = Number(b);
                if (!isNaN(aNum) && !isNaN(bNum)) {
                  return aNum - bNum;
                }
                return (a || "").localeCompare(b || "");
              })
              .map((value, index) => (
                <Button
                  key={index}
                  disabled={isOptionDisabled(variantName, value)}
                  style={{
                    ...styles.variantBtn,
                    background:
                      (selectedVariant?.variantNameOne === variantName &&
                        selectedVariant?.variantValueOne === value) ||
                      (selectedVariant?.variantNameTwo === variantName &&
                        selectedVariant?.variantValueTwo === value) ||
                      (selectedVariant?.variantNameThree === variantName &&
                        selectedVariant?.variantValueThree === value)
                        ? appConfig.color.primary
                        : isOptionDisabled(variantName, value)
                        ? "#e0e0e0"
                        : "#f0f0f0",
                    maxWidth: "100%",
                  }}
                  onClick={() => handleVariantSelect(variantName, value)}
                >
                  <Typography
                    sx={{
                      color:
                        (selectedVariant?.variantNameOne === variantName &&
                          selectedVariant?.variantValueOne === value) ||
                        (selectedVariant?.variantNameTwo === variantName &&
                          selectedVariant?.variantValueTwo === value) ||
                        (selectedVariant?.variantNameThree === variantName &&
                          selectedVariant?.variantValueThree === value)
                          ? "#ffffff"
                          : appConfig.color.primary,
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      maxWidth: "100%",
                      display: "inline-block",
                    }}
                  >
                    {value}
                  </Typography>
                </Button>
              ))}
          </Box>
        </Stack>
      ))}
    </Box>
  );
};

export default VariantSelector;

const styles: Record<string, React.CSSProperties> = {
  variantBtn: {
    paddingBlock: 4,
    marginRight: "5px",
    marginBottom: "5px",
  },
  variantContainer: {
    marginBlock: 10,
  },
  variantName: {
    color: "#878787",
    fontSize: 14,
    fontWeight: 500,
  },
  imageStyle: {
    width: 30,
    height: 30,
  },
  optionContainer: {
    display: "flex",
    alignItems: "center",
    gap: 4,
  },
  optionnName: {
    color: "#6C6C6C",
    fontSize: 13,
    fontWeight: 400,
  },
};
