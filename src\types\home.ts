export enum keyComponent {
  "BannerWithBranch" = "BannerWithBranch",
  "BranchLocation" = "BranchLocation",
  "MenuAction" = "MenuAction",
  "MenuCategory" = "MenuCategory",
  "BannerHome" = "BannerHome",
  "ActiveAccount" = "ActiveAccount",
  "FollowOA" = "FollowOA",
  "ProductList" = "ProductList",
  "ListNews" = "ListNews",
  "Html" = "Html",
  "Policy" = "Policy",
  "ShopInfo" = "ShopInfo",
  "Game1" = "Game1",
  "Game2" = "Game2",
  "Game3" = "Game3",
  "Banner2" = "Banner2",
  "ListNews2" = "ListNews2",
  "ProductList2" = "ProductList2",
  "PointBox" = "PointBox",
  "Banner3" = "Banner3",
  "MenuCategory2" = "MenuCategory2",
  "Voucher" = "Voucher",
  "Voucher1" = "Voucher1",
}

export interface KeyComponent {
  type: string;
  show: boolean;
  style?: {
    title?: string;
    category: "slice" | "slice2" | "grid" | "1row" | "2rows" | "banner1" | "banner2";
    itemInRow?: number;
    background?: string;
  };

  items?: any[];
}
