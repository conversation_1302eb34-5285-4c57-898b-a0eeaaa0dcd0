import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const CartInfoIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
    >
      <path
        d="M2.07926 8.854H11.9751"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.24593 17.1875H8.32927"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.9334 17.1875H15.1001"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.9126 12.5314V16.7814C22.9126 20.4377 21.9855 21.3543 18.2876 21.3543H6.70426C3.00635 21.3543 2.07926 20.4377 2.07926 16.7814V8.21891C2.07926 4.56266 3.00635 3.646 6.70426 3.646H15.1001"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.8712 4.30212L16.0066 8.1667C15.8608 8.31253 15.715 8.6042 15.6837 8.81253L15.4754 10.2917C15.4025 10.823 15.7775 11.198 16.3087 11.125L17.7879 10.9167C17.9962 10.8854 18.2879 10.7396 18.4337 10.5938L22.2983 6.7292C22.965 6.06253 23.2775 5.2917 22.2983 4.31253C21.3087 3.32295 20.5379 3.63545 19.8712 4.30212Z"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.3186 4.85449C19.652 6.03158 20.5686 6.94824 21.7353 7.27116"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default CartInfoIcon;
