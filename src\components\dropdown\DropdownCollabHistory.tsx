import React from "react";
import { Select, MenuItem, FormControl, InputLabel } from "@mui/material";

function DropdownCollabHistory({ label, options, defaultValue, onChange }) {
  return (
    <FormControl
      sx={{
        width: "100%",
        height: 42,
        ".MuiOutlinedInput-notchedOutline": {
          border: "1px transparent white",
          borderRadius: "10px",
        },
      }}
    >
      <InputLabel>{label}</InputLabel>
      <Select
        value={defaultValue}
        onChange={(e) => onChange(e.target.value)}
        sx={{
          backgroundColor: "#FFFFFF", // Set the background color to white
          "& .MuiSelect-select": {
            backgroundColor: "#FFFFFF", // Ensure the selected item's background is also white
          },
          "&:focus": {
            backgroundColor: "#FFFFFF", // Keep the background white even when focused
          },
          "&:hover": {
            backgroundColor: "#FFFFFF", // Keep the background white on hover as well
          },
        }}
      >
        {options.map((option, index) => (
          <MenuItem
            key={index}
            value={option?.id}
            sx={{
              fontSize: "16px",
              fontWeight: 400,
              lineHeight: "19.36px",
              textAlign: "left",
            }}
          >
            {option.name || option}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}

export default DropdownCollabHistory;
