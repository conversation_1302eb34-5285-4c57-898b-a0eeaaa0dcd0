import BtnAction from "@/components/common/BtnAction";
import Banner from "@/components/home/<USER>";
import ListItem from "@/components/home/<USER>";
import ProductListGridSliceV2 from "@/components/home/<USER>/ProductListGridSliceRetail";
import ListSlider from "@/components/home/<USER>";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import NewsItem from "@/components/news/NewsItem";
import NewsItemHorizontalV2 from "@/components/news/newsitemhorizontal/NewsItemHorizontalRetail";
import NoDataView from "@/components/UI/NoDataView";
import PopupAdvertise from "@/components/UI/PopupAdvertise";
import VoucherListGrid from "@/components/voucher/VoucherListSlide";
import { Router } from "@/constants/Route";
import { COLORS } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getAdvertisements } from "@/redux/slices/advertise/advertisementSlice";
import { getListNewsUser } from "@/redux/slices/news/newsListSlice";
import {
  getProductListByCategory,
  setSearchCondition,
} from "@/redux/slices/product/productListSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { keyComponent } from "@/types/home";
import { INews } from "@/types/news";
import { useNavigate } from "@/utils/component-util";
import { Box, Container, Grid, Skeleton, Typography } from "@mui/material";

import ArrowRightIcon from "@/components/icon/ArrowRightIcon";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { configAppView } from "zmp-sdk/apis";
import ListNews from "@/components/news/ListNews";

configAppView({
  headerColor: "#1843EF",
  headerTextColor: "white",
  hideAndroidBottomNavigationBar: true,
  hideIOSSafeAreaBottom: true,
  actionBar: {
    hide: true,
    leftButton: "none",
  },
  statusBarType: "transparent",
});

const HomePage: React.FunctionComponent = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { home } = useConfigApp();
  const { container } = useConfigApp();
  const appConfig = useConfigApp();

  const { user } = useSelector((state: RootState) => state.auth);
  const { productCategoryInHome } = useSelector((state: RootState) => state.product);
  const { list } = useSelector((state: RootState) => state.branch);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { advertisements, isLoading } = useSelector((state: RootState) => state.advertisement);
  const [listArticle, setListArticle] = useState<INews[]>([]);
  const [isLoadingNews, setIsLoadingNews] = useState(false);
  const { thumbnailLink } = useSelector((state: RootState) => state.gamification);

  useEffect(() => {
    getProductData();
  }, [productCategoryInHome]);

  const containerStyleItem = container as any;
  const containerBgColor = containerStyleItem?.backgroundColor || "";
  const containerBgImage = containerStyleItem?.backgroundImage || "";

  const containerStyle = {
    paddingInline: 0,
    backgroundColor: containerBgImage ? undefined : containerBgColor,
    backgroundImage: containerBgImage ? `url(${containerBgImage})` : undefined,
    backgroundSize: containerBgImage ? "cover" : undefined,
    backgroundRepeat: containerBgImage ? "no-repeat" : undefined,
    backgroundPosition: containerBgImage ? "center" : undefined,
  };

  const getProductData = async () => {
    if (productCategoryInHome.length > 0) {
      await dispatch(
        getProductListByCategory({
          categoryId: productCategoryInHome[0].categoryId,
        })
      );
    }
  };

  const renderNewsSkeletonLoading = (isSlice = false) => {
    const imageHeight = 110;
    const borderRadius = 1;
    const titleHeight = 20;
    const dateHeight = 14;
    if (isSlice) {
      return (
        <Box sx={{ width: "100%", px: 0 }}>
          <Box
            sx={{
              display: "flex",
              gap: 1.5,
              overflow: "hidden",
            }}
          >
            {[...Array(1)].map((_, index) => (
              <Box
                key={index}
                sx={{
                  minWidth: "100%",
                  flexShrink: 0,
                  borderRadius: borderRadius,
                  p: 0.5,
                }}
              >
                <Skeleton
                  variant="rectangular"
                  height={imageHeight}
                  sx={{ borderRadius: borderRadius }}
                />
                <Skeleton variant="text" width="80%" height={titleHeight} sx={{ mt: 1 }} />
                <Skeleton
                  variant="text"
                  width="50%"
                  height={dateHeight}
                  sx={{ mt: 0.5, bgcolor: "grey.200" }}
                />
              </Box>
            ))}
          </Box>
        </Box>
      );
    }

    return (
      <Grid container spacing={2} sx={{ width: "100%", margin: 0 }}>
        {[...Array(2)].map((_, index) => (
          <Grid item xs={6} key={index}>
            <Box sx={{ mb: 1.5, borderRadius: borderRadius, p: 0.5 }}>
              <Skeleton
                variant="rectangular"
                height={imageHeight}
                sx={{ borderRadius: borderRadius }}
              />
              <Skeleton variant="text" width="80%" height={titleHeight} sx={{ mt: 1 }} />
              <Skeleton
                variant="text"
                width="50%"
                height={dateHeight}
                sx={{ mt: 0.5, bgcolor: "grey.200" }}
              />
            </Box>
          </Grid>
        ))}
      </Grid>
    );
  };

  const fetchListNewsUser = async () => {
    if (shopId) {
      setIsLoadingNews(true);
      try {
        const res = await dispatch(getListNewsUser(shopId));
        setListArticle(res?.payload?.data);
      } finally {
        setIsLoadingNews(false);
      }
    }
  };
  useEffect(() => {
    fetchListNewsUser();
  }, [shopId]);
  const endTime = dayjs().add(1, "hour").add(30, "minute").toISOString();
  useEffect(() => {
    if (shopId) {
      dispatch(getAdvertisements(shopId));
    }
  }, [dispatch, shopId]);

  const onNavigateToProduction = async () => {};

  const onClickItemCategory = async (cateKey) => {
    if (cateKey) {
      dispatch(
        setSearchCondition({
          categoryId: cateKey.id,
        })
      );
    }
    navigate(Router.menu);
  };

  const onClickSeeAllNewProduct = async () => {
    dispatch(
      setSearchCondition({
        categoryId: null,
      })
    );

    navigate(Router.menu);
  };

  const onNavigateToPost = (title?: string) => {
    navigate(Router.post.index, { state: { title } });
  };

  const { promotion } = useConfigApp();

  const pageTitle =
    Array.isArray(container?.navbar) && container.navbar[1]?.text
      ? container.navbar[1].text
      : "Ưu đãi";

  const renderPromotionComponent = (item: any, index: number) => {
    if (!item.show && item.type !== "Header") return null;
    let com;
    switch (item.type) {
      case "Voucher1":
        com = (
          <Box key={index} px={2}>
            <VoucherListGrid tabIndex={0} config={item} />
          </Box>
        );
        break;
      case "BannerWithBranch":
        com = (
          <Box key={index} pt={1}>
            <Banner item={item} />
          </Box>
        );
        break;
      case "ProductList2":
        com = (
          <Box key={index} px={2} pt={2}>
            <ProductListGridSliceV2
              title={item?.style?.title}
              item={item}
              onNavigateToProduction={() => {}}
            />
          </Box>
        );
        break;
      case keyComponent.ListNews2:
        if (item.style?.category == "grid") {
          com = (
            <Box key={index} p={2}>
              <ListItem
                title={item?.style?.title || ""}
                seeAll={() => onNavigateToPost(item?.style?.title)}
                btnAction={
                  <BtnAction
                    text={`Xem thêm`}
                    icon={<ArrowRightIcon fillColor={"#878787"} />}
                    eventAction={() => onNavigateToPost(item?.style?.title)}
                  />
                }
              >
                {Array.isArray(listArticle) && listArticle.length > 0 ? (
                  listArticle.map((news: INews, index) => (
                    <NewsItem
                      news={news}
                      key={String(index)}
                      titleFromParent={item?.style?.title}
                    />
                  ))
                ) : (
                  <NoDataView content="Không có tin tức nào" />
                )}
              </ListItem>
            </Box>
          );
        } else if (item.style?.category == "slice") {
          com = (
            <Box key={index} py={2} px={2}>
              <ListSlider
                className={"list-news-slider"}
                title={item?.style?.title}
                titleStyle={{ color: appConfig.color.primary }}
                seeAll={() => onNavigateToPost(item?.style?.title)}
                btnAction={
                  <BtnAction
                    text={`Xem tất cả`}
                    styleOverride={{ color: appConfig.color.primary, fontSize: 12 }}
                    icon={<ArrowRightIcon fillColor={"#878787"} />}
                    eventAction={() => onNavigateToPost(item?.style?.title)}
                  />
                }
                sliceConfig={{
                  dots: false,
                  infinite: false,
                  slidesToShow: 1.5,
                  autoplay: false,
                  arrows: false,
                }}
              >
                {isLoadingNews ? (
                  renderNewsSkeletonLoading(true)
                ) : Array.isArray(listArticle) && listArticle.length > 0 ? (
                  listArticle.map((news: INews, index: number) => (
                    <NewsItemHorizontalV2
                      news={news}
                      key={index}
                      titleFromParent={item?.style?.title}
                      containerStyles={{
                        backgroundColor: COLORS.white,
                      }}
                    />
                  ))
                ) : (
                  <NoDataView content="Không có tin tức nào" />
                )}
              </ListSlider>
            </Box>
          );
        }
        break;
      case "ListNews":
        com = (
          <Box key={index} p={2}>
            <Box
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: 8,
              }}
            >
              <Typography style={{ fontWeight: 700, fontSize: 18 }}>
                {item?.style?.title || "Tin tức"}
              </Typography>
              <BtnAction
                text="Xem thêm"
                icon={<ArrowRightIcon fillColor={"#878787"} />}
                eventAction={() =>
                  navigate("/posts", { state: { title: item?.style?.title ?? "Tin tức" } })
                }
              />
            </Box>
            <ListNews list={listArticle} titleFromParent={item?.style?.title} />
          </Box>
        );
        break;
      default:
        com = null;
    }
    return com;
  };

  return (
    <FrameContainerFull title={pageTitle}>
      <Box sx={{ overflowX: "hidden", position: "relative" }}>
        {!isLoading && advertisements.length > 0 && (
          <PopupAdvertise advertisements={advertisements} />
        )}
      </Box>
      <Container style={containerStyle}>
        {/* Hiển thị promotion thay vì home */}
        {promotion?.map((item, index) => renderPromotionComponent(item, index))}
      </Container>
    </FrameContainerFull>
  );
};

export default HomePage;
