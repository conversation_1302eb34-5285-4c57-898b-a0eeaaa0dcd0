import {
  <PERSON>,
  <PERSON><PERSON>,
  Cir<PERSON>P<PERSON>ress,
  Container,
  <PERSON>rid,
  <PERSON>ack,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useRecoilValue } from "recoil";
import FrameContainer from "../../components/layout/Container";
import UserCard from "../../components/user-card";
import { Icon } from "../../constants/Assets";
import { userState } from "../../state";
import { useTheme } from "@mui/material/styles";
import { useNavigate } from "../../utils/component-util";
import { COLORS } from "../../constants/themes";
import ProfileHeader from "./ProfileHeader";
import { useSelector } from "react-redux";
import store, { RootState } from "../../redux/store";
import { getUser } from "../../redux/slices/authen/authSlice";
import { getReport } from "../../redux/slices/team/team";

export default function MemberShip() {
  const { user } = useSelector((state: RootState) => state.auth);
  const { teamReport } = useSelector((state: RootState) => state.team);
  const [viewMenberShip, setViewMemberShip] = useState(0);
  const contentMemberShip = [
    {
      class: "Đồng",
      color: "#CC9D8D",
      title: "THÀNH VIÊN 1",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
    {
      class: "Bạc",
      color: "#B9CADC",
      title: "THÀNH VIÊN 2",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
    {
      class: "Vàng",
      color: "#E7B15B",
      title: "THÀNH VIÊN 3",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
    {
      class: "Bạch kim",
      color: "#6C7886",
      title: "THÀNH VIÊN 4",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
    {
      class: "Kim cương",
      color: "#72D8E4",
      title: "THÀNH VIÊN 5",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
  ];
  const theme = useTheme();

  useEffect(() => {
    store.dispatch(getUser());
    store.dispatch(getReport());
  }, []);

  if (!user)
    return (
      <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
        <CircularProgress />
      </Stack>
    );

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString("vi-VN") + "đ";
  };

  const formatNumber = (number) => {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  };

  return (
    <FrameContainer title="Hạng thành viên">
      <Stack
        style={{
          borderRadius: 20,
          marginBlock: 12,
          background: "#fff",
          padding: 12,
          boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.25)",
        }}
      >
        {user && <ProfileHeader />}
        <Grid container spacing={2} marginBlock={1}>
          <Grid item xs={6}>
            <Stack
              textAlign="center"
              boxShadow="0px 0px 4px rgba(0, 0, 0, 0.25)"
              borderRadius={4}
              padding={2}
              fontSize={17}
              gap={1}
            >
              <span>Tổng chi tiêu</span>
              <span style={{ color: "#2677E8", fontWeight: 700 }}>
                {formatCurrency(parseInt(user?.mySale || "0"))}
              </span>
            </Stack>
          </Grid>
          <Grid item xs={6}>
            <Stack
              textAlign="center"
              boxShadow="0px 0px 4px rgba(0, 0, 0, 0.25)"
              borderRadius={4}
              padding={2}
              fontSize={17}
              gap={1}
              width="100%"
            >
              <span>Điểm thưởng</span>
              <span style={{ color: "#2677E8", fontWeight: 700 }}>
                {formatNumber(
                  (user?.bonusPoint !== undefined && user?.bonusPoint) || 0
                )}
              </span>
            </Stack>
          </Grid>
        </Grid>
        <Stack>
          <Stack direction="row" alignItems={"center"} gap={4}>
            <Stack
              style={{ background: "#44B57B" }}
              direction="row"
              color="#fff"
              padding={2}
              width={35}
              height={35}
              borderRadius={"50%"}
              alignItems="center"
              justifyContent={"center"}
            >
              TQ
            </Stack>
            <Stack>
              <p style={{ textAlign: "center" }}>
                Bạn cần tích luỹ thêm{" "}
                <b style={{ color: COLORS.primary1 }}>100k </b>
                để nâng hạng VIP
              </p>
            </Stack>
            <Stack
              style={{ background: "#44B57B" }}
              direction="row"
              color="#fff"
              padding={2}
              width={35}
              height={35}
              borderRadius={"50%"}
              alignItems="center"
              justifyContent={"center"}
            >
              VIP
            </Stack>
          </Stack>
          <Stack>
            <div
              style={{
                background: "#B3B3B3",
                height: 7,
                width: "100%",
                marginBlock: 2,
              }}
            >
              <div
                style={{ background: COLORS.primary1, height: 7, width: "80%" }}
              ></div>
            </div>
            <Stack direction="row" justifyContent={"space-between"}>
              <span>0</span>
              <span>2.000.000</span>
            </Stack>
          </Stack>
        </Stack>
      </Stack>
      <Stack paddingBottom={4}>
        <Stack direction={"row"} justifyContent="space-between" marginBlock={2}>
          {contentMemberShip.map((item, i) => (
            <Button
              key={`member-${i}`}
              onClick={() => {
                setViewMemberShip(i);
              }}
              style={{
                height: 45,
                width: "100%",
                background: viewMenberShip === i ? "transparent" : "#fff",
              }}
            >
              <div
                style={{
                  background: item.color,
                  height: 30,
                  width: 30,
                  borderRadius: "50%",
                }}
              />
            </Button>
          ))}
        </Stack>
        {contentMemberShip.map((item, i) => (
          <Stack
            key={`membership-${i}`}
            gap={0}
            display={viewMenberShip === i ? "block" : "none"}
          >
            <p style={{ fontWeight: 700, padding: 0, margin: 0 }}>
              {item.title}
            </p>
            <p>{item.content}</p>
          </Stack>
        ))}
      </Stack>
    </FrameContainer>
  );
}
