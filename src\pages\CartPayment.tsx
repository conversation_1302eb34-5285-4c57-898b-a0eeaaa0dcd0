import LoginPopup from "@/components/LoginPopup";
import PopupChangeBranch from "@/components/UI/PopupChangeBranch";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import {
  estimatePointCart,
  getCart,
  setCartPayment,
  updateAddressCartPayment,
  updateCartStatusDelivery,
} from "@/redux/slices/cart/cartSlice";
import { getOrderDetail } from "@/redux/slices/order/orderSlice";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ReceiptLongIcon from "@mui/icons-material/ReceiptLong";
import RequestQuoteOutlinedIcon from "@mui/icons-material/RequestQuoteOutlined";
import SpeakerNotesOutlinedIcon from "@mui/icons-material/SpeakerNotesOutlined";
import {
  Backdrop,
  Box,
  ButtonBase,
  CircularProgress,
  Dialog,
  Divider,
  FormControlLabel,
  IconButton,
  Input,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { Stack } from "@mui/system";
import moment from "moment";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useDispatch, useSelector } from "react-redux";
import { CustomSwitch } from "../components/UI/CustomSwitch";
import NoDataView from "../components/UI/NoDataView";
import PopupChangeAddress from "../components/UI/PopupChangeAddress";
import CartInfoIcon from "../components/icon/CartInfoIcon";
import DiscountIcon from "../components/icon/DiscountIcon";
import HistoryOrderIcon from "../components/icon/HistoryOrderIcon";
import PaymentIcon from "../components/icon/PaymentIcon";
import ShipIcon from "../components/icon/ShipIcon";
import SmallCheckIcon from "../components/icon/SmallCheckIcon";
import StoreIcon from "../components/icon/StoreIcon";
import BottomVerify from "../components/products/BottomVerify";
import ChangeInfoATC from "../components/products/PopupChangeInfoATC";
import SelectVoucher from "../components/voucher/SelectVoucher";
import { Platform } from "../config";
import { Icon } from "../constants/Assets";
import { StatusDelivery, TransportService, TypePay } from "../constants/Const";
import { Router } from "../constants/Route";
import { COLORS } from "../constants/themes";
import { useCalcCartAfterLogin } from "../hooks/useCalcCartAfterLogin";
import { useCart } from "../hooks/useCart";
import { useAlert } from "../redux/slices/alert/useAlert";
import { getUser } from "../redux/slices/authen/authSlice";
import {
  DataGetListPayment,
  getPaymentMethodList,
} from "../redux/slices/paymentMethod/paymentMethodSlice";
import { setCurVoucher, VoucherDto } from "../redux/slices/voucher/voucherSlice";
import store, { AppDispatch, RootState } from "../redux/store";
import { IProduct } from "../types/product";
import { copy, formatNumber, showToast } from "../utils/common";
import { useNavigate } from "../utils/component-util";
import { formatPrice } from "../utils/formatPrice";
interface VietQrBankInfo {
  account?: string;
  bank?: string;
  orderNo?: string;
  amount?: number;
  orderId?: string;
}

export default function CartPayment() {
  const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { shopId, shopInfo } = useSelector((state: RootState) => state.appInfo);
  const { color, ...appConfig } = useConfigApp();
  const {
    cartPayment,
    clearCart,
    changeProductQuantity,
    removeProductFromCart,
    createOrder,
    changeDelivery,
    changeAddress,
    changeBranch,
    changePaymentMethod,
    setCartAndSave,
    availableDiscountPoint,
    pointAfterOrderComplete,
    toggleUsePoint,
    createOrderZalo,
  } = useCart();

  const { checkLogin, openLoginPopup, setOpenLoginPopup, onClickRegister, loading } =
    useCheckLogin();
  const { showAlert } = useAlert();

  const { user } = useSelector((state: RootState) => state.auth);
  const { currentAddress, defaultAddress } = useSelector((state: RootState) => state.address);
  const { defaultBranch } = useSelector((state: RootState) => state.branch);
  const { listPaymentMethod } = useSelector((state: RootState) => state.paymentMethod);
  const transportService = cartPayment?.transportService;
  let estimateShippingTime = `Ngày ${moment().add(3, "days").format("DD")} Th${moment()
    .add(3, "days")
    .format("MM")} - 
  Ngày ${moment().add(4, "days").format("DD")} Th${moment().add(4, "days").format("MM")}`;
  if (transportService === TransportService.AHAMOVE) {
    const startTime = moment().add(30, "minutes").format("HH:mm");
    const endTime = moment().add(2, "hours").format("HH:mm");
    estimateShippingTime = `Ngày ${moment().format("DD")} Th${moment().format(
      "MM"
    )}: Từ ${startTime} - ${endTime}`;
  }

  const { selectedVoucherItem } = useSelector((state: RootState) => state.vouchers);
  // useHandlePayment();

  const [products, setProducts] = useState<IProduct[]>(cartPayment?.listItems);
  const [paymentMethod, setPaymentMethod] = useState(cartPayment?.typePay);
  const [isEmpty, setIsEmpty] = useState(true);
  const [isLoading, setLoading] = useState(false);
  const [isCreatedOrder, setIsCreatedOrder] = useState<boolean>(true);
  // Discount value
  const [selectDiscountPoint, setDiscountPoint] = React.useState(0);
  const [realDiscountPoint, setRealDiscountPoint] = React.useState(0);
  const [checked, setChecked] = useState(false);
  const [openSelectVoucher, setOpenSelectVoucher] = useState(false);
  const [selectedVoucher, setSelectedVoucher] = useState<VoucherDto[] | null>([]);
  const [openVietQr, setOpenVietQr] = useState(false);
  const [vietQrUrl, setVietQrUrl] = useState("");
  const [vietQrBankInfo, setVietQrBankInfo] = useState<VietQrBankInfo>({});
  const data = useSelector((state: RootState) => state.cart);
  const [note, setNote] = useState(cartPayment?.notes || "");

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked && availableDiscountPoint == 0) {
      showAlert({
        title: `Điểm thưởng không hợp lệ`,
        content: `Bạn không có đủ điểm thưởng`,
        buttons: [
          {
            title: "Đóng",
          },
        ],
      });
      return;
    }

    if (event.target.checked) {
      toggleUsePoint(1);
    } else {
      toggleUsePoint(0);
    }
    setChecked(event.target.checked);
  };

  useEffect(() => {
    const checkEmpty =
      products.length > 0
        ? products?.reduce((acc, item) => {
            return acc + item.quantity;
          }, 0)
        : cartPayment.listItems?.reduce((acc, item) => {
            return acc + item.quantity;
          }, 0);

    if (checkEmpty > 0) {
      setIsEmpty(false);
    }
  }, [cartPayment.listItems]);

  // useEffect(() => {
  //   if (
  //     Array.isArray(cartPayment?.listItems) &&
  //     cartPayment?.listItems.length > 0
  //   ) {
  //     setProducts(cartPayment?.listItems);
  //   } else {
  //     setProducts([]);
  //   }
  // }, []);

  useEffect(() => {
    if (shopId) {
      const data: DataGetListPayment = {
        shopId: shopId || "",
        platform: "ZaloMiniApp",
      };
      store.dispatch(getPaymentMethodList(data));
      if (!Array.isArray(listPaymentMethod) || !listPaymentMethod.length) {
      }
    }
  }, [shopId]);

  useEffect(() => {
    setCartPayment({ ...cartPayment, branchId: defaultBranch?.branchId });
  }, []);

  useEffect(() => {
    if (cartPayment && cartPayment.userId) {
      dispatch(estimatePointCart());
      const hasExchangePoints = cartPayment.exchangePoints > 0;
      setChecked(hasExchangePoints);
    }
  }, [cartPayment]);

  useEffect(() => {
    const currentCart = { ...cartPayment };
    currentCart.transportService = TransportService.LCOD;
    if (defaultBranch) {
      currentCart.branchId = defaultBranch.branchId;
    }

    if (currentAddress) {
      currentCart.addressId = currentAddress.shippingAddressId;
    }

    if (
      listPaymentMethod &&
      (!currentCart.typePay ||
        !listPaymentMethod.some((method) => method.typePay === currentCart.typePay))
    ) {
      const defaultPaymentMethod = listPaymentMethod.find((item) => item.isActive);

      if (defaultPaymentMethod) {
        currentCart.typePay = defaultPaymentMethod.typePay;
        setPaymentMethod(defaultPaymentMethod.typePay);
      }
    }
    setCartAndSave(currentCart);
  }, [currentAddress]);

  useEffect(() => {
    let intervalId: any;
    if (openVietQr && vietQrBankInfo.orderId) {
      intervalId = setInterval(async () => {
        try {
          const orderId = String(vietQrBankInfo.orderId);
          const res = await dispatch(getOrderDetail(orderId));
          if (res?.payload?.statusPay === "Paid") {
            setOpenVietQr(false);
            if (intervalId) clearInterval(intervalId);
            navigate(`${Router.checkoutResult}?orderNo=${vietQrBankInfo.orderNo}`);
          }
        } catch (e) {}
      }, 2000);
    }
    return () => intervalId && clearInterval(intervalId);
  }, [openVietQr, vietQrBankInfo.orderId]);

  useEffect(() => {
    setNote(cartPayment?.notes || "");
  }, [cartPayment?.notes]);

  const handlerCreateOrder = () => {
    checkLogin(checkCreateOrder);
  };

  const handleClickChangeAddress = () => {
    checkLogin(() => {
      navigate(Router.profile.address, { state: { shouldGoBack: true } });
    });
  };

  const checkCreateOrder = async () => {
    if (!currentAddress && cartPayment.statusDelivery === "ExpressDelivery") return;
    setLoading(true);
    const res: any = await createOrder();

    setLoading(false);
    if (res) {
      if (res.error) {
        showToast({
          content: res.message,
          type: "error",
          duration: 3000,
        });
        return;
      }
      if (res.orderNo) {
        dispatch(getCart());
        onCreateOrderSuccess(res);
      } else {
        showToast({
          content: res.message,
          type: "error",
          duration: 3000,
        });
      }
    }
  };

  const onCreateOrderSuccess = (order) => {
    dispatch(getUser());
    // dispatch(getOrderDetail(order.orderId));
    // if (AppEnv !== "production" && Platform === "zalo") {
    if (order.typePay == TypePay.TRANSFER) {
      if (Platform === "zalo") {
        createOrderZalo(order);
        return;
      } else {
        navigate(Router.bankTransferV2.index, { state: { order } });
        return;
      }
    }
    navigate(`${Router.checkoutResult}?orderNo=${order.orderNo}`);
    // }
  };

  const calcBonusPoint = (finalPrice) => {
    const cashbackPercent = user && user.cashbackPercent ? user.cashbackPercent : 3;
    return formatNumber((Number(finalPrice) * cashbackPercent) / 100);
  };

  const handleChangeDelivery = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const newStatus =
      e.target.value === StatusDelivery.InHome ? StatusDelivery.InHome : StatusDelivery.InShop;
    dispatch(updateCartStatusDelivery(newStatus));
    if (e.target.value === StatusDelivery.InHome && currentAddress) {
      dispatch(updateAddressCartPayment(currentAddress?.shippingAddressId));
      await setCartAndSave({
        ...cartPayment,
        statusDelivery: newStatus,
        // Cập nhật voucherCodes gồm cả voucherPromotion và voucherTransport
        voucherCodes: [
          ...(cartPayment.voucherPromotion?.map((v: any) => v.code) || []),
          ...(cartPayment.voucherTransport?.map((v: any) => v.code) || []),
        ],
      });
      setSelectedVoucher([...cartPayment.voucherPromotion, ...cartPayment.voucherTransport]);
    } else {
      dispatch(updateAddressCartPayment(""));
      dispatch(setCartPayment({ ...cartPayment, voucherTransport: [] }));
      setSelectedVoucher([...cartPayment.voucherPromotion]);
      await setCartAndSave({
        ...cartPayment,
        voucherTransport: [],
        voucherTransportPrice: 0,
        statusDelivery: newStatus,
        // Cập nhật voucherCodes chỉ còn voucherPromotion
        voucherCodes: [...(cartPayment.voucherPromotion?.map((v: any) => v.code) || [])],
      });
    }
  };

  const handleUpdateQuantity = async (product, quantity, note) => {
    try {
      const response: any = await changeProductQuantity(product, quantity, note);
      if (response === true) {
        setIsCreatedOrder(true);
      }
      if (response?.status === 400) {
        toast.error(response?.detail);
        const listItems = cartPayment?.listItems?.map((item) => {
          return { ...item, quantity, note };
        });
        await setCartAndSave({
          ...cartPayment,
          listItems,
          voucherPromotion: [],
          voucherTransport: [],
          voucherPromotionPrice: 0,
          voucherTransportPrice: 0,
        });
        await dispatch(setCurVoucher(null));
        setSelectedVoucher([]);
        await dispatch(getCart());
      }
    } catch (error: any) {}
  };

  const fetchUserAndCart = useCalcCartAfterLogin();

  useEffect(() => {
    if (
      user?.userId &&
      cartPayment &&
      cartPayment.userId !== user.userId &&
      cartPayment.listItems &&
      cartPayment.listItems.length > 0
    ) {
      fetchUserAndCart();
    }
  }, [user?.userId]);

  const { container } = useConfigApp();
  const containerStyleItem = container as any;
  const containerBgColor = containerStyleItem?.backgroundColor || "";
  const containerBgImage = containerStyleItem?.backgroundImage || "";

  const containerStyle = {
    paddingInline: 0,
    backgroundColor: containerBgImage ? undefined : containerBgColor,
    backgroundImage: containerBgImage ? `url(${containerBgImage})` : undefined,
    backgroundSize: containerBgImage ? "cover" : undefined,
    backgroundRepeat: containerBgImage ? "no-repeat" : undefined,
    backgroundPosition: containerBgImage ? "center" : undefined,
  };

  const cartTitle =
    Array.isArray(container?.navbar) && container.navbar[3]?.text
      ? container.navbar[3].text
      : "Thanh toán";

  const effectiveLoading = user ? isLoading || loading : false;
  return (
    <>
      <Box sx={{ marginTop: -1 }}>
        <LoginPopup
          open={openLoginPopup}
          onClose={() => setOpenLoginPopup(false)}
          onClickRegister={onClickRegister}
          loading={loading}
        />
        <FrameContainerFull
          title={cartTitle}
          overrideStyle={{ paddingBlock: 10, ...containerStyle }}
        >
          <Stack sx={styles.sectionContainer}>
            <Stack direction="row" sx={styles.headerSection} paddingBottom={2}>
              <HistoryOrderIcon fillColor={color.primary} />
              Sản phẩm đặt mua
            </Stack>
            <Divider />
            <Stack gap={2} paddingTop={2}>
              {cartPayment?.listItems?.length > 0 ? (
                cartPayment?.listItems?.map((item, index) => (
                  <Stack key={index} direction="row" gap={2} alignItems={"center"}>
                    <Stack gap={1} flex={1} maxWidth="100%" overflow="hidden">
                      {/* <span>{item.name}</span> */}
                      <ChangeInfoATC
                        item={item}
                        onChangeQuantity={async (item, quantity, note) => {
                          try {
                            if (quantity === 0) {
                              const res = await removeProductFromCart(item);
                              setSelectedVoucher([]);
                              if (res.status === 400) {
                                setSelectedVoucher([]);
                              }
                            } else await handleUpdateQuantity(item, quantity, note);
                          } catch (error: any) {
                            console.log({ error });
                          }
                        }}
                      />
                    </Stack>
                    <IconButton
                      style={{ padding: 0 }}
                      onClick={async () => {
                        try {
                          const res = await removeProductFromCart(item);
                          if ((cartPayment?.listItems?.length || 1) <= 1) {
                            const clearedCart = {
                              ...cartPayment,
                              listItems: [],
                              voucherPromotion: [],
                              voucherTransport: [],
                              voucherPromotionPrice: 0,
                              voucherTransportPrice: 0,
                              voucherCodes: [],
                            };
                            dispatch(setCartPayment(clearedCart));
                            await setCartAndSave(clearedCart);
                            setSelectedVoucher([]);
                            await dispatch(setCurVoucher(null));
                          } else {
                            setSelectedVoucher([]);
                          }
                          if (res.status === 400) {
                            setSelectedVoucher([]);
                          }
                          showToast({
                            content: "Xóa thành công sản phẩm",
                            type: "success",
                          });
                        } catch (error: any) {}
                      }}
                    >
                      <img src={Icon.accent_off} alt="" />
                    </IconButton>
                  </Stack>
                ))
              ) : (
                <NoDataView content=" Giỏ hàng trống" />
              )}
            </Stack>
          </Stack>
          {shopInfo?.enableInShop && (
            <Stack sx={styles.sectionContainer}>
              <ButtonBase
                style={{ width: "100%", textAlign: "left", borderRadius: 5 }}
                onClick={() =>
                  handleChangeDelivery({ target: { value: StatusDelivery.InShop } } as any)
                }
                disableRipple
              >
                <Stack
                  direction="row"
                  justifyContent={"space-between"}
                  alignItems={"start"}
                  paddingBottom={cartPayment.statusDelivery === StatusDelivery.InShop ? 2 : 0}
                  width="100%"
                >
                  <Stack direction="row" sx={styles.headerSection}>
                    <StoreIcon fillColor={color.primary} />
                    <Typography sx={styles.headerSection}>Nhận tại cửa hàng</Typography>
                  </Stack>
                  <FormControlLabel
                    style={{ margin: 0 }}
                    control={
                      <Radio
                        style={{ padding: 0 }}
                        name="deliveryOption"
                        value={StatusDelivery.InShop}
                        checked={cartPayment.statusDelivery === StatusDelivery.InShop}
                        // onChange={(e) => handleChangeDelivery(e)}
                        checkedIcon={<SmallCheckIcon fillColor={color.primary} />}
                        icon={<img src={Icon.tick_circle} alt="" />}
                      />
                    }
                    labelPlacement="bottom"
                    label=""
                  />
                </Stack>
              </ButtonBase>
              {cartPayment.statusDelivery === StatusDelivery.InShop && (
                <>
                  <PopupChangeBranch />
                  <Divider />
                  {/* <Stack
                          direction="row"
                          justifyContent={"space-between"}
                          paddingTop={1}
                        >
                          <Stack direction="row" gap={2}>
                            <img src={Icon.map} />
                            <Stack>
                              <Typography style={styles.contentTitle}>
                                Địa chỉ cửa hàng
                              </Typography>
                              <Typography style={styles.contentText}>
                                Cập nhật địa chỉ cửa hàng
                              </Typography>
                            </Stack>
                          </Stack>
                          <IconButton>
                            <img src={Icon.arrow_right} alt="" />
                          </IconButton>
                        </Stack> */}
                </>
              )}
            </Stack>
          )}

          {shopInfo?.enableExpressDelivery && (
            <Stack sx={styles.sectionContainer}>
              <ButtonBase
                style={{ width: "100%", textAlign: "left", borderRadius: 5 }}
                onClick={() =>
                  handleChangeDelivery({ target: { value: StatusDelivery.InHome } } as any)
                }
                disableRipple
              >
                <Stack
                  direction="row"
                  justifyContent={"space-between"}
                  alignItems={"start"}
                  paddingBottom={cartPayment.statusDelivery === StatusDelivery.InHome ? 2 : 0}
                  width="100%"
                >
                  <Stack direction="row" sx={styles.headerSection}>
                    <ShipIcon fillColor={color.primary} />
                    Nhận hàng tại nhà
                  </Stack>
                  <FormControlLabel
                    style={{ margin: 0 }}
                    control={
                      <Radio
                        style={{ padding: 0 }}
                        name="deliveryOption"
                        value={StatusDelivery.InHome}
                        checked={cartPayment.statusDelivery === StatusDelivery.InHome}
                        // onChange={(e) => handleChangeDelivery(e)}
                        checkedIcon={<SmallCheckIcon fillColor={color.primary} />}
                        icon={<img src={Icon.tick_circle} alt="" />}
                      />
                    }
                    labelPlacement="bottom"
                    label=""
                  />
                </Stack>
              </ButtonBase>
              {cartPayment.statusDelivery === StatusDelivery.InHome && (
                <>
                  <Divider />
                  <Stack gap={1}>
                    <PopupChangeAddress handleClickChangeAddress={handleClickChangeAddress} />
                    <Divider />
                    <Stack direction="row" justifyContent={"space-between"} gap={2}>
                      <Stack direction="row" gap={2}>
                        <img src={Icon.lock} />
                        <Stack>
                          <Typography style={styles.contentTitle}>
                            {estimateShippingTime}
                          </Typography>
                          <Typography style={styles.contentText}>
                            Thời gian nhận hàng dự kiến
                          </Typography>
                        </Stack>
                      </Stack>
                      <IconButton>
                        <img src={Icon.arrow_right} alt="" />
                      </IconButton>
                    </Stack>
                  </Stack>
                </>
              )}
            </Stack>
          )}

          <Stack sx={styles.sectionContainer}>
            <Stack direction="row" sx={styles.headerSection} paddingBottom={2}>
              <DiscountIcon fillColor={color.primary} />
              Thông tin khuyến mại
            </Stack>
            <Divider />
            <SelectVoucher
              onApplyVoucher={(curVoucher) => dispatch(setCurVoucher(curVoucher))}
              initVoucher={selectedVoucherItem}
              open={openSelectVoucher}
              setOpen={setOpenSelectVoucher}
              isCreatedOrder={isCreatedOrder}
              setIsCreatedOrder={setIsCreatedOrder}
              selectedVoucher={selectedVoucher}
              setSelectedVoucher={setSelectedVoucher}
              // cartPayment={cartPayment}
            />
            {/* <Divider /> */}
            <Stack
              direction="row"
              justifyContent={"space-between"}
              alignItems={"center"}
              gap={2}
              paddingTop={1}
            >
              <Stack direction="row" gap={2}>
                <img src={Icon.pig} />
                <Stack>
                  <Typography style={styles.contentTitle}>Dùng điểm thưởng</Typography>
                  <Typography style={styles.contentText}>
                    Bạn đang có {availableDiscountPoint || 0} điểm khả dụng
                  </Typography>
                </Stack>
              </Stack>
              <CustomSwitch
                checked={checked}
                onChange={handleChange}
                inputProps={{ "aria-label": "custom switch" }}
                sx={{ "--activeColor": color.primary }}
              />
            </Stack>
            {/* <Box sx={{ paddingTop: 2 }}>
                        <SelectDiscountPoint
                          selectedDiscountPoint={setDiscountPoint}
                          initPoint={realDiscountPoint}
                        />
                      </Box> */}
          </Stack>
          <Stack
            sx={{
              ...styles.sectionContainer,
              cursor: "pointer",
            }}
          >
            <Stack direction="row" sx={styles.headerSection} alignItems="center" paddingBottom={2}>
              <SpeakerNotesOutlinedIcon style={{ color: color.primary }} />
              <Box ml={1}>Lời nhắn</Box>
            </Stack>
            <Divider />
            <Stack direction="row" gap={2} mt={1}>
              <img src={Icon.note} />
              <Stack width={"100%"}>
                <Input
                  style={styles.inputStyle}
                  placeholder="Nhập ghi chú..."
                  disableUnderline
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  onBlur={() => setCartAndSave({ ...cartPayment, notes: note })}
                />
              </Stack>
            </Stack>
          </Stack>
          <Stack
            sx={{
              ...styles.sectionContainer,
              cursor: "pointer",
            }}
            onClick={() => navigate("/invoice-request")}
          >
            <Stack direction="row" sx={styles.headerSection} alignItems="center" paddingBottom={2}>
              <ReceiptLongIcon style={{ color: color.primary }} />
              <Box ml={1}>Xuất hoá đơn GTGT</Box>
              <Box flex={1} />
              <ChevronRightIcon sx={{ fontSize: 28, color: color.primary }} />
            </Stack>
            <Divider />
          </Stack>
          <Stack sx={styles.sectionContainer}>
            <Stack direction="row" sx={styles.headerSection} paddingBottom={2}>
              <PaymentIcon fillColor={color.primary} />
              <b>Phương thức thanh toán</b>
            </Stack>
            <Divider />
            {listPaymentMethod && (
              <RadioGroup row aria-labelledby="demo-form-control-label-placement" name="position">
                {listPaymentMethod.map((item, index) => (
                  <React.Fragment key={index}>
                    <Stack
                      direction="row"
                      justifyContent={"space-between"}
                      alignItems={"center"}
                      width={"100%"}
                      onClick={() => {
                        setCartAndSave({
                          ...cartPayment,
                          typePay: item.typePay,
                        });
                        setPaymentMethod(item.typePay);
                      }}
                      style={{ cursor: "pointer" }}
                    >
                      <Stack direction="row" gap={2}>
                        <img src={Icon.money} />
                        <Typography style={styles.contentTitle}>{item.name}</Typography>
                      </Stack>
                      <FormControlLabel
                        style={{ margin: 0 }}
                        value={item.typePay}
                        control={
                          <Radio
                            checkedIcon={<SmallCheckIcon fillColor={color.primary} />}
                            icon={<img src={Icon.tick_circle} alt="" />}
                          />
                        }
                        checked={item.typePay == paymentMethod}
                        labelPlacement="bottom"
                        label=""
                      />
                    </Stack>
                    {index < listPaymentMethod.length - 1 && <Divider style={{ width: "100%" }} />}
                  </React.Fragment>
                ))}
              </RadioGroup>
            )}
          </Stack>
          <Stack sx={styles.sectionContainer}>
            <Stack direction="row" sx={styles.headerSection} paddingBottom={2}>
              <CartInfoIcon fillColor={color.primary} />
              <b>Chi tiết thanh toán</b>
            </Stack>
            <Divider />
            <Stack>
              <Stack direction="row" sx={styles.contentContainer} paddingBlock={1}>
                <img src={Icon.money} />
                <Stack>
                  <Typography style={styles.contentTitle}>
                    {formatPrice(cartPayment?.originPrice)}
                  </Typography>
                  <Typography style={styles.contentText}>Thành tiền chưa thuế</Typography>
                </Stack>
              </Stack>
              <Divider />
              <Stack direction="row" sx={styles.contentContainer} paddingBlock={1}>
                <img src={Icon.ship2} />
                <Stack>
                  <Typography style={styles.contentTitle}>
                    {formatPrice(cartPayment?.transportPrice)}
                  </Typography>
                  <Typography style={styles.contentText}>Phí vận chuyển</Typography>
                </Stack>
              </Stack>
              <Divider />
              <Stack direction="row" sx={styles.contentContainer} paddingBlock={1}>
                <img src={Icon.voucher2} />
                <Stack>
                  <Typography style={styles.contentTitle}>
                    Giảm{" "}
                    {formatPrice(
                      cartPayment?.voucherPromotionPrice + cartPayment?.voucherTransportPrice
                    )}
                  </Typography>
                  <Typography style={styles.contentText}>Tổng cộng mã giảm giá</Typography>
                </Stack>
              </Stack>
              <Divider />
              <Stack direction="row" sx={styles.contentContainer} paddingBlock={1}>
                <img src={Icon.pig} />
                <Stack>
                  <Typography style={styles.contentTitle}>
                    Giảm {formatPrice(cartPayment.pointPrice)}
                  </Typography>
                  <Typography style={styles.contentText}>
                    Trừ {cartPayment.exchangePoints} điểm thưởng tích luỹ
                  </Typography>
                </Stack>
              </Stack>
              <Divider />
              <Stack direction="row" sx={styles.contentContainer} paddingBlock={1}>
                <img src={Icon.medal_star} />
                <Stack>
                  <Typography style={styles.contentTitle}>
                    {pointAfterOrderComplete || 0} điểm thưởng
                  </Typography>
                  <Typography style={styles.contentText}>
                    Điểm thưởng khi đơn hàng thành công
                  </Typography>
                </Stack>
              </Stack>
              <Divider />
              <Stack direction="row" sx={styles.contentContainer} paddingBlock={1}>
                <RequestQuoteOutlinedIcon
                  style={{ width: 25, height: 28, color: "#c4c4c4", alignSelf: "center" }}
                />
                <Stack>
                  <Typography style={styles.contentTitle}>
                    {formatPrice(cartPayment?.totalTaxAmount || 0)}
                  </Typography>
                  <Typography style={styles.contentText}>Thuế GTGT</Typography>
                </Stack>
              </Stack>
            </Stack>
          </Stack>

          {/* <Stack sx={styles.sectionContainer}>
                  <Stack direction="row" sx={styles.headerSection}>
                    <img src={Icon.payment} />
                    <b>Phương thức thanh toán</b>
                  </Stack>
                  <Stack gap={2}>
                    <Stack sx={styles.paymentMethods}>
                      <img src={Icon.vnpay} />
                      <img src={Icon.visa} />
                      <img src={Icon.bank} />
                    </Stack>
                    <Stack>
                      <Typography
                        style={{
                          fontSize: 13,
                          fontWeight: 400,
                          textAlign: "center",
                          marginBottom: 5,
                        }}
                      >
                        Hỗ trợ đầy đủ các phương thức thanh toán bao gồm:
                      </Typography>
                      <Typography
                        style={{
                          fontSize: 13,
                          fontWeight: 700,
                          textAlign: "center",
                          marginBottom: 5,
                          color: COLORS.primary,
                        }}
                      >
                        VNPay, Visa, chuyển khoản, thanh toán khi nhận hàng
                      </Typography>
                      <Typography
                        style={{
                          fontSize: 11,
                          lineHeight: "16px",
                          fontWeight: 400,
                        }}
                      >
                        (*) Lựa chọn phương thức thanh toán sau khi đặt hàng
                      </Typography>
                    </Stack>
                  </Stack>
                </Stack> */}

          {/* {(user && !isReviewer) ||
                  (Platform === "zalo" && (
                    <Stack sx={styles.sectionContainer}>
                      <Stack direction="row" sx={styles.headerSection}>
                        <img src={Icon.payment} />
                        <b>Phương thức thanh toán</b>
                      </Stack>
                      <FormControl>
                        <Stack gap={2}>
                          <RadioGroup
                            aria-labelledby="demo-form-control-label-placement"
                            name="position"
                            defaultValue={DefaultPaymentMethod}
                            onChange={(e) => setPaymentMethod(Number(e.target.value))}
                          >
                            <Stack gap={2}>
                              {newPaymentMethods.map((item, index) => (
                                <Stack
                                  key={index}
                                  direction="row"
                                  style={styles.paymentItemContainer}
                                >
                                  <img src={item.iconPath} />
                                  <FormControlLabel
                                    sx={styles.paymentItemLabel}
                                    disabled={!item.active}
                                    value={item.value}
                                    control={<Radio />}
                                    label={item.label}
                                    labelPlacement="start"
                                  />
                                </Stack>
                              ))}
                            </Stack>
                          </RadioGroup>
                        </Stack>
                      </FormControl>
                    </Stack>
                  ))} */}

          <Typography pb={6} style={styles.policyText}>
            Bằng việc tiến hành đặt hàng, bạn đồng ý với{" "}
            <Typography
              component={"span"}
              color={`${appConfig.textColor.primary}`}
              sx={{ cursor: "pointer", textDecoration: "underline" }}
              onClick={() => {
                navigate(`/profile/policy`);
              }}
            >
              điều kiện và điều khoản
            </Typography>{" "}
            sử dụng của
            <span
              style={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                display: "inline-block",
                verticalAlign: "bottom",
                width: "100%",
              }}
              title={appConfig?.shopName}
            >
              {appConfig?.shopName}
            </span>
          </Typography>
        </FrameContainerFull>
        {Array.isArray(cartPayment?.listItems) && cartPayment?.listItems.length > 0 && (
          <BottomVerify
            cart={cartPayment}
            currentAddress={currentAddress}
            handlerCreateOrder={handlerCreateOrder}
            isCreatedOrder={isCreatedOrder}
          />
        )}
        <Backdrop
          sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
          open={effectiveLoading}
        >
          <CircularProgress color="inherit" />
        </Backdrop>

        <Dialog open={openVietQr} onClose={() => setOpenVietQr(false)} maxWidth="xs">
          <Stack alignItems="center" p={2}>
            <Typography fontWeight={700} fontSize={18} mb={2}>
              Thanh toán chuyển khoản
            </Typography>
            <Typography fontSize={14} mb={2} align="center">
              Quét mã QR hoặc chuyển khoản theo thông tin bên dưới để hoàn tất thanh toán đơn hàng
            </Typography>
            <img
              src={vietQrUrl}
              alt="QR VietQR"
              style={{ marginBottom: 12, width: 250, height: 250 }}
            />
            {Platform === "web" && (
              <ButtonBase
                component="a"
                href={vietQrUrl}
                download={`vietqr_${vietQrBankInfo.orderNo || ""}.jpg`}
                style={{
                  marginBottom: 12,
                  background: "#f5f5f5",
                  borderRadius: 6,
                  padding: "6px 18px",
                  fontWeight: 600,
                }}
              >
                Lưu ảnh QR
              </ButtonBase>
            )}
            <Stack spacing={1} mb={2}>
              <Typography fontSize={14}>
                <b>Ngân hàng:</b> {vietQrBankInfo.bank}
              </Typography>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Typography fontSize={14}>
                  <b>Số tài khoản:</b> {vietQrBankInfo.account}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => copy(vietQrBankInfo.account || "", "số tài khoản")}
                >
                  <img src={Icon.copy} alt="Copy" width={18} height={18} />
                </IconButton>
              </Stack>
              <Typography fontSize={14}>
                <b>Số tiền:</b> {formatPrice(vietQrBankInfo.amount)}
              </Typography>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Typography fontSize={14}>
                  <b>Nội dung:</b> {vietQrBankInfo.orderNo}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => copy(vietQrBankInfo.orderNo || "", "nội dung chuyển khoản")}
                >
                  <img src={Icon.copy} alt="Copy" width={18} height={18} />
                </IconButton>
              </Stack>
            </Stack>
          </Stack>
        </Dialog>
      </Box>

      {/* )} */}
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  sectionContainer: {
    // borderRadius: "5px",
    background: "#fff",
    padding: 2,
    marginBlock: 1,
  },
  headerSection: {
    color: "#1D1D1D",
    fontSize: 17,
    fontWeight: 700,
    alignItems: "center",
    gap: 2,
  },
  contentTitle: {
    fontWeight: 500,
    fontSize: 15,
    color: "#828282",
  },
  contentTitleDisabled: {
    fontWeight: 700,
    color: COLORS.neutral4,
  },
  contentText: {
    color: "#C5C5C5",
    fontWeight: 400,
    fontSize: 12,
    paddingTop: 1,
  },
  contentContainer: {
    justifyContent: "flex-start",
    gap: 2,
  },
  paymentItemContainer: {
    width: "100%",
    justifyContent: "flex-start",
    border: "1px solid #D9D9D9",
    padding: 8,
  },
  paymentItemLabel: {
    flex: 1,
    display: "flex",
    justifyContent: "space-between",
  },
  paymentMethods: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    gap: 2,
  },
  policyText: {
    color: "rgb(130, 130, 130)",
    textAlign: "center",
    fontWeight: 11,
    paddingInline: 10,
  },
  inputStyle: {
    fontSize: 15,
    fontWeight: 400,
    color: "#828282",
  },
};
