import { Box, Container, Grid, Stack } from "@mui/material";
import React, { ReactNode, useState } from "react";
import { Header } from "zmp-ui";
import styles from "../../css/styles.module.css";

export default function FrameContainerWhite({
  children,
  title,
  overrideStyle,
}: {
  children: ReactNode;
  title: string;
  overrideStyle: React.CSSProperties;
}) {
  const [positionCss, setPositionCss] = useState({});
  const handleScroll = (e) => {
    const bottom =
      e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
    if (bottom) {
      // setPositionCss({
      //     position: 'fixed',
      // })
    }
  };

  return (
    <div style={{ backgroundColor: "#FFF", ...overrideStyle }}>
      <Header
        backgroundColor="#2677E8"
        textColor="#fff"
        style={positionCss}
        title={title}
      />
      <div className={styles.pageContent}>
        <Container
          onScroll={handleScroll}
          style={{ paddingBottom: 64, backgroundColor: "#FFF" }}
        >
          <Stack paddingBlock={2}>{children}</Stack>
        </Container>
      </div>
    </div>
  );
}
