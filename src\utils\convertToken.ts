import { useRecoilValue } from "recoil";
import { accessTokenSuccess, phoneSuccessDataState } from "../state";
import axios from "axios";
import { ZMP_API_INFO, ZMP_SECRET_KEY } from "../constants";

export const getConvertToken = async () => {
  const userAccessToken = useRecoilValue(phoneSuccessDataState);
  const token = useRecoilValue(accessTokenSuccess);
  const secretKey = ZMP_SECRET_KEY;
  const apiUrl = ZMP_API_INFO;

  const headers = {
    access_token: userAccessToken,
    token: token,
    secret_key: secretKey,
  };

  try {
    const res = await axios.post(apiUrl, {}, { headers });
    return res.data;
  } catch (err) {
    return null;
  }
};
