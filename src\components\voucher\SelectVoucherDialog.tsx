import {
  But<PERSON>,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  FormControl,
  IconButton,
  RadioGroup,
  Slide,
  Stack,
  InputAdornment,
  TextField,
  Container,
  Typography,
  Box,
  CircularProgress,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import React, { useEffect, useMemo } from "react";
import { useState } from "react";
import { Icon } from "../../constants/Assets";
import { TransitionProps } from "@mui/material/transitions";
import ClearIcon from "@mui/icons-material/Clear";
import { AppDispatch, RootState } from "../../redux/store";
import { useDispatch, useSelector } from "react-redux";
import {
  getMyVoucherList,
  getVoucherList,
  redeemPointToVoucher,
  VoucherDto,
} from "../../redux/slices/voucher/voucherSlice";
import { COLORS, commonStyle } from "../../constants/themes";
import VoucherItem from "./VourcherItem";
import { useDebounce } from "use-debounce";
import { removeMark, showToast } from "../../utils/common";
import { useAlert } from "../../redux/slices/alert/useAlert";
import { ERROR_MESSAGE } from "../../constants/Const";
import NoDataView from "../UI/NoDataView";
import { getUser } from "../../redux/slices/authen/authSlice";
import CheckIcon from "../icon/CheckIcon";
import WarningIcon from "../icon/WarningIcon";
import { useConfigApp } from "@/hooks/useConfigApp";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function SelectVoucherDialog() {
  const appConfig = useConfigApp();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const [showClearIcon, setShowClearIcon] = useState("none");
  const [open, setOpen] = useState(false);
  const [voucherSelected, setVoucherSelected] = useState<VoucherDto | null>();
  const [searchKey, setSearchKey] = useState("");
  const [debounceValue] = useDebounce(searchKey, 1000);
  const { showAlert } = useAlert();

  const { voucherList, isLoading, myVoucherList } = useSelector(
    (state: RootState) => state.vouchers
  );

  async function getVoucher() {
    if (!user) return;
    await dispatch(getVoucherList());
    // await dispatch(getMyVoucherList());
  }

  // Handle open voucher list
  const handleClickOpen = async () => {
    await getVoucher();
    setTimeout(() => {
      setOpen(true);
    }, 500);
  };
  const handleClose = () => {
    setOpen(false);
    setVoucherSelected(null);
    setSearchKey("");
  };

  // Handle select voucher
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    setSearchKey(event.target.value);
  };

  // Handle search voucher
  const handleClick = (): void => {
    setSearchKey("");
  };

  // handle apply voucher
  const handleSelectVoucher = (item: VoucherDto) => {
    setVoucherSelected(item);
  };

  const onChangePointToVoucher = async (voucherID) => {
    const res = await dispatch(
      redeemPointToVoucher({
        voucherId: voucherID,
      })
    ).unwrap();
    if (res.data) {
      if (res.message) {
        showAlert({
          title: ERROR_MESSAGE[res.message],
          content: "Xem lại tại kho voucher",
          icon: (
            <WarningIcon
              primaryColor={appConfig.color.primary}
              secondaryColor={appConfig.bgColor.primary}
            />
          ),
        });
      } else {
        dispatch(getUser());
        // dispatch(getMyVoucherList());
        showAlert({
          title: voucherSelected?.rewardPoint
            ? `Bạn đã đổi ${voucherSelected?.rewardPoint} điểm lấy voucher thành công`
            : "Bạn đã lấy voucher thành công",
          content: "Xem lại tại kho voucher của mình",
        });
      }
    } else {
      showToast({
        content: ERROR_MESSAGE[res.message] || "Đổi voucher không thành công, vui lòng thử lại sau",
        type: "error",
      });
    }
    setVoucherSelected(null);
  };

  const onApplyClick = (voucherSelect) => {
    showAlert({
      title: voucherSelect?.matchPoint ? "Đổi điểm lấy Voucher" : "Lấy Voucher",
      content: voucherSelect?.matchPoint
        ? `Bạn có muốn dùng ${voucherSelect?.matchPoint} điểm để đổi voucher này không?`
        : "Bạn có chắc chắn muốn lấy voucher này không?",
      icon: (
        <CheckIcon
          primaryColor={appConfig.color.primary}
          secondaryColor={appConfig.bgColor.primary}
        />
      ),
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "Xác nhận",
          action: () => {
            onChangePointToVoucher(voucherSelect.id);
          },
        },
      ],
    });
  };

  const filteredVoucherList = useMemo(() => {
    return debounceValue && voucherList.length
      ? voucherList?.filter((voucher: VoucherDto) => {
          const removeMarkVoucherCode = removeMark(
            voucher.voucherDetails?.[0]?.voucherCode || ""
          ).toLowerCase();
          const removeMarkSearchKey = removeMark(debounceValue).toLowerCase();
          return removeMarkVoucherCode.includes(removeMarkSearchKey);
        })
      : voucherList || [];
  }, [debounceValue, voucherList]);

  return (
    <Box>
      <Button
        style={{
          ...styles.discountBtn,
          color: appConfig.color.primary,
          borderColor: appConfig.color.primary,
        }}
        variant="outlined"
        onClick={handleClickOpen}
      >
        Mã giảm giá
      </Button>
      <Dialog
        className="popup-voucher"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
        scroll={"paper"}
      >
        <DialogActions sx={{ justifyContent: "center" }}>
          <Container>
            <Stack width={"100%"}>
              <Stack direction="row" sx={styles.topContainer}>
                {/* <img src="/images/voucher.png" /> */}
                <Stack>
                  <Typography
                    style={{
                      ...commonStyle.headline16,
                      color: appConfig.color.primary,
                    }}
                  >
                    {appConfig?.shopName}
                  </Typography>
                  <Typography style={{ fontSize: 16 }}>(Kho Ưu đãi khách hàng)</Typography>
                </Stack>
              </Stack>
              <Stack direction={"row"} sx={styles.inputContainer}>
                <FormControl style={{ flexGrow: 1 }}>
                  <TextField
                    placeholder="Nhập mã ưu đãi"
                    style={{ width: "100%" }}
                    size="small"
                    variant="outlined"
                    onChange={handleChange}
                    value={searchKey}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">{<SearchIcon />}</InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment
                          position="end"
                          style={{ display: showClearIcon }}
                          onClick={handleClick}
                        >
                          <ClearIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </FormControl>
              </Stack>
            </Stack>
          </Container>
        </DialogActions>
        <DialogContent style={{ width: "100%" }}>
          {isLoading ? (
            <Stack sx={styles.loadingContainer}>
              <CircularProgress />
            </Stack>
          ) : (
            <Stack className="voucher-profile" width={"100%"}>
              <RadioGroup
                row
                aria-labelledby="demo-form-control-label-placement"
                name="position"
                defaultValue="top"
              >
                {filteredVoucherList?.length > 0 ? (
                  filteredVoucherList?.map((item: VoucherDto) => (
                    <Box sx={{ paddingBottom: 2, width: "100%" }} key={item.voucherId}>
                      <VoucherItem
                        item={item}
                        onSelectVoucher={() => handleSelectVoucher(item)}
                        isChecked={item.id === voucherSelected?.id}
                        myVoucherList={myVoucherList}
                        onApplyClick={() => {
                          onApplyClick(item);
                        }}
                      />
                    </Box>
                  ))
                ) : (
                  <NoDataView content="Không có voucher" />
                )}
              </RadioGroup>
            </Stack>
          )}
        </DialogContent>
        <DialogActions sx={styles.bottomBtnContainer}>
          <Button
            style={{
              ...styles.bottomBtn,
              background: COLORS.accent4,
              color: appConfig.color.primary,
            }}
            onClick={handleClose}
          >
            Đóng
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentTitle: {
    fontWeight: 700,
    color: COLORS.neutral2,
  },
  contentText: {
    color: COLORS.neutral5,
    paddingTop: 4,
  },
  topContainer: {
    justifyContent: "center",
    alignItems: "center",
    marginBlock: 1,
    gap: 2,
    width: "100%",
  },
  inputContainer: {
    gap: 2,
    justifyContent: "space-between",
    marginBlock: 2,
    width: "100%",
  },
  applyBtn: {
    color: "white",
    whiteSpace: "nowrap",
    background: COLORS.accent1,
    paddingInline: 16,
  },
  bottomBtnContainer: {
    justifyContent: "space-around",
    gap: 2,
    marginBlock: 2,
  },
  bottomBtn: {
    margin: 0,
    height: "100%",
    paddingBlock: 10,
    width: 160,
    borderRadius: 99,
    display: "flex",
    gap: "8px",
    background: COLORS.accent1,
    color: "#fff",
    fontSize: 12,
  },
  discountBtn: {
    background: "transparent",
    border: `1px solid ${COLORS.primary}`,
    color: COLORS.primary,
    borderRadius: "5px",
    fontSize: 12,
    fontWeight: 700,
    padding: 1,
    marginTop: 4,
    marginBottom: 12,
    width: 150,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 4,
    width: "100%",
  },
};
