import React from "react";
import { COLORS } from "./themes";
import { useConfigApp } from "@/hooks/useConfigApp";

export const MoneySVG = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 20 20">
    <path
      fill={COLORS.black}
      d="M10.75 8.75c-.5-.125-1-.375-1.375-.75-.375-.125-.5-.5-.5-.75S9 6.625 9.25 6.5c.375-.25.75-.5 1.125-.375.75 0 1.375.375 1.75.875l1.125-1.5c-.375-.375-.75-.625-1.125-.875s-.875-.375-1.375-.375V2.5h-1.5v1.75c-.625.125-1.25.5-1.75 1-.5.625-.875 1.375-.75 2.125 0 .75.25 1.5.75 2 .625.625 1.5 1 2.25 1.375.375.125.875.375 1.25.625.25.25.375.625.375 1s-.125.75-.375 1.125c-.375.375-.875.5-1.25.5-.5 0-1.125-.125-1.5-.5a3.804 3.804 0 0 1-1-1L6 13.875c.375.5.75.875 1.25 1.25.625.375 1.375.75 2.125.75V17.5h1.375v-1.875c.75-.125 1.375-.5 1.875-1 .625-.625 1-1.625 1-2.5 0-.75-.25-1.625-.875-2.125-.625-.625-1.25-1-2-1.25ZM10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0Zm0 18.625c-4.75 0-8.625-3.875-8.625-8.625S5.25 1.375 10 1.375 18.625 5.25 18.625 10 14.75 18.625 10 18.625Z"
    />
  </svg>
);
export const Address = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
      <path
        d="M15.625 2.08337V12.5C15.625 13.6459 14.6875 14.5834 13.5417 14.5834H2.08337V6.25004C2.08337 3.94796 3.94796 2.08337 6.25004 2.08337H15.625Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.9167 14.5834V17.7084C22.9167 19.4375 21.5209 20.8334 19.7917 20.8334H18.75C18.75 19.6875 17.8125 18.75 16.6667 18.75C15.5209 18.75 14.5834 19.6875 14.5834 20.8334H10.4167C10.4167 19.6875 9.47921 18.75 8.33337 18.75C7.18754 18.75 6.25004 19.6875 6.25004 20.8334H5.20837C3.47921 20.8334 2.08337 19.4375 2.08337 17.7084V14.5834H13.5417C14.6875 14.5834 15.625 13.6459 15.625 12.5V5.20837H17.5417C18.2917 5.20837 18.9792 5.61463 19.3542 6.26047L21.1355 9.37504H19.7917C19.2188 9.37504 18.75 9.84379 18.75 10.4167V13.5417C18.75 14.1146 19.2188 14.5834 19.7917 14.5834H22.9167Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.33333 22.9167C9.48393 22.9167 10.4167 21.9839 10.4167 20.8333C10.4167 19.6827 9.48393 18.75 8.33333 18.75C7.18274 18.75 6.25 19.6827 6.25 20.8333C6.25 21.9839 7.18274 22.9167 8.33333 22.9167Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.6667 22.9167C17.8173 22.9167 18.75 21.9839 18.75 20.8333C18.75 19.6827 17.8173 18.75 16.6667 18.75C15.5161 18.75 14.5834 19.6827 14.5834 20.8333C14.5834 21.9839 15.5161 22.9167 16.6667 22.9167Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.9167 12.5V14.5833H19.7917C19.2187 14.5833 18.75 14.1146 18.75 13.5417V10.4167C18.75 9.84375 19.2187 9.375 19.7917 9.375H21.1354L22.9167 12.5Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Voucher = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
      <path
        d="M20.3125 13.0208C20.3125 11.5833 21.4792 10.4166 22.9167 10.4166V9.37496C22.9167 5.20829 21.875 4.16663 17.7084 4.16663H7.29171C3.12504 4.16663 2.08337 5.20829 2.08337 9.37496V9.89579C3.52087 9.89579 4.68754 11.0625 4.68754 12.5C4.68754 13.9375 3.52087 15.1041 2.08337 15.1041V15.625C2.08337 19.7916 3.12504 20.8333 7.29171 20.8333H17.7084C21.875 20.8333 22.9167 19.7916 22.9167 15.625C21.4792 15.625 20.3125 14.4583 20.3125 13.0208Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.375 15.3646L15.625 9.11462"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.6192 15.3646H15.6286"
        stroke={color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.36924 9.63546H9.37859"
        stroke={color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const AppIcon = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
      <path
        d="M17.7084 10.4166H19.7917C21.875 10.4166 22.9167 9.37498 22.9167 7.29165V5.20831C22.9167 3.12498 21.875 2.08331 19.7917 2.08331H17.7084C15.625 2.08331 14.5834 3.12498 14.5834 5.20831V7.29165C14.5834 9.37498 15.625 10.4166 17.7084 10.4166Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.20837 22.9166H7.29171C9.37504 22.9166 10.4167 21.875 10.4167 19.7916V17.7083C10.4167 15.625 9.37504 14.5833 7.29171 14.5833H5.20837C3.12504 14.5833 2.08337 15.625 2.08337 17.7083V19.7916C2.08337 21.875 3.12504 22.9166 5.20837 22.9166Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.25004 10.4166C8.55123 10.4166 10.4167 8.55117 10.4167 6.24998C10.4167 3.94879 8.55123 2.08331 6.25004 2.08331C3.94885 2.08331 2.08337 3.94879 2.08337 6.24998C2.08337 8.55117 3.94885 10.4166 6.25004 10.4166Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.75 22.9166C21.0512 22.9166 22.9167 21.0512 22.9167 18.75C22.9167 16.4488 21.0512 14.5833 18.75 14.5833C16.4489 14.5833 14.5834 16.4488 14.5834 18.75C14.5834 21.0512 16.4489 22.9166 18.75 22.9166Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const Setting = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
      <path
        d="M2.08337 13.4166V11.5832C2.08337 10.4999 2.96879 9.60408 4.06254 9.60408C5.94796 9.60408 6.71879 8.27075 5.77087 6.63533C5.22921 5.69783 5.55212 4.47908 6.50004 3.93741L8.30212 2.90616C9.12504 2.41658 10.1875 2.70825 10.6771 3.53116L10.7917 3.72908C11.7292 5.3645 13.2709 5.3645 14.2188 3.72908L14.3334 3.53116C14.823 2.70825 15.8855 2.41658 16.7084 2.90616L18.5105 3.93741C19.4584 4.47908 19.7813 5.69783 19.2396 6.63533C18.2917 8.27075 19.0625 9.60408 20.948 9.60408C22.0313 9.60408 22.9271 10.4895 22.9271 11.5832V13.4166C22.9271 14.4999 22.0417 15.3957 20.948 15.3957C19.0625 15.3957 18.2917 16.7291 19.2396 18.3645C19.7813 19.3124 19.4584 20.5207 18.5105 21.0624L16.7084 22.0937C15.8855 22.5832 14.823 22.2916 14.3334 21.4687L14.2188 21.2707C13.2813 19.6353 11.7396 19.6353 10.7917 21.2707L10.6771 21.4687C10.1875 22.2916 9.12504 22.5832 8.30212 22.0937L6.50004 21.0624C5.55212 20.5207 5.22921 19.302 5.77087 18.3645C6.71879 16.7291 5.94796 15.3957 4.06254 15.3957C2.96879 15.3957 2.08337 14.4999 2.08337 13.4166Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.4999 12.0192C13.6062 12.0192 14.5031 11.1224 14.5031 10.016C14.5031 8.90968 13.6062 8.01282 12.4999 8.01282C11.3936 8.01282 10.4967 8.90968 10.4967 10.016C10.4967 11.1224 11.3936 12.0192 12.4999 12.0192Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.9415 16.9872C15.9415 15.4367 14.399 14.1827 12.5 14.1827C10.6009 14.1827 9.05847 15.4367 9.05847 16.9872"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const Policy = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
      <path
        d="M21.875 7.29165V17.7083C21.875 20.8333 20.3125 22.9166 16.6667 22.9166H8.33333C4.6875 22.9166 3.125 20.8333 3.125 17.7083V7.29165C3.125 4.16665 4.6875 2.08331 8.33333 2.08331H16.6667C20.3125 2.08331 21.875 4.16665 21.875 7.29165Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.1458 2.08331V10.2708C16.1458 10.7291 15.6041 10.9583 15.2708 10.6562L12.8542 8.42709C12.6562 8.23959 12.3437 8.23959 12.1458 8.42709L9.72915 10.6562C9.39582 10.9583 8.85413 10.7291 8.85413 10.2708V2.08331H16.1458Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.8021 14.5833H18.2292"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.375 18.75H18.2292"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const Question = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
      <path
        d="M17.7084 19.198H13.5417L8.90628 22.2813C8.21878 22.7396 7.29171 22.2501 7.29171 21.4167V19.198C4.16671 19.198 2.08337 17.1146 2.08337 13.9896V7.73958C2.08337 4.61458 4.16671 2.53125 7.29171 2.53125H17.7084C20.8334 2.53125 22.9167 4.61458 22.9167 7.73958V13.9896C22.9167 17.1146 20.8334 19.198 17.7084 19.198Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 11.8333V11.6146C12.5 10.9063 12.9375 10.5313 13.375 10.2292C13.8021 9.9375 14.2292 9.56252 14.2292 8.87502C14.2292 7.91668 13.4583 7.14581 12.5 7.14581C11.5417 7.14581 10.7709 7.91668 10.7709 8.87502"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.4953 14.3229H12.5046"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const LogoutIcon = () => (
  <svg width="30px" height="30px" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg" fill="red">
    <path d="M25.429 21.446H23.37a0.45 0.45 0 0 0 -0.36 0.17 9 9 0 0 1 -0.657 0.717 10.35 10.35 0 0 1 -3.301 2.223 10.35 10.35 0 0 1 -4.04 0.815 10.35 10.35 0 0 1 -4.04 -0.815 10.35 10.35 0 0 1 -3.301 -2.223 10.35 10.35 0 0 1 -2.226 -3.296c-0.543 -1.276 -0.816 -2.632 -0.816 -4.037a10.35 10.35 0 0 1 3.041 -7.334 10.35 10.35 0 0 1 7.341 -3.037 10.35 10.35 0 0 1 4.04 0.815 10.35 10.35 0 0 1 3.301 2.223q0.345 0.347 0.657 0.717a0.465 0.465 0 0 0 0.36 0.17h2.059a0.234 0.234 0 0 0 0.197 -0.36 12.6 12.6 0 0 0 -10.646 -5.79 12.585 12.585 0 0 0 -12.578 12.726c0.071 6.9 5.69 12.47 12.609 12.47a12.6 12.6 0 0 0 10.614 -5.792 0.234 0.234 0 0 0 -0.197 -0.36m2.604 -6.63L23.875 11.535a0.235 0.235 0 0 0 -0.381 0.184v2.226H14.297a0.234 0.234 0 0 0 -0.234 0.234v1.641c0 0.129 0.105 0.234 0.234 0.234h9.2v2.228c0 0.197 0.228 0.307 0.381 0.184l4.158 -3.282a0.234 0.234 0 0 0 0 -0.369" />
  </svg>
);

export const ZaloIcon = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M5.81413 19.5802V20.0802H6.31413H21.8646V20.0939C20.3423 22.1574 17.9072 23.5 15.1539 23.5H8.83978C4.2351 23.5 0.5 19.765 0.5 15.1603V8.84621C0.5 5.43122 2.55445 2.50046 5.49518 1.21543L5.81413 1.42893V19.5802Z"
        fill="#0A68FE"
        stroke="white"
      />
      <path
        d="M6.57928 20.7923L6.37643 20.3353C5.6344 20.6647 4.90348 20.7927 4.33085 20.8354C4.92018 20.0158 5.00012 19.1894 4.9761 18.6909L4.97595 18.688C4.95743 18.3455 4.82891 18.0338 4.64179 17.7778C2.97861 15.4532 2.3942 13.0654 2.3942 8.84605C2.3942 6.20129 3.84483 3.04237 5.82757 1.0724C6.76391 0.712451 7.78047 0.506314 8.83974 0.506314H15.1539H15.361L15.3648 0.502464C19.875 0.61117 23.5 4.30349 23.5 8.83973V15.1538C23.5 16.9095 22.9518 18.5391 22.0224 19.8817C20.1794 20.973 17.3807 21.5993 14.5288 21.5993C12.2159 21.5993 9.87754 21.4495 7.90636 20.4014L7.90638 20.4013L7.90178 20.399C7.44304 20.1611 6.88506 20.1082 6.37546 20.3357L6.57928 20.7923ZM6.57928 20.7923C6.93287 20.6345 7.33066 20.666 7.67163 20.8428L3.88213 21.3538C4.53159 21.3494 5.54213 21.2527 6.57928 20.7923Z"
        fill="white"
        stroke="white"
      />
      <path
        d="M20.0474 13.8974C18.7404 13.8974 17.6796 12.8366 17.6796 11.5296C17.6796 10.2226 18.7404 9.16183 20.0474 9.16183C21.3544 9.16183 22.4152 10.2226 22.4152 11.5296C22.4152 12.8366 21.3544 13.8974 20.0474 13.8974ZM20.0474 10.1089C19.2645 10.1089 18.6267 10.7467 18.6267 11.5296C18.6267 12.3126 19.2645 12.9503 20.0474 12.9503C20.8304 12.9503 21.4681 12.3126 21.4681 11.5296C21.4681 10.7467 20.8304 10.1089 20.0474 10.1089Z"
        fill="#0A68FE"
      />
      <path
        d="M16.7326 13.8974H16.1011C15.9242 13.8974 15.7853 13.7585 15.7853 13.5817V8.21472H16.7326V13.8974Z"
        fill="#0A68FE"
      />
      <path
        d="M13.8911 9.31969V9.64802C13.4933 9.34494 13.0071 9.16183 12.4704 9.16183C11.1634 9.16183 10.1026 10.2226 10.1026 11.5296C10.1026 12.8366 11.1634 13.8974 12.4704 13.8974C13.0071 13.8974 13.4933 13.7143 13.8911 13.4112V13.5817C13.8911 13.7585 14.03 13.8974 14.2068 13.8974H14.8382V9.31969H13.8911ZM12.4704 12.9503C11.6874 12.9503 11.0497 12.3126 11.0497 11.5296C11.0497 10.7467 11.6874 10.1089 12.4704 10.1089C13.2534 10.1089 13.8911 10.7467 13.8911 11.5296C13.8911 12.3126 13.2534 12.9503 12.4704 12.9503Z"
        fill="#0A68FE"
      />
      <path
        d="M10.2604 8.21472H5.20914V9.16184H8.56826L5.05129 13.266H5.07023C4.9692 13.3986 4.89343 13.5564 4.89343 13.7396V13.8974H9.62903C9.80583 13.8974 9.94474 13.7585 9.94474 13.5817V12.9503H6.58562L10.1026 8.84613H10.0836C10.1847 8.71354 10.2604 8.55568 10.2604 8.37257V8.21472Z"
        fill="#0A68FE"
      />
    </svg>
  );
};

export const SurPlus = () => (
  <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12.2862 3.2877C12.4841 3.30654 12.6709 3.30968 12.85 3.34423C13.7686 3.52168 14.4423 4.32099 14.4439 5.25377C14.4502 7.18688 14.4502 9.11998 14.4439 11.0531C14.4407 12.1429 13.5739 12.9925 12.4574 13.016C12.2265 13.0207 11.9973 13.0176 11.7664 13.0176C8.67597 13.0176 5.58553 13.0192 2.49665 13.016C1.63139 13.016 0.976551 12.6423 0.607519 11.8508C0.492883 11.6043 0.428499 11.3106 0.428499 11.0389C0.414366 8.16363 0.417506 5.28518 0.420647 2.4083C0.420647 1.3122 1.28748 0.428089 2.38515 0.424948C5.0202 0.417096 7.65525 0.415526 10.2903 0.424948C11.4351 0.429659 12.2784 1.29806 12.2862 2.44599C12.2878 2.72708 12.2862 3.0066 12.2862 3.2877ZM1.49791 4.16395C1.49791 4.24875 1.49791 4.30371 1.49791 4.35867C1.49791 6.55559 1.49791 8.75408 1.49791 10.951C1.49791 11.5666 1.86694 11.9403 2.48252 11.9403C5.78025 11.9419 9.07956 11.9419 12.3773 11.9403C12.9944 11.9403 13.3666 11.5697 13.3666 10.9557C13.3666 9.09014 13.3666 7.22456 13.3666 5.35899C13.3666 5.28047 13.365 5.20038 13.3509 5.12501C13.2614 4.66018 12.9049 4.38694 12.3883 4.38694C9.30412 4.38694 6.21838 4.38851 3.13421 4.38537C2.58616 4.3838 2.03026 4.44661 1.49791 4.16395ZM11.2058 3.29712C11.2058 3.04115 11.2058 2.79618 11.2058 2.5512C11.2074 1.85554 10.8556 1.50221 10.1615 1.50221C7.61599 1.50221 5.07202 1.50221 2.52649 1.50221C2.45896 1.50221 2.39144 1.50064 2.32391 1.50849C1.84181 1.56188 1.49477 1.94191 1.49791 2.41144C1.50105 2.92809 1.91091 3.30497 2.47623 3.30497C5.32485 3.30654 8.17346 3.30497 11.0221 3.30497C11.077 3.30497 11.132 3.30026 11.2058 3.29712Z"
      fill="#2677E8"
    />
  </svg>
);

export const Wallet = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
    <path
      d="M27.5 15V21.25C27.5 25 25 27.5 21.25 27.5H8.75C5 27.5 2.5 25 2.5 21.25V15C2.5 11.6 4.55 9.225 7.7375 8.825C8.0625 8.775 8.4 8.75 8.75 8.75H21.25C21.575 8.75 21.8875 8.76248 22.1875 8.81248C25.4125 9.18748 27.5 11.575 27.5 15Z"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.1893 8.8125C21.8893 8.7625 21.5768 8.75001 21.2518 8.75001H8.75176C8.40176 8.75001 8.06426 8.77501 7.73926 8.82501C7.91426 8.47501 8.16426 8.15001 8.46426 7.85001L12.5268 3.775C14.2393 2.075 17.0143 2.075 18.7268 3.775L20.9143 5.98752C21.7143 6.77502 22.1393 7.775 22.1893 8.8125Z"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27.5 15.625H23.75C22.375 15.625 21.25 16.75 21.25 18.125C21.25 19.5 22.375 20.625 23.75 20.625H27.5"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Bill = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8.45908 0.716797H2.03738C1.79408 0.716797 1.56073 0.81345 1.38869 0.985493C1.21665 1.15754 1.12 1.39088 1.12 1.63418V11.7254C1.12 11.9687 1.21665 12.2021 1.38869 12.3741C1.56073 12.5462 1.79408 12.6428 2.03738 12.6428H12.1286C12.3719 12.6428 12.6053 12.5462 12.7773 12.3741C12.9494 12.2021 13.046 11.9687 13.046 11.7254V5.30373L8.45908 0.716797Z"
      stroke="#2677E8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.78971 4.38634V3.01026M4.78971 4.38634C4.02828 4.38634 3.41364 4.38634 3.41364 5.30373C3.41364 6.67981 6.16579 6.67981 6.16579 8.05589C6.16579 8.97327 5.55115 8.97327 4.78971 8.97327M4.78971 4.38634C5.55115 4.38634 6.16579 4.73495 6.16579 5.30373M3.41364 8.05589C3.41364 8.74393 4.02828 8.97327 4.78971 8.97327M4.78971 8.97327V10.3494M8.45926 8.97327H11.2114M8.45926 4.84504V0.716797L13.0462 5.30373H8.91795C8.7963 5.30373 8.67963 5.2554 8.59361 5.16938C8.50759 5.08336 8.45926 4.96669 8.45926 4.84504Z"
      stroke="#2677E8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Cart = () => (
  <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M4.54631 11.4678C4.8254 11.4678 5.09306 11.5786 5.2904 11.776C5.48774 11.9733 5.59861 12.241 5.59861 12.5201C5.59861 12.7992 5.48774 13.0668 5.2904 13.2642C5.09306 13.4615 4.8254 13.5724 4.54631 13.5724C4.26723 13.5724 3.99957 13.4615 3.80223 13.2642C3.60488 13.0668 3.49402 12.7992 3.49402 12.5201C3.49402 12.241 3.60488 11.9733 3.80223 11.776C3.99957 11.5786 4.26723 11.4678 4.54631 11.4678ZM10.8601 11.4678C11.1392 11.4678 11.4068 11.5786 11.6042 11.776C11.8015 11.9733 11.9124 12.241 11.9124 12.5201C11.9124 12.7992 11.8015 13.0668 11.6042 13.2642C11.4068 13.4615 11.1392 13.5724 10.8601 13.5724C10.581 13.5724 10.3133 13.4615 10.116 13.2642C9.91866 13.0668 9.80779 12.7992 9.80779 12.5201C9.80779 12.241 9.91866 11.9733 10.116 11.776C10.3133 11.5786 10.581 11.4678 10.8601 11.4678Z"
      fill="#2677E8"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.01525 0.470368C1.14109 0.409915 1.28579 0.401928 1.41752 0.448163L1.62938 0.522525L1.64258 0.527176C2.06901 0.677392 2.43207 0.805284 2.71815 0.945548C3.02753 1.09778 3.29271 1.28439 3.49194 1.57622C3.68977 1.86525 3.77115 2.18305 3.80903 2.53171C3.82615 2.7038 3.83621 2.87652 3.8392 3.04944H11.3028C12.4849 3.04944 13.5484 3.04944 13.8599 3.45422C14.1713 3.859 14.05 4.46863 13.8065 5.6893L13.4558 7.39051C13.2348 8.46245 13.124 8.99842 12.7367 9.31411C12.3502 9.6298 11.8023 9.6298 10.7072 9.6298H6.98767C5.96526 9.6298 5.21008 9.6298 4.63533 9.5384H4.22447V9.44768C3.9048 9.35275 3.65312 9.20832 3.44494 8.9886C2.83671 8.3474 2.79252 7.66691 2.79252 5.60301V3.77763C2.79252 3.25849 2.79111 2.91124 2.76305 2.64466C2.73499 2.39 2.68588 2.26232 2.62275 2.16972C2.56101 2.07992 2.46771 1.99504 2.25514 1.89051C2.02855 1.77897 1.72058 1.67023 1.25196 1.50537L1.06886 1.44153C0.937127 1.3953 0.829161 1.29862 0.768708 1.17278C0.708255 1.04695 0.700268 0.902245 0.746503 0.770516C0.792738 0.638788 0.889409 0.530821 1.01525 0.470368Z"
      fill="#2677E8"
    />
    <path
      d="M10.1506 4.02137C10.064 3.9732 9.96883 3.94257 9.87043 3.93123C9.77202 3.91988 9.67235 3.92805 9.5771 3.95526C9.48185 3.98247 9.39291 4.02818 9.31534 4.08979C9.23778 4.1514 9.17312 4.2277 9.12506 4.31432L7.72515 6.83364L6.92395 6.03245C6.85439 5.96043 6.77119 5.90298 6.67919 5.86347C6.58719 5.82395 6.48824 5.80314 6.38812 5.80227C6.28799 5.8014 6.1887 5.82048 6.09603 5.8584C6.00336 5.89631 5.91916 5.9523 5.84836 6.02311C5.77756 6.09391 5.72157 6.1781 5.68365 6.27077C5.64574 6.36344 5.62666 6.46274 5.62753 6.56286C5.6284 6.66298 5.6492 6.76193 5.68872 6.85393C5.72824 6.94593 5.78569 7.02914 5.85771 7.0987L7.36584 8.60682C7.50835 8.74972 7.70064 8.82814 7.89896 8.82814L8.0034 8.8206C8.11897 8.80443 8.22922 8.76166 8.32546 8.69564C8.4217 8.62963 8.50131 8.54218 8.55801 8.44018L10.4432 5.04689C10.4913 4.96034 10.5219 4.86516 10.5333 4.76679C10.5446 4.66841 10.5365 4.56876 10.5093 4.47353C10.4821 4.3783 10.4365 4.28935 10.3749 4.21177C10.3134 4.13419 10.2371 4.06949 10.1506 4.02137Z"
      fill="white"
    />
  </svg>
);

export const Agency = () => (
  <svg width="19" height="16" viewBox="0 0 19 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M18.871 14.5922C18.7843 14.8995 18.6837 15.1976 18.4613 15.4421C18.1762 15.755 17.8292 15.9681 17.4093 15.9811C16.5695 16.006 15.7288 15.9931 14.8881 15.9949C14.8715 15.9949 14.854 15.9848 14.8706 15.9903C14.8872 15.6498 14.9149 15.3212 14.9158 14.9927C14.9204 13.447 14.9167 11.9003 14.9186 10.3546C14.9195 9.80086 14.8983 9.25177 14.7128 8.72207C14.7054 8.70084 14.7118 8.67408 14.7118 8.62425C15.5987 8.67685 16.5058 8.48029 17.3475 8.91679C18.1743 9.3459 18.6763 10.015 18.8351 10.9369C18.8397 10.9655 18.8581 10.9913 18.8692 11.019C18.871 12.2094 18.871 13.4008 18.871 14.5922Z"
      fill={COLORS.primary}
    />
    <path
      d="M9.4392 15.9972C8.19892 15.9972 6.95955 15.9972 5.71927 15.9972C5.2394 15.9972 5.05022 15.8098 5.05114 15.3309C5.05206 13.4834 5.0456 11.6359 5.06037 9.78744C5.0696 8.58868 6.08563 7.44529 7.27147 7.26349C7.44035 7.23765 7.61292 7.22012 7.78364 7.2192C8.9501 7.21735 10.1166 7.21089 11.2821 7.22658C12.4652 7.24319 13.6104 8.26569 13.783 9.44045C13.8134 9.64532 13.8263 9.85573 13.8273 10.0624C13.831 11.8121 13.8291 13.5618 13.8291 15.3106C13.8291 15.8135 13.6464 15.9944 13.1416 15.9944C11.9069 15.9972 10.673 15.9972 9.4392 15.9972Z"
      fill={COLORS.primary}
    />
    <path
      d="M12.7158 3.22812C12.7195 5.05532 11.3002 6.54385 9.4416 6.5457C7.61439 6.54754 6.18954 5.10239 6.16463 3.28718C6.13879 1.49319 7.61624 0.0102039 9.42776 5.27847e-05C11.218 -0.0100984 12.7001 1.44521 12.7158 3.22812Z"
      fill={COLORS.primary}
    />
    <path
      d="M3.99981 15.9918C4.01457 15.9891 3.99058 15.9964 3.96659 15.9964C3.16834 15.9955 2.37009 16.0094 1.57276 15.9881C0.719144 15.9651 0.0297888 15.2822 0.0159464 14.4322C-0.00251027 13.3405 -0.0089701 12.247 0.0187148 11.1553C0.0491683 9.93252 1.13719 8.77252 2.35348 8.67101C2.94963 8.62118 3.55316 8.66271 4.16223 8.66271C4.16223 8.67378 4.17146 8.71162 4.1613 8.74115C3.99243 9.22286 3.96105 9.72304 3.96105 10.2278C3.96197 11.7376 3.95828 13.2473 3.9629 14.7571C3.9629 15.1659 3.98597 15.5738 3.99981 15.9918Z"
      fill={COLORS.primary}
    />
    <path
      d="M3.69446 3.04982C5.04548 3.05443 6.15749 4.16737 6.14181 5.49809C6.12612 6.85004 5.02795 7.95098 3.69261 7.95282C2.34712 7.95651 1.22219 6.82881 1.23327 5.48886C1.24526 4.13968 2.34989 3.04521 3.69446 3.04982Z"
      fill={COLORS.primary}
    />
    <path
      d="M15.1873 3.04981C16.5328 3.04704 17.6375 4.14337 17.6458 5.4907C17.6541 6.8325 16.5291 7.95835 15.1846 7.95374C13.8483 7.94912 12.7501 6.84634 12.7381 5.49716C12.7252 4.16367 13.8382 3.05258 15.1873 3.04981Z"
      fill={COLORS.primary}
    />
  </svg>
);

export const Map = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
      <path
        d="M22.9167 9.375V15.625C22.9167 18.2292 22.3959 20.0521 21.2292 21.2292L14.5834 14.5833L22.6355 6.53125C22.823 7.35417 22.9167 8.29166 22.9167 9.375Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.6355 6.53166L6.53128 22.6358C3.39586 21.9171 2.08337 19.7504 2.08337 15.6254V9.37541C2.08337 4.16707 4.16671 2.08374 9.37504 2.08374H15.625C19.75 2.08374 21.9167 3.39624 22.6355 6.53166Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.2292 21.2296C20.0521 22.3962 18.2292 22.9171 15.625 22.9171H9.37501C8.29168 22.9171 7.35417 22.8233 6.53125 22.6358L14.5833 14.5837L21.2292 21.2296Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.50003 8.31274C7.20836 5.26066 11.7917 5.26066 12.5 8.31274C12.9063 10.1044 11.7813 11.6252 10.7917 12.5627C10.0729 13.2502 8.93755 13.2502 8.20838 12.5627C7.2188 11.6252 6.08336 10.1044 6.50003 8.31274Z"
        stroke={color.primary}
        strokeWidth="1.5"
      />
      <path
        d="M9.47361 9.06307H9.48296"
        stroke={color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const RightChevron = ({
  width = 10,
  height = 21,
  strokeColor = "#C5C5C5",
  strokeWidth = 1.2,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 10 21"
      fill="none"
    >
      <path
        d="M1 20L8.45757 11.1441C8.77102 10.7719 8.77102 10.2281 8.45757 9.85586L1 1"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
    </svg>
  );
};

export const Box = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
    <path
      d="M3.96265 9.30005L15.0001 15.6875L25.9626 9.33751"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 27.0126V15.675"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.4125 3.1L5.73753 6.81254C4.22503 7.65004 2.98755 9.75002 2.98755 11.475V18.5375C2.98755 20.2625 4.22503 22.3625 5.73753 23.2L12.4125 26.9126C13.8375 27.7001 16.175 27.7001 17.6 26.9126L24.275 23.2C25.7875 22.3625 27.025 20.2625 27.025 18.5375V11.475C27.025 9.75002 25.7875 7.65004 24.275 6.81254L17.6 3.1C16.1625 2.3 13.8375 2.3 12.4125 3.1Z"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21.2499 16.55V11.9751L9.38745 5.125"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Car = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
    <path
      d="M14.9999 17.5H16.2499C17.6249 17.5 18.7499 16.375 18.7499 15V2.5H7.49994C5.62494 2.5 3.98745 3.53748 3.13745 5.06248"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 21.25C2.5 23.325 4.175 25 6.25 25H7.5C7.5 23.625 8.625 22.5 10 22.5C11.375 22.5 12.5 23.625 12.5 25H17.5C17.5 23.625 18.625 22.5 20 22.5C21.375 22.5 22.5 23.625 22.5 25H23.75C25.825 25 27.5 23.325 27.5 21.25V17.5H23.75C23.0625 17.5 22.5 16.9375 22.5 16.25V12.5C22.5 11.8125 23.0625 11.25 23.75 11.25H25.3625L23.225 7.51251C22.775 6.73751 21.95 6.25 21.05 6.25H18.75V15C18.75 16.375 17.625 17.5 16.25 17.5H15"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 27.5C11.3807 27.5 12.5 26.3807 12.5 25C12.5 23.6193 11.3807 22.5 10 22.5C8.61929 22.5 7.5 23.6193 7.5 25C7.5 26.3807 8.61929 27.5 10 27.5Z"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20 27.5C21.3807 27.5 22.5 26.3807 22.5 25C22.5 23.6193 21.3807 22.5 20 22.5C18.6193 22.5 17.5 23.6193 17.5 25C17.5 26.3807 18.6193 27.5 20 27.5Z"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27.5 15V17.5H23.75C23.0625 17.5 22.5 16.9375 22.5 16.25V12.5C22.5 11.8125 23.0625 11.25 23.75 11.25H25.3625L27.5 15Z"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 10H10"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 13.75H7.5"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 17.5H5"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Star = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
    <path
      d="M17.1624 4.38755L19.3624 8.78755C19.6624 9.40005 20.4624 9.98755 21.1374 10.1L25.1249 10.7625C27.6749 11.1875 28.2749 13.0375 26.4374 14.8625L23.3374 17.9625C22.8124 18.4875 22.5249 19.5 22.6874 20.225L23.5749 24.0625C24.2749 27.1 22.6624 28.275 19.9749 26.6875L16.2374 24.475C15.5624 24.075 14.4499 24.075 13.7624 24.475L10.0249 26.6875C7.34989 28.275 5.72489 27.0875 6.42489 24.0625L7.31239 20.225C7.47489 19.5 7.18739 18.4875 6.66239 17.9625L3.56239 14.8625C1.73739 13.0375 2.32489 11.1875 4.87489 10.7625L8.86239 10.1C9.52489 9.98755 10.3249 9.40005 10.6249 8.78755L12.8249 4.38755C14.0249 2.00005 15.9749 2.00005 17.1624 4.38755Z"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SearchNormal = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M11.5 21C16.7467 21 21 16.7467 21 11.5C21 6.25329 16.7467 2 11.5 2C6.25329 2 2 6.25329 2 11.5C2 16.7467 6.25329 21 11.5 21Z"
      stroke="#9F9F9F"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22 22L20 20"
      stroke="#9F9F9F"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SearchIcon = ({ sx = {} }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style={sx}
  >
    <path
      d="M11.5 21C16.7467 21 21 16.7467 21 11.5C21 6.25329 16.7467 2 11.5 2C6.25329 2 2 6.25329 2 11.5C2 16.7467 6.25329 21 11.5 21Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22 22L20 20"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Phone = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M21.97 18.33C21.97 18.69 21.89 19.06 21.72 19.42C21.55 19.78 21.33 20.12 21.04 20.44C20.55 20.98 20.01 21.37 19.4 21.62C18.8 21.87 18.15 22 17.45 22C16.43 22 15.34 21.76 14.19 21.27C13.04 20.78 11.89 20.12 10.75 19.29C9.6 18.45 8.51 17.52 7.47 16.49C6.44 15.45 5.51 14.36 4.68 13.22C3.86 12.08 3.2 10.94 2.72 9.81C2.24 8.67 2 7.58 2 6.54C2 5.86 2.12 5.21 2.36 4.61C2.6 4 2.98 3.44 3.51 2.94C4.15 2.31 4.85 2 5.59 2C5.87 2 6.15 2.06 6.4 2.18C6.66 2.3 6.89 2.48 7.07 2.74L9.39 6.01C9.57 6.26 9.7 6.49 9.79 6.71C9.88 6.92 9.93 7.13 9.93 7.32C9.93 7.56 9.86 7.8 9.72 8.03C9.59 8.26 9.4 8.5 9.16 8.74L8.4 9.53C8.29 9.64 8.24 9.77 8.24 9.93C8.24 10.01 8.25 10.08 8.27 10.16C8.3 10.24 8.33 10.3 8.35 10.36C8.53 10.69 8.84 11.12 9.28 11.64C9.73 12.16 10.21 12.69 10.73 13.22C11.27 13.75 11.79 14.24 12.32 14.69C12.84 15.13 13.27 15.43 13.61 15.61C13.66 15.63 13.72 15.66 13.79 15.69C13.87 15.72 13.95 15.73 14.04 15.73C14.21 15.73 14.34 15.67 14.45 15.56L15.21 14.81C15.46 14.56 15.7 14.37 15.93 14.25C16.16 14.11 16.39 14.04 16.64 14.04C16.83 14.04 17.03 14.08 17.25 14.17C17.47 14.26 17.7 14.39 17.95 14.56L21.26 16.91C21.52 17.09 21.7 17.3 21.81 17.55C21.91 17.8 21.97 18.05 21.97 18.33Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
      />
    </svg>
  );
};

export const EmptyWalletAdd = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M15.0333 11.2918C14.6833 11.6334 14.4833 12.1251 14.5333 12.6501C14.6083 13.5501 15.4333 14.2084 16.3333 14.2084H17.9167V15.2001C17.9167 16.9251 16.5083 18.3335 14.7833 18.3335H6.35834C6.61667 18.1168 6.84168 17.8501 7.01668 17.5501C7.32501 17.0501 7.50001 16.4585 7.50001 15.8335C7.50001 13.9918 6.00834 12.5001 4.16668 12.5001C3.38334 12.5001 2.65834 12.7751 2.08334 13.2335V9.59179C2.08334 7.86679 3.49168 6.45845 5.21668 6.45845H14.7833C16.5083 6.45845 17.9167 7.86679 17.9167 9.59179V10.7918H16.2333C15.7667 10.7918 15.3417 10.9751 15.0333 11.2918Z"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.08333 10.3417V6.53342C2.08333 5.54175 2.69166 4.65838 3.61666 4.30838L10.2333 1.80838C11.2667 1.41672 12.375 2.18341 12.375 3.29174V6.4584"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.799 11.6418V13.3585C18.799 13.8168 18.4323 14.1918 17.9657 14.2085H16.3323C15.4323 14.2085 14.6073 13.5501 14.5323 12.6501C14.4823 12.1251 14.6823 11.6335 15.0323 11.2918C15.3407 10.9751 15.7657 10.7918 16.2323 10.7918H17.9657C18.4323 10.8085 18.799 11.1835 18.799 11.6418Z"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.83333 10H11.6667"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.49999 15.8333C7.49999 16.4583 7.32499 17.05 7.01666 17.55C6.84166 17.85 6.61666 18.1167 6.35833 18.3333C5.77499 18.8583 5.00833 19.1667 4.16666 19.1667C2.94999 19.1667 1.89166 18.5167 1.31666 17.55C1.00833 17.05 0.833328 16.4583 0.833328 15.8333C0.833328 14.7833 1.31666 13.8417 2.08333 13.2333C2.65833 12.775 3.38333 12.5 4.16666 12.5C6.00833 12.5 7.49999 13.9917 7.49999 15.8333Z"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.40977 15.8162H2.92644"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.16667 14.5996V17.0913"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const WalletTick = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M15.0332 11.2917C14.6832 11.6334 14.4833 12.1251 14.5333 12.6501C14.6083 13.5501 15.4333 14.2084 16.3333 14.2084H17.9166V15.2001C17.9166 16.9251 16.5083 18.3334 14.7833 18.3334H6.35826C6.61659 18.1167 6.84158 17.8501 7.01658 17.5501C7.32492 17.0501 7.49992 16.4584 7.49992 15.8334C7.49992 13.9917 6.00825 12.5001 4.16659 12.5001C3.38325 12.5001 2.65825 12.7751 2.08325 13.2334V9.59175C2.08325 7.86675 3.49158 6.4584 5.21658 6.4584H14.7833C16.5083 6.4584 17.9166 7.86675 17.9166 9.59175V10.7917H16.2333C15.7666 10.7917 15.3416 10.9751 15.0332 11.2917Z"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.08313 10.3418V6.5335C2.08313 5.54183 2.69146 4.65846 3.61646 4.30846L10.2331 1.80846C11.2665 1.41679 12.3748 2.18348 12.3748 3.29182V6.45847"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.7988 11.6419V13.3586C18.7988 13.817 18.4321 14.1919 17.9655 14.2086H16.3321C15.4321 14.2086 14.6071 13.5503 14.5321 12.6503C14.4821 12.1253 14.6821 11.6336 15.0321 11.2919C15.3405 10.9753 15.7655 10.7919 16.2321 10.7919H17.9655C18.4321 10.8086 18.7988 11.1836 18.7988 11.6419Z"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.83313 10H11.6665"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.4998 15.8333C7.4998 16.4583 7.32479 17.05 7.01646 17.55C6.84146 17.85 6.61647 18.1167 6.35813 18.3333C5.7748 18.8583 5.00813 19.1667 4.16646 19.1667C2.9498 19.1667 1.89146 18.5167 1.31646 17.55C1.00813 17.05 0.83313 16.4583 0.83313 15.8333C0.83313 14.7833 1.31646 13.8417 2.08313 13.2333C2.65813 12.775 3.38313 12.5 4.16646 12.5C6.00813 12.5 7.4998 13.9917 7.4998 15.8333Z"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.86816 15.8328L3.69316 16.6578L5.46816 15.0162"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const WalletTime = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M15.0332 11.2918C14.6832 11.6335 14.4833 12.1252 14.5333 12.6502C14.6083 13.5502 15.4333 14.2085 16.3333 14.2085H17.9166V15.2002C17.9166 16.9252 16.5083 18.3335 14.7833 18.3335H6.36659C7.05825 17.7252 7.49992 16.8335 7.49992 15.8335C7.49992 13.9918 6.00825 12.5002 4.16659 12.5002C3.38325 12.5002 2.65825 12.7752 2.08325 13.2335V9.59185C2.08325 7.86685 3.49158 6.45851 5.21658 6.45851H14.7833C16.5083 6.45851 17.9166 7.86685 17.9166 9.59185V10.7919H16.2333C15.7666 10.7919 15.3416 10.9752 15.0332 11.2918Z"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.08313 9.59183V6.53351C2.08313 5.54184 2.69146 4.65847 3.61646 4.30847L10.2331 1.80847C11.2665 1.42514 12.3748 2.1835 12.3748 3.29183V6.45849"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.7988 11.6419V13.3586C18.7988 13.817 18.4321 14.1919 17.9655 14.2086H16.3321C15.4321 14.2086 14.6071 13.5503 14.5321 12.6503C14.4821 12.1253 14.6821 11.6336 15.0321 11.2919C15.3405 10.9753 15.7655 10.7919 16.2321 10.7919H17.9655C18.4321 10.8086 18.7988 11.1836 18.7988 11.6419Z"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.83313 10H11.6665"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.4998 15.8333C7.4998 16.8333 7.05813 17.725 6.36646 18.3333C5.7748 18.85 5.00813 19.1667 4.16646 19.1667C2.3248 19.1667 0.83313 17.675 0.83313 15.8333C0.83313 14.7833 1.31646 13.8417 2.08313 13.2333C2.65813 12.775 3.38313 12.5 4.16646 12.5C6.00813 12.5 7.4998 13.9917 7.4998 15.8333Z"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.37492 14.7915V16.0415L3.33325 16.6665"
        stroke={color.primary}
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const People = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M10.9491 10.54C14.3802 10.5401 17.3094 11.084 17.3094 13.2607C17.3091 15.4362 14.3988 16 10.9491 16C7.51793 16 4.58782 15.4561 4.58773 13.2803C4.58773 11.1035 7.49907 10.54 10.9491 10.54ZM5.56527 9.22949C5.58496 9.23219 5.59473 9.24678 5.59652 9.25488C5.59902 9.26833 5.59376 9.28749 5.56917 9.30176C4.9621 9.60346 2.61844 10.9161 2.91292 13.6826C2.92546 13.8033 2.83084 13.9058 2.71175 13.8887C2.13505 13.8058 0.651458 13.4851 0.191242 12.4863C-0.0637435 11.9578 -0.0637514 11.3459 0.191242 10.8174C0.49208 10.1735 1.2172 9.72968 2.32113 9.51172C2.84312 9.38474 4.25265 9.20519 5.56527 9.22949ZM16.4354 9.22852C17.7467 9.20426 19.1562 9.38483 19.6776 9.5127C20.7816 9.72973 21.5085 10.1725 21.8094 10.8164C22.0637 11.345 22.0636 11.9586 21.8094 12.4863C21.3493 13.4849 19.8658 13.8057 19.2889 13.8887C19.1699 13.9067 19.0735 13.8032 19.0858 13.6836C19.3803 10.9162 17.0378 9.60355 16.4315 9.30176C16.4055 9.28825 16.4005 9.26749 16.4032 9.25488C16.4051 9.24586 16.4158 9.2312 16.4354 9.22852ZM10.9491 0C13.2849 0.000101932 15.1578 1.8821 15.158 4.23242C15.158 6.58204 13.2851 8.46669 10.9491 8.4668C8.61295 8.4668 6.73909 6.5821 6.73909 4.23242C6.73932 1.88204 8.61309 0 10.9491 0ZM5.33577 0.706055C5.58102 0.706055 5.81562 0.731521 6.04476 0.77832C6.0752 0.785525 6.10986 0.801107 6.12777 0.828125C6.14828 0.862339 6.13264 0.908781 6.11116 0.938477C5.43344 1.89398 5.0428 3.05962 5.0428 4.30957C5.04289 5.50721 5.40047 6.62327 6.02718 7.55078C6.09126 7.64616 6.0342 7.77502 5.92073 7.79492C5.76326 7.82282 5.60196 7.83728 5.43734 7.8418C3.79431 7.88503 2.3194 6.82094 1.91195 5.21973C1.30755 2.84122 3.07936 0.706055 5.33577 0.706055ZM16.6629 0.706055C18.9193 0.706055 20.6912 2.84122 20.0877 5.21973C19.6803 6.82083 18.2062 7.88486 16.5633 7.8418C16.3987 7.8373 16.2364 7.82188 16.0799 7.79492C15.9663 7.77513 15.9084 7.64621 15.9725 7.55078C16.5992 6.62325 16.9568 5.50725 16.9569 4.30957C16.9569 3.05955 16.5663 1.89401 15.8885 0.938477C15.867 0.908757 15.8514 0.862348 15.8729 0.828125C15.8908 0.800455 15.9237 0.786487 15.9549 0.779297C16.1831 0.732485 16.4177 0.706077 16.6629 0.706055Z"
        fill="url(#paint0_linear_13207_1048)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_13207_1048"
          x1="22"
          y1="16"
          x2="-2.48684"
          y2="6.23786"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FB9400" />
          <stop offset="1" stop-color="#FFAB38" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ProfileTick = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M4.81 0H13.191C16.28 0 18 1.78 18 4.83V15.16C18 18.26 16.28 20 13.191 20H4.81C1.77 20 0 18.26 0 15.16V4.83C0 1.78 1.77 0 4.81 0ZM5.08 4.66V4.65H8.069C8.5 4.65 8.85 5 8.85 5.429C8.85 5.87 8.5 6.22 8.069 6.22H5.08C4.649 6.22 4.3 5.87 4.3 5.44C4.3 5.01 4.649 4.66 5.08 4.66ZM5.08 10.74H12.92C13.35 10.74 13.7 10.39 13.7 9.96C13.7 9.53 13.35 9.179 12.92 9.179H5.08C4.649 9.179 4.3 9.53 4.3 9.96C4.3 10.39 4.649 10.74 5.08 10.74ZM5.08 15.31H12.92C13.319 15.27 13.62 14.929 13.62 14.53C13.62 14.12 13.319 13.78 12.92 13.74H5.08C4.78 13.71 4.49 13.85 4.33 14.11C4.17 14.36 4.17 14.69 4.33 14.95C4.49 15.2 4.78 15.35 5.08 15.31Z"
        fill="url(#paint0_linear_13207_933)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_13207_933"
          x1="18"
          y1="20"
          x2="-3.73868"
          y2="14.3274"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6949FF" />
          <stop offset="1" stop-color="#876DFF" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const UserAdd = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M8 13.1738C12.3384 13.1738 15.9997 13.8789 16 16.5986C16 19.3197 12.3146 20 8 20C3.6625 20 0.000205662 19.295 0 16.5752C0 13.8542 3.68538 13.1738 8 13.1738ZM8 0C10.9391 0 13.2939 2.354 13.2939 5.29102C13.2939 8.22804 10.9391 10.583 8 10.583C5.0619 10.583 2.70605 8.22804 2.70605 5.29102C2.70607 2.354 5.06191 1.27348e-07 8 0Z"
        fill="url(#paint0_linear_13207_1064)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_13207_1064"
          x1="16"
          y1="20"
          x2="-3.58532"
          y2="15.4571"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FB9400" />
          <stop offset="1" stop-color="#FFAB38" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const BoxTick = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M5.42672 16.9081C6.25586 16.9084 6.93549 17.6019 6.93551 18.4589C6.93551 19.3058 6.25587 19.9997 5.42672 19.9999C4.58738 19.9999 3.90719 19.3059 3.90719 18.4589C3.90722 17.6017 4.58739 16.9081 5.42672 16.9081ZM16.6679 16.9081C17.4971 16.9084 18.1767 17.6018 18.1767 18.4589C18.1767 19.3058 17.4971 19.9997 16.6679 19.9999C15.8286 19.9999 15.1484 19.3059 15.1484 18.4589C15.1484 17.6017 15.8286 16.9081 16.6679 16.9081ZM0.010706 0.640547C0.0806422 0.223214 0.470225 -0.0534427 0.879847 0.00871079L3.26364 0.374922C3.60315 0.437338 3.85281 0.722434 3.88278 1.06926L4.07223 3.35539C4.10239 3.68259 4.36221 3.92743 4.6816 3.92766H18.1767C18.7861 3.9277 19.1863 4.14186 19.5859 4.61125C19.9853 5.08062 20.0547 5.75405 19.9648 6.36516L19.0156 13.0595C18.8357 14.3462 17.7571 15.2947 16.4882 15.2948H5.5859C4.25723 15.2946 3.15882 14.2549 3.04879 12.9081L2.12887 1.78312L0.620081 1.51848C0.220481 1.44703 -0.0591203 1.04867 0.010706 0.640547ZM12.1211 7.70207C11.7015 7.70218 11.372 8.03915 11.372 8.4677C11.3721 8.88602 11.7015 9.23321 12.1211 9.23332H14.8886C15.3083 9.23332 15.6386 8.88609 15.6386 8.4677C15.6386 8.03908 15.3083 7.70207 14.8886 7.70207H12.1211Z"
        fill="url(#paint0_linear_13207_117)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_13207_117"
          x1="19.9997"
          y1="19.9999"
          x2="-3.79819"
          y2="13.1"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF5A5F" />
          <stop offset="1" stop-color="#FF8A9B" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const Export = ({ width = 22, height = 22 }) => {
  const { color } = useConfigApp();
  return (
    <svg width="18" height="22" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M14.5845 0.333008C14.085 0.332834 13.5916 0.446811 13.139 0.666899C12.6864 0.886988 12.2857 1.20783 11.9652 1.60678C11.6446 2.00574 11.412 2.47309 11.2838 2.97587C11.1556 3.47865 11.1348 4.00461 11.223 4.51664L6.25444 8.14013L6.18301 8.19811C5.67893 7.78771 5.07428 7.53311 4.43799 7.46332C3.8017 7.39353 3.15938 7.51137 2.58429 7.80341C2.00919 8.09545 1.52446 8.54992 1.18538 9.11501C0.846297 9.68009 0.666504 10.3331 0.666504 10.9994C0.666504 11.6658 0.846297 12.3187 1.18538 12.8838C1.52446 13.4489 2.00919 13.9034 2.58429 14.1954C3.15938 14.4875 3.8017 14.6053 4.43799 14.5355C5.07428 14.4657 5.67893 14.2111 6.18301 13.8007L6.25444 13.8598L11.223 17.4833C11.188 17.6875 11.1705 17.8968 11.1705 18.1113C11.1707 18.9542 11.4583 19.7697 11.9822 20.4122C12.5061 21.0547 13.2321 21.4825 14.0307 21.6192C14.8294 21.756 15.6487 21.5927 16.3424 21.1587C17.0362 20.7246 17.5594 20.048 17.8186 19.2494C18.0778 18.4509 18.0562 17.5824 17.7576 16.7989C17.459 16.0154 16.9029 15.3679 16.1884 14.9719C15.4739 14.5759 14.6476 14.4571 13.8569 14.6367C13.0661 14.8164 12.3624 15.2828 11.8712 15.9527L7.16622 12.5207C7.37631 12.059 7.49396 11.5437 7.49396 10.9989C7.49396 10.454 7.37631 9.93764 7.16622 9.47705L11.8722 6.04612C12.2246 6.52692 12.6889 6.90614 13.2213 7.14791C13.7536 7.38969 14.3364 7.48602 14.9145 7.4278C15.4925 7.36959 16.0468 7.15874 16.5248 6.81521C17.0028 6.47169 17.3888 6.00683 17.6462 5.46462C17.9036 4.92241 18.0239 4.32077 17.9958 3.71661C17.9677 3.11245 17.7921 2.52574 17.4856 2.01199C17.1791 1.49823 16.7518 1.07441 16.2441 0.780611C15.7365 0.486815 15.1652 0.332756 14.5845 0.333008ZM12.7462 3.88866C12.7462 3.38088 12.9399 2.8939 13.2846 2.53485C13.6294 2.17579 14.0969 1.97408 14.5845 1.97408C15.072 1.97408 15.5396 2.17579 15.8843 2.53485C16.2291 2.8939 16.4227 3.38088 16.4227 3.88866C16.4227 4.39644 16.2291 4.88342 15.8843 5.24247C15.5396 5.60153 15.072 5.80324 14.5845 5.80324C14.0969 5.80324 13.6294 5.60153 13.2846 5.24247C12.9399 4.88342 12.7462 4.39644 12.7462 3.88866ZM4.08001 9.08538C3.59247 9.08538 3.1249 9.2871 2.78015 9.64615C2.43541 10.0052 2.24174 10.4922 2.24174 11C2.24174 11.5077 2.43541 11.9947 2.78015 12.3538C3.1249 12.7128 3.59247 12.9145 4.08001 12.9145C4.56756 12.9145 5.03513 12.7128 5.37987 12.3538C5.72462 11.9947 5.91829 11.5077 5.91829 11C5.91829 10.4922 5.72462 10.0052 5.37987 9.64615C5.03513 9.2871 4.56756 9.08538 4.08001 9.08538ZM14.5845 16.1967C14.0969 16.1967 13.6294 16.3984 13.2846 16.7575C12.9399 17.1165 12.7462 17.6035 12.7462 18.1113C12.7462 18.619 12.9399 19.106 13.2846 19.4651C13.6294 19.8241 14.0969 20.0259 14.5845 20.0259C15.072 20.0259 15.5396 19.8241 15.8843 19.4651C16.2291 19.106 16.4227 18.619 16.4227 18.1113C16.4227 17.6035 16.2291 17.1165 15.8843 16.7575C15.5396 16.3984 15.072 16.1967 14.5845 16.1967Z"
        fill={color.primary}
      />
    </svg>
  );
};

export const LongRightArrow = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M14.4301 5.92999L20.5001 12L14.4301 18.07"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.5 12H20.33"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Copys = () => {
  const { color } = useConfigApp();
  return (
    <svg width="19" height="19" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M4.125 13.5H3.5C2.11458 13.5 1 12.3854 1 11V3.5C1 2.11458 2.11458 1 3.5 1H11C12.3854 1 13.5 2.11458 13.5 3.5V4.125M9.75 7.25H17.25C17.913 7.25 18.5489 7.51339 19.0178 7.98223C19.4866 8.45107 19.75 9.08696 19.75 9.75V17.25C19.75 17.913 19.4866 18.5489 19.0178 19.0178C18.5489 19.4866 17.913 19.75 17.25 19.75H9.75C9.08696 19.75 8.45107 19.4866 7.98223 19.0178C7.51339 18.5489 7.25 17.913 7.25 17.25V9.75C7.25 9.42169 7.31466 9.09661 7.4403 8.79329C7.56594 8.48998 7.75009 8.21438 7.98223 7.98223C8.21438 7.75009 8.48998 7.56594 8.79329 7.4403C9.09661 7.31466 9.42169 7.25 9.75 7.25Z"
        stroke={color.primary}
        strokeWidth="2"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Download = () => {
  const { color } = useConfigApp();
  return (
    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M1 16H16"
        stroke={color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.5 1V12.25"
        stroke={color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.3337 6.83398L8.50033 12.6673L2.66699 6.83398"
        stroke={color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Share = () => {
  const { color } = useConfigApp();
  return (
    <svg width="22" height="19" viewBox="0 0 22 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21.5 9.00065L13.3333 0.833984V5.50065C5.16667 6.66732 1.66667 12.5007 0.5 18.334C3.41667 14.2507 7.5 12.384 13.3333 12.384V17.1673L21.5 9.00065Z"
        fill={color.primary}
      />
    </svg>
  );
};

export const MoneyRecive = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
      <path
        d="M11.875 17.1874C11.875 18.3999 12.8125 19.3749 13.9625 19.3749H16.3125C17.3125 19.3749 18.125 18.5249 18.125 17.4624C18.125 16.3249 17.625 15.9124 16.8875 15.6499L13.125 14.3374C12.3875 14.0749 11.8875 13.6749 11.8875 12.5249C11.8875 11.4749 12.7 10.6124 13.7 10.6124H16.05C17.2 10.6124 18.1375 11.5874 18.1375 12.7999"
        stroke={color.primary}
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 9.375V20.625"
        stroke={color.primary}
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M27.5 15C27.5 21.9 21.9 27.5 15 27.5C8.1 27.5 2.5 21.9 2.5 15C2.5 8.1 8.1 2.5 15 2.5"
        stroke={color.primary}
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.25 3.75V8.75H26.25"
        stroke={color.primary}
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M27.5 2.5L21.25 8.75"
        stroke={color.primary}
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const RoundShare = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
      <circle cx="15" cy="15" r="15" fill={color.primary} />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.4385 6.99996C18.0639 6.99983 17.6938 7.08531 17.3544 7.25038C17.0149 7.41545 16.7144 7.65608 16.474 7.95529C16.2336 8.25451 16.0591 8.60502 15.963 8.98211C15.8668 9.35919 15.8512 9.75366 15.9174 10.1377L12.1909 12.8553L12.1374 12.8988C11.7593 12.591 11.3058 12.4 10.8286 12.3477C10.3514 12.2954 9.86966 12.3837 9.43834 12.6028C9.00702 12.8218 8.64347 13.1626 8.38916 13.5865C8.13484 14.0103 8 14.5 8 14.9998C8 15.4995 8.13484 15.9893 8.38916 16.4131C8.64347 16.8369 9.00702 17.1777 9.43834 17.3968C9.86966 17.6158 10.3514 17.7042 10.8286 17.6518C11.3058 17.5995 11.7593 17.4085 12.1374 17.1007L12.1909 17.1451L15.9174 19.8627C15.8911 20.0158 15.878 20.1728 15.878 20.3337C15.8781 20.9659 16.0939 21.5775 16.4868 22.0594C16.8797 22.5413 17.4242 22.8621 18.0232 22.9646C18.6221 23.0672 19.2366 22.9448 19.757 22.6192C20.2773 22.2937 20.6697 21.7862 20.8641 21.1873C21.0585 20.5884 21.0423 19.937 20.8183 19.3494C20.5944 18.7618 20.1773 18.2761 19.6414 17.9791C19.1056 17.6821 18.4858 17.593 17.8928 17.7277C17.2997 17.8625 16.7719 18.2123 16.4035 18.7147L12.8748 16.1407C13.0324 15.7945 13.1206 15.408 13.1206 14.9994C13.1206 14.5907 13.0324 14.2034 12.8748 13.858L16.4043 11.2848C16.6686 11.6454 17.0168 11.9298 17.4161 12.1111C17.8154 12.2925 18.2524 12.3647 18.686 12.3211C19.1195 12.2774 19.5352 12.1193 19.8937 11.8616C20.2522 11.604 20.5417 11.2553 20.7348 10.8487C20.9278 10.442 21.0181 9.99078 20.997 9.53766C20.9759 9.08454 20.8442 8.64451 20.6143 8.25919C20.3844 7.87388 20.0639 7.55601 19.6832 7.33566C19.3025 7.11532 18.874 6.99977 18.4385 6.99996ZM17.0598 9.6667C17.0598 9.28587 17.205 8.92063 17.4636 8.65134C17.7221 8.38205 18.0728 8.23076 18.4385 8.23076C18.8041 8.23076 19.1548 8.38205 19.4134 8.65134C19.6719 8.92063 19.8172 9.28587 19.8172 9.6667C19.8172 10.0475 19.6719 10.4128 19.4134 10.6821C19.1548 10.9514 18.8041 11.1026 18.4385 11.1026C18.0728 11.1026 17.7221 10.9514 17.4636 10.6821C17.205 10.4128 17.0598 10.0475 17.0598 9.6667ZM10.5601 13.5642C10.1945 13.5642 9.8438 13.7155 9.58524 13.9848C9.32668 14.2541 9.18142 14.6193 9.18142 15.0002C9.18142 15.381 9.32668 15.7463 9.58524 16.0155C9.8438 16.2848 10.1945 16.4361 10.5601 16.4361C10.9258 16.4361 11.2765 16.2848 11.535 16.0155C11.7936 15.7463 11.9388 15.381 11.9388 15.0002C11.9388 14.6193 11.7936 14.2541 11.535 13.9848C11.2765 13.7155 10.9258 13.5642 10.5601 13.5642ZM18.4385 18.8977C18.0728 18.8977 17.7221 19.049 17.4636 19.3183C17.205 19.5876 17.0598 19.9528 17.0598 20.3337C17.0598 20.7145 17.205 21.0797 17.4636 21.349C17.7221 21.6183 18.0728 21.7696 18.4385 21.7696C18.8041 21.7696 19.1548 21.6183 19.4134 21.349C19.6719 21.0797 19.8172 20.7145 19.8172 20.3337C19.8172 19.9528 19.6719 19.5876 19.4134 19.3183C19.1548 19.049 18.8041 18.8977 18.4385 18.8977Z"
        fill="white"
      />
    </svg>
  );
};

export const Copy = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M16 12.9V17.1C16 20.6 14.6 22 11.1 22H6.9C3.4 22 2 20.6 2 17.1V12.9C2 9.4 3.4 8 6.9 8H11.1C14.6 8 16 9.4 16 12.9Z"
        fill={color.accent}
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22 6.9V11.1C22 14.6 20.6 16 17.1 16H16V12.9C16 9.4 14.6 8 11.1 8H8V6.9C8 3.4 9.4 2 12.9 2H17.1C20.6 2 22 3.4 22 6.9Z"
        stroke={color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Refresh = ({ width = 24, height = 24 }: { width?: number; height?: number }) => {
  const { color } = useConfigApp();
  return (
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="20" cy="20" r="20" fill={"#EDF3FF"} />
      <path
        d="M19.9932 23.5178C19.6914 23.4927 19.4117 23.433 19.154 23.3387C18.8963 23.2444 18.6732 23.1187 18.4846 22.9616C18.3023 22.8044 18.1577 22.6158 18.0509 22.3958C17.944 22.1696 17.8874 21.915 17.8812 21.6321H19.2954C19.3143 21.8521 19.3803 22.0313 19.4934 22.1696C19.6066 22.3078 19.7732 22.4021 19.9932 22.4524V20.6516C19.748 20.5887 19.4997 20.5196 19.2483 20.4441C19.0032 20.3687 18.7769 20.265 18.5694 20.133C18.3683 19.9947 18.2017 19.8156 18.0697 19.5956C17.944 19.3756 17.8812 19.0896 17.8812 18.7376C17.8812 18.461 17.9314 18.2096 18.032 17.9833C18.1389 17.7507 18.2866 17.5527 18.4752 17.3893C18.6637 17.2258 18.8869 17.0938 19.1446 16.9933C19.4023 16.8927 19.6852 16.8298 19.9932 16.8047V16.041H20.5966V16.8047C21.1874 16.855 21.6652 17.0341 22.0297 17.3421C22.4006 17.6501 22.608 18.087 22.652 18.6527H21.2283C21.2094 18.4704 21.1434 18.3133 21.0303 18.1813C20.9234 18.043 20.7789 17.9456 20.5966 17.889V19.671C20.8417 19.7338 21.09 19.803 21.3414 19.8784C21.5929 19.9538 21.8223 20.0607 22.0297 20.199C22.2372 20.331 22.4037 20.507 22.5294 20.727C22.6614 20.947 22.7274 21.2298 22.7274 21.5756C22.7274 21.8144 22.6803 22.047 22.586 22.2733C22.4917 22.4996 22.3534 22.7007 22.1712 22.8767C21.9952 23.0527 21.7752 23.2004 21.5112 23.3198C21.2472 23.433 20.9423 23.5021 20.5966 23.5273V24.291H19.9932V23.5178ZM21.3603 21.6321C21.3603 21.4121 21.288 21.2424 21.1434 21.123C20.9989 20.9973 20.8166 20.8967 20.5966 20.8213V22.4713C20.8354 22.4273 21.0209 22.3298 21.1529 22.179C21.2912 22.0281 21.3603 21.8458 21.3603 21.6321ZM19.2577 18.6716C19.2577 18.8916 19.3237 19.0644 19.4557 19.1901C19.594 19.3158 19.7732 19.4164 19.9932 19.4918V17.8607C19.7732 17.8921 19.594 17.977 19.4557 18.1153C19.3237 18.2473 19.2577 18.4327 19.2577 18.6716Z"
        fill={color.primary}
      />
      <path
        d="M20.4854 10.584C25.467 10.8367 29.4287 14.9556 29.4287 20L29.416 20.4854C29.4057 20.6896 29.3884 20.8919 29.3647 21.0924C29.3157 21.5075 28.8488 21.7089 28.4573 21.5623C28.1574 21.4501 27.9496 21.1499 27.9835 20.8315C28.0127 20.5582 28.0283 20.2809 28.0283 20C28.0283 15.566 24.434 11.9718 20 11.9717C15.5659 11.9717 11.9717 15.5659 11.9717 20C11.9718 24.434 15.566 28.0283 20 28.0283C20.9882 28.0283 21.9338 27.8479 22.8077 27.5207C23.1079 27.4083 23.4493 27.5305 23.6334 27.7929C23.8732 28.1348 23.7979 28.639 23.4086 28.7904C22.3515 29.2016 21.2025 29.4287 20 29.4287L19.5146 29.416C14.6937 29.1716 10.8285 25.3063 10.584 20.4854L10.5713 20C10.5713 14.7927 14.7927 10.5713 20 10.5713L20.4854 10.584Z"
        fill={color.primary}
      />
      <path
        d="M26.8992 25.967C26.8992 26.1928 26.901 26.4187 26.8986 26.6448C26.8959 26.8872 26.7196 27.0685 26.4882 27.0718C26.2511 27.0753 26.0675 26.8916 26.066 26.6419C26.0636 26.1999 26.061 25.758 26.0675 25.316C26.0695 25.173 26.0179 25.1276 25.8782 25.1297C25.4462 25.1358 25.0137 25.1226 24.5821 25.1353C24.3126 25.1429 24.1289 24.9303 24.1251 24.7207C24.1215 24.5134 24.302 24.2855 24.585 24.2941C25.0166 24.3073 25.4492 24.2932 25.8811 24.3C26.0241 24.3023 26.0695 24.2504 26.0675 24.1104C26.061 23.6785 26.0742 23.2459 26.0618 22.814C26.0539 22.5445 26.2668 22.3608 26.4764 22.357C26.6834 22.3534 26.9113 22.5339 26.9027 22.8169C26.8895 23.2486 26.9036 23.6811 26.8968 24.1133C26.8945 24.2563 26.9464 24.3017 27.0861 24.2997C27.5181 24.2932 27.9506 24.3065 28.3822 24.2941C28.6517 24.2861 28.8354 24.499 28.8392 24.7086C28.8428 24.9156 28.6626 25.1438 28.3793 25.135C27.9476 25.1217 27.5151 25.1358 27.0832 25.1291C26.9402 25.1267 26.8915 25.1786 26.8968 25.3186C26.9054 25.5345 26.8992 25.7509 26.8992 25.967Z"
        fill={color.primary}
        stroke={color.primary}
        strokeWidth="0.4"
        strokeMiterlimit="10"
      />
    </svg>
  );
};

export const Clipboard = ({ color = "currentColor" }) => (
  <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M4 11.0002C3.63333 11.0002 3.31944 10.8696 3.05833 10.6085C2.79722 10.3474 2.66667 10.0335 2.66667 9.66683V1.66683C2.66667 1.30016 2.79722 0.986274 3.05833 0.725163C3.31944 0.464052 3.63333 0.333496 4 0.333496H10C10.3667 0.333496 10.6806 0.464052 10.9417 0.725163C11.2028 0.986274 11.3333 1.30016 11.3333 1.66683V9.66683C11.3333 10.0335 11.2028 10.3474 10.9417 10.6085C10.6806 10.8696 10.3667 11.0002 10 11.0002H4ZM4 9.66683H10V1.66683H4V9.66683ZM1.33333 13.6668C0.966667 13.6668 0.652778 13.5363 0.391667 13.2752C0.130556 13.0141 0 12.7002 0 12.3335V3.00016H1.33333V12.3335H8.66667V13.6668H1.33333Z"
      fill="#BFBFBF"
    />
  </svg>
);

export const Eye = ({ color = "currentColor" }) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
    <circle cx="12" cy="12" r="3" />
  </svg>
);

export const EyeSlash = ({ color = "currentColor" }) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
    <line x1="1" y1="1" x2="23" y2="23" />
  </svg>
);

export const Logo = ({ color = "currentColor" }) => (
  <svg width="70" height="14" viewBox="0 0 70 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.03623 0.0522096C13.1676 -0.757356 16.8592 8.04929 11.3307 12.4998C7.00637 15.9813 0.398136 13.0262 0.0159283 7.50713C-0.231567 3.92071 2.43345 0.461187 6.03623 0.0522096ZM10.9684 2.74936H4.02389L3.03182 4.95155H9.97631L10.9684 2.74936ZM10.6551 5.89534H4.80711L3.34928 9.0948L4.70163 11.1921H8.25533L9.1931 9.04132H5.74696L6.23464 8.09333L9.71629 8.04719L10.6541 5.89534H10.6551Z"
      fill="#969696"
    />
    <path
      d="M64.6779 0.337891C64.729 0.375642 64.5755 0.473168 64.5755 0.49519V4.42766C64.992 4.28714 65.2151 3.87712 65.6316 3.67473C66.6283 3.19024 68.1449 3.27938 68.9778 4.05853C69.4956 4.54197 69.9991 5.77204 69.9991 6.47255V11.0342H68.2595V6.78715C68.2595 6.72108 68.0753 6.0835 68.0292 5.97444C67.5135 4.76743 65.8322 4.74855 65.0882 5.73849C64.8743 6.02267 64.5766 6.76408 64.5766 7.10175V11.0342H62.8369V0.337891H64.6789H64.6779Z"
      fill="#969696"
    />
    <path
      d="M54.1381 6.20891L48.9192 8.62398C48.8076 8.78128 49.3357 9.19864 49.4605 9.2731C50.8358 10.1015 52.377 8.94067 53.1762 7.79658L54.5044 8.30309C54.2547 9.56882 52.8446 10.6038 51.678 10.9772C46.7641 12.5523 44.2876 5.2043 49.3009 3.4027C51.7231 2.53231 53.3716 3.94171 54.1391 6.20891H54.1381ZM50.2219 4.7691C49.3121 4.91277 48.3318 5.74226 48.2264 6.71227C48.2028 6.93249 48.1394 7.11286 48.3584 7.25967L51.9861 5.52938C51.4672 4.90858 51.0538 4.63697 50.2219 4.76806V4.7691Z"
      fill="#969696"
    />
    <path
      d="M58.6189 3.30644C59.8121 3.18165 60.9623 3.47842 61.8107 4.37712L60.7167 5.46668C60.5254 5.51597 60.1989 5.19823 59.9677 5.11328C57.8832 4.34776 56.2858 6.5856 57.306 8.47004C58.009 9.76933 59.7476 9.7169 60.8252 8.94928L61.9109 10.0472C61.4361 10.7005 60.3852 11.1557 59.5993 11.2333C54.2289 11.7649 53.6978 3.82029 58.6189 3.30749V3.30644Z"
      fill="#969696"
    />
    <path
      d="M43.5977 1.38672V3.48404H45.6444V5.05703H43.5977V8.6749C43.5977 8.73677 43.784 9.07864 43.8474 9.15309C44.4614 9.86828 45.4786 9.1856 45.542 9.25167L45.9524 10.8236C44.6927 11.1812 42.8691 11.3322 42.1845 9.91442C42.0955 9.72881 41.8591 9.04193 41.8591 8.88464V5.05703H40.5288V3.48404H41.8591V1.64888C42.3084 1.38672 43.0595 1.44544 43.5988 1.38672H43.5977Z"
      fill="#969696"
    />
    <path
      d="M22.7151 5.9917L17.5033 8.41305C18.5092 10.1255 21.088 9.01394 21.701 7.46821L23.2339 7.99359C22.8164 9.44074 21.2712 10.5691 19.8774 10.8973C15.6859 11.8852 13.2699 6.02211 16.8975 3.64899C19.4364 1.98792 21.9404 3.14459 22.7151 5.9917ZM16.8893 7.04875L20.3041 5.46317C20.6838 5.29749 20.2121 4.88746 19.9941 4.75743C18.6709 3.96674 16.5475 5.43906 16.8893 7.04875Z"
      fill="#D0D0D0"
    />
    <path
      d="M35.5948 3.30438C40.958 2.8587 41.2885 11.1851 35.9632 11.2322C31.0584 11.2752 30.7668 3.70602 35.5948 3.30438ZM35.8015 4.97595C33.2074 5.16366 33.2156 9.90045 36.3746 9.45163C38.7282 9.1171 38.4038 4.78824 35.8015 4.97595Z"
      fill="#D0D0D0"
    />
    <path
      d="M25.2802 3.48242L27.4783 9.04137L29.6293 3.48242H31.5225L28.4382 11.0191L26.5543 11.0401L23.3359 3.48242H25.2802Z"
      fill="#D0D0D0"
    />
  </svg>
);
export const Coin = () => {
  const { color } = useConfigApp();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 512 512">
      <path
        fill="#EFB832"
        d="M256 512c-141.159 0-256-114.841-256-256S114.841 0 256 0s256 114.841 256 256-114.841 256-256 256z"
      />
      <path
        opacity=".2"
        fill="#FFF"
        d="M331.858 278.511c-2.639-7.713-6.907-14.208-12.786-19.487-4.679-4.679-10.266-8.329-16.762-10.969-6.496-2.639-14.723-4.679-24.68-6.102l-1.92-.291-23.978-3.668c-4.662-.6-8.57-1.628-11.723-3.051-1.32-.583-2.554-1.234-3.719-1.936-1.611-.977-3.051-2.057-4.353-3.24-2.245-2.228-3.805-4.662-4.73-7.301-.908-2.639-1.371-5.382-1.371-8.227 0-7.524 2.691-13.968 8.073-19.35.737-.737 1.525-1.423 2.382-2.04 5.348-4.028 12.94-6.033 22.761-6.033 5.33 0 10.883.48 16.659 1.457 1.936.326 3.891.703 5.879 1.131 7.918 1.731 15.237 5.844 21.938 12.34l26.806-26.514c-9.341-9.135-19.59-15.631-30.765-19.487-7.164-2.468-15.133-4.165-23.875-5.039-4.884-.514-10.026-.754-15.425-.754-8.638 0-16.642.874-23.978 2.639-2.657.634-5.227 1.371-7.713 2.245-9.341 3.24-17.259 7.764-23.755 13.54-6.496 5.793-11.484 12.7-14.928 20.721-3.462 8.021-5.176 16.917-5.176 26.669 0 18.476 5.176 32.702 15.528 42.642 4.885 4.679 10.66 8.484 17.362 11.432 5.296 2.314 11.5 4.165 18.681 5.519 1.937.377 3.925.72 5.999 1.028l25.897 3.959c2.913.428 5.416.891 7.524 1.371 1.868.446 3.428.891 4.662 1.371 2.639 1.011 5.073 2.537 7.318 4.559 4.456 4.473 6.701 10.472 6.701 17.979 0 8.741-3.256 15.442-9.752 20.104-2.502 1.817-5.484 3.274-8.929 4.37-5.484 1.765-12.151 2.639-20.018 2.639-6.684 0-13.163-.6-19.402-1.8-3.034-.583-5.999-1.303-8.929-2.159-8.929-2.639-16.848-7.404-23.755-14.311l-27.423 27.405c10.558 10.78 22.281 18.236 35.187 22.401 7.678 2.485 15.991 4.216 24.938 5.21 6.067.686 12.443 1.029 19.093 1.029 7.061 0 13.831-.566 20.31-1.714 3.942-.686 7.781-1.594 11.517-2.708 9.855-2.931 18.373-7.198 25.589-12.786 7.216-5.587 12.854-12.443 16.917-20.567 4.062-8.124 6.085-17.465 6.085-28.022 0-8.324-1.32-16.048-3.959-23.777z"
      />
      <path
        opacity=".5"
        fill="#AE8132"
        d="M256 472.633C136.548 472.633 39.367 375.452 39.367 256S136.548 39.367 256 39.367 472.633 136.548 472.633 256 375.452 472.633 256 472.633zm0-378.029c-88.994 0-161.396 72.402-161.396 161.396S167.006 417.396 256 417.396 417.396 345.006 417.396 256 344.994 94.604 256 94.604z"
      />
    </svg>
  );
};
export const FilterIcon = ({ fillColor = "#ffffff" }) => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M22 3H2L10 12.46V19L14 21V12.46L22 3Z"
      stroke={fillColor}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
);
