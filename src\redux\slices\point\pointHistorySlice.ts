import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";

interface PointHistoryState {
  incomeHistory: any[];
  spendingHistory: any[];
  isLoading: boolean;
  error: string | null;
}

const initialState: PointHistoryState = {
  incomeHistory: [],
  spendingHistory: [],
  isLoading: false,
  error: null,
};

export const getIncomeHistory = createAsyncThunk(
  "pointHistory/getIncomeHistory",
  async (params: { shopId: string; skip?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const { shopId, skip = 0, limit = 20 } = params;
      const response: any = await request(
        "get",
        `/api/user/me/incomehistory?shopId=${shopId}&skip=${skip}&limit=${limit}`
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Error fetching income history");
    }
  }
);

export const getSpendingHistory = createAsyncThunk(
  "pointHistory/getSpendingHistory",
  async (params: { shopId: string; skip?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const { shopId, skip = 0, limit = 20 } = params;
      const response: any = await request(
        "get",
        `/api/user/me/spendinghistory?shopId=${shopId}&skip=${skip}&limit=${limit}`
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Error fetching spending history");
    }
  }
);

const pointHistorySlice = createSlice({
  name: "pointHistory",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getIncomeHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getIncomeHistory.fulfilled, (state, action: PayloadAction<any>) => {
        state.incomeHistory = action.payload?.data || [];
        state.isLoading = false;
      })
      .addCase(getIncomeHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(getSpendingHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSpendingHistory.fulfilled, (state, action: PayloadAction<any>) => {
        state.spendingHistory = action.payload?.data || [];
        state.isLoading = false;
      })
      .addCase(getSpendingHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export default pointHistorySlice.reducer;
