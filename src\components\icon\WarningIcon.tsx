import React from "react";
export interface IconCustomProps {
  primaryColor?: string;
  secondaryColor?: string;
  className?: string;
}
const WarningIcon: React.FC<IconCustomProps> = ({
  primaryColor,
  secondaryColor,
  className,
}) => {
  return (
    <svg
      width="74"
      height="74"
      viewBox="0 0 74 74"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="37" cy="37" r="37" fill="#FFB42A" fillOpacity="0.4" />
      <path
        d="M36.9998 16.1665C48.5061 16.1665 57.8332 25.4936 57.8332 36.9998C57.8332 48.5061 48.5061 57.8332 36.9998 57.8332C25.4936 57.8332 16.1665 48.5061 16.1665 36.9998C16.1665 25.4936 25.4936 16.1665 36.9998 16.1665ZM36.9998 43.2498C36.4473 43.2498 35.9174 43.4693 35.5267 43.86C35.136 44.2507 34.9165 44.7806 34.9165 45.3332C34.9165 45.8857 35.136 46.4156 35.5267 46.8063C35.9174 47.197 36.4473 47.4165 36.9998 47.4165C37.5524 47.4165 38.0823 47.197 38.473 46.8063C38.8637 46.4156 39.0832 45.8857 39.0832 45.3332C39.0832 44.7806 38.8637 44.2507 38.473 43.86C38.0823 43.4693 37.5524 43.2498 36.9998 43.2498ZM36.9998 24.4998C36.4896 24.4999 35.9971 24.6872 35.6157 25.0263C35.2344 25.3654 34.9908 25.8326 34.9311 26.3394L34.9165 26.5832V39.0832C34.9171 39.6142 35.1204 40.1249 35.4849 40.511C35.8495 40.8971 36.3477 41.1295 36.8777 41.1606C37.4078 41.1917 37.9298 41.0193 38.337 40.6785C38.7442 40.3376 39.0059 39.8542 39.0686 39.3269L39.0832 39.0832V26.5832C39.0832 26.0306 38.8637 25.5007 38.473 25.11C38.0823 24.7193 37.5524 24.4998 36.9998 24.4998Z"
        fill={primaryColor}
      />
    </svg>
  );
};

export default WarningIcon;
