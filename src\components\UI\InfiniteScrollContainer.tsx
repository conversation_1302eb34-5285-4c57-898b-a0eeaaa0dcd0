import React from "react";
import { Box, CircularProgress, Typography, Button } from "@mui/material";
import { COLORS } from "@/constants/themes";

interface InfiniteScrollContainerProps<T> {
  data: T[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  error: Error | null;
  observerRef: React.RefObject<HTMLDivElement>;
  onRetry?: () => void;
  renderItem: (item: T, index: number) => React.ReactNode;
  renderEmpty?: () => React.ReactNode;
  renderError?: (error: Error, onRetry?: () => void) => React.ReactNode;
  renderLoader?: () => React.ReactNode;
  renderLoadMoreLoader?: () => React.ReactNode;
  className?: string;
  containerProps?: any;
  loadMoreThreshold?: number;
}

function InfiniteScrollContainer<T>({
  data,
  isLoading,
  isLoadingMore,
  hasMore,
  error,
  observerRef,
  onRetry,
  renderItem,
  renderEmpty,
  renderError,
  renderLoader,
  renderLoadMoreLoader,
  className,
  containerProps,
}: InfiniteScrollContainerProps<T>) {
  // Default empty state
  const defaultEmpty = () => (
    <Box
      sx={{
        textAlign: "center",
        py: 4,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: 2,
      }}
    >
      <Typography color={COLORS.neutral6} fontWeight={500}>
        Không có dữ liệu
      </Typography>
    </Box>
  );

  // Default error state
  const defaultError = (error: Error, onRetry?: () => void) => (
    <Box
      sx={{
        textAlign: "center",
        py: 4,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: 2,
      }}
    >
      {/* <Typography color="error" fontWeight={500}>
        Có lỗi xảy ra: {error.message}
      </Typography>
      {onRetry && (
        <Button variant="outlined" onClick={onRetry} size="small">
          Thử lại
        </Button>
      )} */}
    </Box>
  );

  // Default loading state
  const defaultLoader = () => (
    <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", py: 4 }}>
      <CircularProgress />
    </Box>
  );

  // Default load more loading state
  const defaultLoadMoreLoader = () => (
    <Box sx={{ display: "flex", justifyContent: "center", py: 2 }}>
      <CircularProgress size={24} />
    </Box>
  );

  // Show initial loading
  if (isLoading && data.length === 0) {
    return renderLoader ? renderLoader() : defaultLoader();
  }

  // Show error state
  if (error && data.length === 0) {
    return renderError ? renderError(error, onRetry) : defaultError(error, onRetry);
  }

  // Show empty state
  if (!isLoading && data.length === 0) {
    return renderEmpty ? renderEmpty() : defaultEmpty();
  }

  return (
    <Box className={className} {...containerProps}>
      {/* Render items */}
      {data.map((item, index) => renderItem(item, index))}

      {/* Load more trigger */}
      {hasMore && (
        <div
          ref={observerRef}
          style={{
            height: "20px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {isLoadingMore &&
            (renderLoadMoreLoader ? renderLoadMoreLoader() : defaultLoadMoreLoader())}
        </div>
      )}

      {/* End message */}
      {!hasMore && data.length > 0 && (
        <Box sx={{ textAlign: "center", py: 2 }}>
          <Typography variant="body2" color={COLORS.neutral6}>
            Đã hiển thị tất cả
          </Typography>
        </Box>
      )}

      {/* Error state for load more */}
      {error && data.length > 0 && (
        <Box sx={{ textAlign: "center", py: 2 }}>
          <Typography variant="body2" color="error" sx={{ mb: 1 }}>
            Lỗi khi tải thêm dữ liệu
          </Typography>
          {onRetry && (
            <Button variant="text" onClick={onRetry} size="small">
              Thử lại
            </Button>
          )}
        </Box>
      )}
    </Box>
  );
}

export default InfiniteScrollContainer;
