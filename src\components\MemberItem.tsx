import { Box, Stack, Typography } from "@mui/material";
import dayjs from "dayjs";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { LevelName } from "../constants/Const";
import { COLORS } from "../constants/themes";

export default function MemberItem({ item }) {
  const { user } = useSelector((state: RootState) => state.auth);

  return (
    <Stack gap={1}>
      <Stack
        style={{
          background: "#fff",
          marginTop: "30px",
          border: "1px solid #D9D9D9",
        }}
        padding={2}
        borderRadius={4}
        marginBlock={2}
      >
        <Stack
          direction="row"
          alignItems={"center"}
          justifyContent={"space-between"}
          gap={1}
        >
          <Typography
            style={{
              fontWeight: 700,
              fontSize: 18,
              color: "#143374",
            }}
          >
            {item.name}
          </Typography>
          <Typography
            style={{
              fontWeight: 400,
              fontSize: 18,
              color: "#969595",
              marginTop: 8,
              marginBottom: 8,
            }}
          >
            {item.phone}
          </Typography>
        </Stack>
        <Box style={{ display: "flex", justifyContent: "space-between" }}>
          <Typography
            style={{ fontSize: 18, fontWeight: 400, color: "#96959" }}
          >
            Hệ cấp:{" "}
            <span
              style={{
                fontSize: "16",
                color: "#FF9900",
                fontWeight: 700,
              }}
            >
              F{item.fLevel}
            </span>
          </Typography>
          <Typography
            style={{ fontSize: 18, fontWeight: 400, color: "#96959" }}
          >
            Hạng:{" "}
            <span
              style={{
                fontSize: "16",
                color: COLORS.primary1,
                fontWeight: 700,
              }}
            >
              {LevelName[item.level]}
            </span>
          </Typography>
        </Box>
        <Typography
          style={{
            fontWeight: 400,
            fontSize: 18,
            color: "#969595",
            marginTop: 8,
            marginBottom: 8,
          }}
        >
          Ngày gia nhập: {dayjs(item.createdAt).format("DD/MM/YYYY")}
        </Typography>
      </Stack>
    </Stack>
  );
}
