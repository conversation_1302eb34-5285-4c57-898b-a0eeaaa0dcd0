import { ITaxInvoiceConfiguration } from "./taxInvoice";

export interface IOrder {
  branchId: string;
  orderId: string;
  orderNo: string;
  transportId: string | null;
  paymentId: string | null;
  transactionId: string;
  partnerId: string;
  userId: string;
  userFullName: string;
  userPhone: string;
  userAddressId: string;
  userProvinceId: string;
  userProvinceName: string;
  userDistrictId: string;
  userDistrictName: string;
  userWardId: string;
  userWardName: string;
  userAddress: string;
  userShippingFullName: string;
  userShippingPhoneNumber: string;
  shopId: string;
  shopName: string;
  shopProvinceId: string | null;
  shopProvinceName: string | null;
  shopDistrictId: string | null;
  shopDistrictName: string | null;
  shopWardId: string | null;
  shopWardName: string | null;
  shopAddress: string | null;
  notes: null;
  orderOrigin: null;
  listItems: any[];
  voucherPromotionIds: any[];
  voucherPromotion: any[];
  voucherTransportIds: any[];
  voucherTransport: any[];
  price: number;
  exchangePoints: number;
  pointPrice: number;
  voucherPromotionPrice: number;
  voucherTransportPrice: number;
  transportPrice: number;
  transportService: string;
  statusTransport: string;
  statusDelivery: string;
  statusOrder: string;
  statusPay: string;
  typePay: string;
  userShippingAddress: IShippingAddress;
  created: string;
  updated: string;
  taxInvoice?: ITaxInvoiceConfiguration | null;
  totalTaxAmount?: number;
  totalAfterTax?: number;
}
interface IShippingAddress {
  address: string;
  created: string;
  districtId: string;
  districtName: string;
  fullName: string;
  id: string;
  isDefault: boolean;
  phoneNumber: string;
  provinceId: string;
  provinceName: string;
  shippingAddressId: string;
  updated: string;
  userId: string;
  wardId: string;
  wardName: string;
}

export interface IOrderSearchCondition {
  search?: string;
  orderStatus: string;
}
