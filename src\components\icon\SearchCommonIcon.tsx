import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";

export interface IconCustomProps {
  fillColor?: string;
  strokeColor?: string;
  className?: string;
}

const SearchCommonIcon: React.FC<IconCustomProps> = ({
  fillColor,
  strokeColor,
}) => {
  const appConfig = useConfigApp();
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill={fillColor || "none"}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.4791 20.3125H7.81246C7.16663 20.3125 6.59371 20.2917 6.08329 20.2187C3.34371 19.9167 2.60413 18.625 2.60413 15.1042V9.89583C2.60413 6.375 3.34371 5.08333 6.08329 4.78125C6.59371 4.70833 7.16663 4.6875 7.81246 4.6875H11.4166"
        stroke={strokeColor || appConfig.color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.6459 4.6875H17.1875C17.8334 4.6875 18.4063 4.70833 18.9167 4.78125C21.6563 5.08333 22.3959 6.375 22.3959 9.89583V15.1042C22.3959 18.625 21.6563 19.9167 18.9167 20.2187C18.4063 20.2917 17.8334 20.3125 17.1875 20.3125H15.6459"
        stroke={strokeColor || appConfig.color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.625 2.0835V22.9168"
        stroke={strokeColor || appConfig.color.primary}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.5567 12.4998H11.5661"
        stroke={strokeColor || appConfig.color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.39011 12.4998H7.39947"
        stroke={strokeColor || appConfig.color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SearchCommonIcon;
