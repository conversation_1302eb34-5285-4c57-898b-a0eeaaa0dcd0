import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IProduct, IProductSearchCondition } from "../../../types/product";

// Tạo một cache để lưu trữ dữ liệu sản phẩm theo danh mục
interface ProductCache {
  [key: string]: {
    products: IProduct[];
    pagination: {
      skip: number;
      limit: number;
      total: number;
    };
    timestamp: number;
    isEmpty?: boolean; // Đ<PERSON>h dấu danh mục không có sản phẩm
  };
}

interface productState {
  productList: IProduct[];
  hotProductList: IProduct[];
  productSameCategory: IProduct[];
  isLoading: boolean;
  isInitialLoading: boolean;
  searchCondition: IProductSearchCondition;
  error: string | null;
  list: {
    products: IProduct[];
    pagination: {
      skip: number;
      limit: number;
      total: number;
    };
  };
  sortBy?: {
    field: string;
    order: "default" | "asc" | "desc";
  };
  // Thêm cache vào state
  productCache: ProductCache;
}

const initialState: productState = {
  productList: [],
  hotProductList: [],
  productSameCategory: [],
  isLoading: true,
  isInitialLoading: true,
  error: null,
  list: {
    products: [],
    pagination: {
      skip: 0,
      limit: 100, // Tăng giới hạn mặc định lên 100
      total: 0,
    },
  },
  searchCondition: {},
  sortBy: {
    field: "price",
    order: "default",
  },
  // Khởi tạo cache rỗng
  productCache: {},
};

let requestPath = "/api/user/itemsuser/listitems";

// Tạo chuỗi key cho cache dựa trên điều kiện tìm kiếm
const getCacheKey = (condition: IProductSearchCondition) => {
  return `${condition.categoryId || ""}_${condition.parentId || ""}_${condition.search || ""}`;
};

export const getProductList = createAsyncThunk(
  "productList/getProductList",
  async (categoryId?: string) => {
    const response: any = await request("get", requestPath);
    return response;
  }
);

export const getProductByCategory = createAsyncThunk(
  "productList/getProductByCategory",
  async (data: any) => {
    const response: any = await request(
      "get",
      requestPath + `?ShopId=${data.shopId}&CategoryId=${data.categoryId}`
    );
    return response;
  }
);

export const getHotProductList = createAsyncThunk(
  "productList/getHotProductList",
  async (categoryId: string) => {
    const response: any = await request("get", requestPath, {
      categoryId,
      skip: 0,
      limit: 10,
    });
    return response;
  }
);

export const getProductListByCategory = createAsyncThunk(
  "productList/getProductListByCategory",
  async (conditon?: IProductSearchCondition, { getState, dispatch }) => {
    // Lấy state hiện tại
    const state = getState() as any;
    const currentState = state.productList;

    // Kiểm tra xem đây có phải là request phân trang không
    const isPaginationRequest = conditon?.skip && conditon.skip > 0;

    // Nếu là request phân trang (skip > 0), luôn gọi API
    if (isPaginationRequest) {
      try {
        // Tạo một promise với timeout
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Request timeout")), 10000);
        });

        // Chuẩn bị các tham số cho API request, bao gồm các tham số lọc và sắp xếp
        const apiParams = {
          ...conditon,
          skip: conditon?.skip || 0,
          limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100 để tải nhiều sản phẩm hơn
        };

        // Thêm tham số sắp xếp nếu có
        if (
          currentState.sortBy &&
          currentState.sortBy.field &&
          currentState.sortBy.order !== "default"
        ) {
          apiParams.sortField = currentState.sortBy.field;
          apiParams.sortOrder = currentState.sortBy.order;
        }

        // Tạo promise cho API request với tham số đã bao gồm sắp xếp và lọc
        const apiPromise = request("get", requestPath, apiParams);

        // Chạy cả hai promise, lấy kết quả từ cái nào hoàn thành trước
        const response: any = await Promise.race([apiPromise, timeoutPromise]);

        // Đánh dấu là request phân trang
        response.isPaginationRequest = true;
        return response;
      } catch (error) {
        // Nếu có lỗi, trả về một response trống
        return {
          data: [],
          skip: conditon?.skip || 0,
          limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100
          total: 0,
          isPaginationRequest: true,
          error: true,
        };
      }
    }

    // Nếu là request đầu tiên và có forceReload, luôn gọi API để tải dữ liệu mới
    if (conditon?.forceReload) {
      try {
        // Tạo một promise với timeout
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Request timeout")), 10000);
        });

        // Chuẩn bị các tham số cho API request
        const apiParams = {
          ...conditon,
          skip: conditon?.skip || 0,
          limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100
        };

        // Tạo promise cho API request
        const apiPromise = request("get", requestPath, apiParams);

        // Chạy cả hai promise, lấy kết quả từ cái nào hoàn thành trước
        const response: any = await Promise.race([apiPromise, timeoutPromise]);

        // Đánh dấu là request tìm kiếm mới
        response.isNewSearch = true;

        // Kiểm tra nếu không có sản phẩm, đánh dấu danh mục này là rỗng
        if (!response.data || response.data.length === 0) {
          response.isEmpty = true;
        }

        return response;
      } catch (error) {
        // Nếu có lỗi, trả về một response trống và đánh dấu là rỗng
        return {
          data: [],
          skip: 0,
          limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100
          total: 0,
          isNewSearch: true,
          error: true,
          isEmpty: true,
        };
      }
    }

    // Tạo key cho cache
    const cacheKey = getCacheKey(conditon || {});

    // Kiểm tra xem có dữ liệu trong cache không và chưa quá thời gian cache (5 phút)
    const cachedData = currentState.productCache[cacheKey];
    const now = Date.now();
    const cacheExpiration = 5 * 60 * 1000; // 5 phút

    // Nếu có cache và cache còn hạn
    if (cachedData && !conditon?.forceReload && now - cachedData.timestamp < cacheExpiration) {
      // Nếu đã biết danh mục này không có sản phẩm, trả về ngay lập tức
      if (cachedData.isEmpty) {
        return {
          data: [],
          skip: 0,
          limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100
          total: 0,
          fromCache: true,
          isEmpty: true,
        };
      }

      // Nếu có sản phẩm trong cache, trả về dữ liệu
      if (cachedData.products && cachedData.products.length > 0) {
        return {
          data: cachedData.products,
          skip: 0,
          limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100
          total: cachedData.pagination.total,
          fromCache: true,
        };
      }
    }

    // Tạo bản sao của điều kiện tìm kiếm để so sánh, loại bỏ các trường không liên quan
    const currentConditionForCompare = {
      search: currentState.searchCondition.search || "",
      categoryId: currentState.searchCondition.categoryId || null,
      parentId: currentState.searchCondition.parentId || undefined,
      shopId: currentState.searchCondition.shopId,
    };

    const newConditionForCompare = {
      search: conditon?.search || "",
      categoryId: conditon?.categoryId || null,
      parentId: conditon?.parentId || undefined,
      shopId: conditon?.shopId,
    };

    const conditionsEqual =
      JSON.stringify(currentConditionForCompare) === JSON.stringify(newConditionForCompare);

    // Nếu danh sách đã có sẵn và điều kiện tìm kiếm không thay đổi, không cần gọi API
    if (currentState.list.products.length > 0 && conditionsEqual && !conditon?.forceReload) {
      // Trả về dữ liệu hiện có
      return {
        data: currentState.list.products,
        skip: 0,
        limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100
        total: currentState.list.pagination.total,
        fromCache: true,
      };
    }

    // Nếu không, gọi API để lấy dữ liệu mới
    try {
      // Tạo một promise với timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Request timeout")), 10000);
      });

      // Chuẩn bị các tham số cho API request, thêm các tham số lọc và sắp xếp nếu server API hỗ trợ
      const apiParams = {
        ...conditon,
        skip: conditon?.skip || 0,
        limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100
      };

      // Bỏ comment dòng dưới nếu server API hỗ trợ lọc và sắp xếp
      /*
      // Thêm tham số sắp xếp nếu có
      if (currentState.sortBy && currentState.sortBy.field && currentState.sortBy.order !== "default") {
        apiParams.sortField = currentState.sortBy.field;
        apiParams.sortOrder = currentState.sortBy.order;
      }
      */

      // Tạo promise cho API request
      const apiPromise = request("get", requestPath, apiParams);

      // Chạy cả hai promise, lấy kết quả từ cái nào hoàn thành trước
      const response: any = await Promise.race([apiPromise, timeoutPromise]);

      // Đánh dấu là request tìm kiếm mới
      response.isNewSearch = true;

      // Kiểm tra nếu không có sản phẩm, đánh dấu danh mục này là rỗng
      if (!response.data || response.data.length === 0) {
        response.isEmpty = true;
      }

      return response;
    } catch (error) {
      // Nếu có lỗi, trả về một response trống và đánh dấu là rỗng
      return {
        data: [],
        skip: 0,
        limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100
        total: 0,
        isNewSearch: true,
        error: true,
        isEmpty: true,
      };
    }
  }
);

export const getProductListSameCategory = createAsyncThunk(
  "productList/getProductListSameCategory",
  async (conditon?: IProductSearchCondition) => {
    const response: any = await request("get", requestPath, {
      ...conditon,
      skip: conditon?.skip || 0,
      limit: conditon?.limit || 100, // Tăng giới hạn mặc định lên 100
    });
    return response;
  }
);

export const productListSlice = createSlice({
  name: "productList",
  initialState,
  reducers: {
    setSearchCondition: (state, action: PayloadAction<IProductSearchCondition>) => {
      state.searchCondition = action.payload;
    },
    setSortBy: (
      state,
      action: PayloadAction<{
        field: string;
        order: "default" | "asc" | "desc";
      }>
    ) => {
      state.sortBy = action.payload;
    },
    resetSortBy: (state) => {
      state.sortBy = {
        field: "price",
        order: "default",
      };
    },
    clearSearchCondition: (state) => {
      state.searchCondition = {};
    },
    clearProductSameCategory: (state) => {
      // keep category id for highlight
      state.productSameCategory = [];
    },
    // Thêm action để xóa cache
    clearProductCache: (state) => {
      state.productCache = {};
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getProductList.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProductList.fulfilled, (state, action) => {
        state.isLoading = false;
        state.productList = action.payload.data;
      })
      .addCase(getProductList.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch product list";
      })
      .addCase(getProductByCategory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProductByCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.productList = action.payload.data;
      })
      .addCase(getProductByCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch product by category";
      })
      .addCase(getProductListByCategory.pending, (state, action) => {
        // Chỉ đặt trạng thái loading ban đầu nếu đây là request đầu tiên (skip = 0)
        const isPaginationRequest = action.meta.arg.skip && action.meta.arg.skip > 0;

        if (!isPaginationRequest) {
          state.isInitialLoading = true;
        }

        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProductListByCategory.fulfilled, (state, action) => {
        const { data, skip, limit, total, fromCache, isEmpty, isPaginationRequest, isNewSearch } =
          action.payload;

        state.isLoading = false;
        state.isInitialLoading = false;

        // Nếu là request phân trang (skip > 0), thêm sản phẩm mới vào danh sách hiện có
        if (isPaginationRequest) {
          state.list = {
            products: [...state.list.products, ...data],
            pagination: {
              skip,
              limit,
              total,
            },
          };
        }
        // Nếu là tìm kiếm mới hoặc không phải phân trang, thay thế toàn bộ danh sách
        else {
          state.list = {
            products: data,
            pagination: {
              skip,
              limit,
              total,
            },
          };
        }

        // Cập nhật cache nếu đây là dữ liệu mới (không phải từ cache)
        if (!fromCache && !isEmpty) {
          const cacheKey = getCacheKey(state.searchCondition);
          state.productCache[cacheKey] = {
            products: state.list.products,
            pagination: state.list.pagination,
            timestamp: Date.now(),
          };
        }

        // Đánh dấu danh mục rỗng vào cache nếu cần
        if (isEmpty && !fromCache) {
          const cacheKey = getCacheKey(state.searchCondition);
          state.productCache[cacheKey] = {
            products: [],
            pagination: {
              skip: 0,
              limit,
              total: 0,
            },
            timestamp: Date.now(),
            isEmpty: true,
          };
        }
      })
      .addCase(getProductListByCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.isInitialLoading = false;
        state.error = action.error.message || "Failed to fetch product list by category";
      })
      .addCase(getHotProductList.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getHotProductList.fulfilled, (state, action) => {
        state.isLoading = false;
        state.hotProductList = action.payload.data;
      })
      .addCase(getHotProductList.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch hot product list";
      })
      .addCase(getProductListSameCategory.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getProductListSameCategory.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.productSameCategory = payload.data;

          state.isLoading = false;
        }
      )
      .addCase(getProductListSameCategory.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export const {
  setSearchCondition,
  clearSearchCondition,
  clearProductSameCategory,
  setSortBy,
  resetSortBy,
  clearProductCache,
} = productListSlice.actions;

export default productListSlice.reducer;
