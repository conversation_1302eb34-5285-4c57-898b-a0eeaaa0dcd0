import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>ack,
  TextField,
  Checkbox,
  FormControlLabel,
  Typography,
  InputAdornment,
  IconButton,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { Page } from "zmp-ui";
import { AppDispatch, RootState } from "../../redux/store";
import { useDispatch, useSelector } from "react-redux";
import * as Yup from "yup";
import { PhoneRegex } from "../../constants/Const";
import { yupResolver } from "@hookform/resolvers/yup";
import { registerForWeb, getUser } from "../../redux/slices/authen/authSlice";
import { showToast } from "../../utils/common";
import { Router } from "../../constants/Route";
import { useSearchParams } from "react-router-dom";
import { useNavigate } from "../../utils/component-util";
import FrameContainerWeb from "@/components/layout/Layout";
import { useConfigApp } from "@/hooks/useConfigApp";
import { updateShopVisit } from "@/redux/slices/affiliation/affiliationSlice";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { Visibility, VisibilityOff } from "@mui/icons-material";

const TOP_NAV_HEIGHT = 64;

export default function Register() {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  const { token } = useSelector((state: RootState) => state.auth);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const calcCartAfterLogin = useCalcCartAfterLogin();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  if (token) navigate("/");

  const validationSchema = Yup.object().shape({
    phone: Yup.string()
      .required("Vui lòng nhập số điện thoại")
      .min(10, "Tối thiểu 10 ký tự")
      .max(13, "Tối đa 13 ký tự")
      .matches(PhoneRegex, "Định dạng số điện thoại không đúng"),
    referCode: Yup.string(),
    password: Yup.string()
      .required(`Vui lòng nhập mật khẩu`)
      .min(8, `Mật khẩu phải dài từ 8 đến 20 ký tự`)
      .max(20, `Mật khẩu phải dài từ 8 đến 20 ký tự`),
    confirmPassword: Yup.string()
      .required("Vui lòng nhập lại mật khẩu")
      .oneOf([Yup.ref("password")], "Mật khẩu không khớp"),
    acceptTerms: Yup.bool().oneOf([true], "Bạn cần đồng ý với điều khoản"),
  });
  const {
    control,
    handleSubmit,
    register,
    formState: { errors, isValid, touchedFields },
    watch,
    setValue,
  } = useForm({
    resolver: yupResolver(validationSchema),
    mode: "all",
  });

  const phone = watch("phone");
  const password = watch("password");
  const confirmPassword = watch("confirmPassword");
  const acceptTerms = watch("acceptTerms");
  const isMainFieldsValid =
    !!phone &&
    !errors.phone &&
    !!password &&
    !errors.password &&
    !!confirmPassword &&
    !errors.confirmPassword &&
    acceptTerms;

  const dispatch = useDispatch<AppDispatch>();
  const [params] = useSearchParams();
  useEffect(() => {
    const code = params.get("referCode");

    code && setValue("referCode", code);
  }, [params]);

  const onSubmit = async (data) => {
    try {
      if (Object.keys(errors).length > 0) return;
      const code = params.get("referCode");
      const res = await dispatch(
        registerForWeb({
          phone: data.phone,
          password: data.password,
          referCode: data.referCode,
        })
      ).unwrap();
      if (res.idFor) {
        showToast({
          content: "Đăng ký tài khoản thành công",
          type: "success",
        });
        await dispatch(getUser());
        await calcCartAfterLogin();
        navigate(Router.homepage);
        if (code) {
          dispatch(updateShopVisit({ shopId, isByLink: true }));
        }
      } else {
        showToast({
          content: res?.detail,
          type: "error",
        });
      }
    } catch (error) {}
  };

  return (
    <FrameContainerWeb>
      <Stack
        direction="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        paddingInline={2}
        marginTop={-5}
      >
        <Box width={"100%"}>
          <Container maxWidth="lg">
            <Stack direction="row" justifyContent="center" alignItems="center">
              <span style={{ fontWeight: 700, fontSize: 24 }}>Đăng ký</span>
            </Stack>
          </Container>
        </Box>
        <Box width={"100%"} mt={5}>
          <Container maxWidth="sm">
            <form onSubmit={handleSubmit(onSubmit)}>
              <Stack gap={1} mb={5}>
                <Controller
                  name="phone"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      fullWidth
                      autoFocus
                      id="standard-required"
                      label="Số điện thoại*"
                      variant="standard"
                      {...register("phone")}
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />

                <Controller
                  name="password"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      {...register("password")}
                      fullWidth
                      type={showPassword ? "text" : "password"}
                      label="Mật khẩu *"
                      variant="standard"
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton onClick={() => setShowPassword((prev) => !prev)} edge="end">
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />

                <Controller
                  name="confirmPassword"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      {...register("confirmPassword")}
                      fullWidth
                      type={showConfirmPassword ? "text" : "password"}
                      label="Nhập lại mật khẩu *"
                      variant="standard"
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowConfirmPassword((prev) => !prev)}
                              edge="end"
                            >
                              {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
                <Controller
                  name="referCode"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      {...register("referCode")}
                      fullWidth
                      label="Mã giới thiệu"
                      variant="standard"
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
                <Controller
                  name="acceptTerms"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          {...register("acceptTerms")}
                          style={{
                            color: error ? "red" : undefined,
                          }}
                        />
                      }
                      label="Tôi đã đọc và đồng ý với điều khoản dịch vụ"
                    />
                  )}
                />
              </Stack>

              <Button
                type="submit"
                style={{
                  color: isMainFieldsValid ? "#fff" : appConfig.color.accent,
                  width: "100%",
                  height: 55,
                  fontSize: "16px",
                  fontWeight: 400,
                  lineHeight: "19.36px",
                  border: "1px solid transparent",
                  borderRadius: 10,
                  padding: "2px",
                  backgroundColor: appConfig.color.primary,
                }}
                variant="contained"
              >
                Đăng ký
              </Button>
              <Typography align="center" color="textSecondary" sx={{ mt: 2, fontSize: 13 }}>
                Bạn đã có tài khoản?{" "}
                <Typography
                  ml={1}
                  fontWeight={700}
                  component="span"
                  style={{
                    cursor: "pointer",
                    textDecoration: "underline",
                    color: appConfig.color.primary,
                  }}
                  onClick={() => navigate(`${Router.login}`)}
                  // onClick={() => navigate(`${Router.otp}`)}
                >
                  Đăng nhập
                </Typography>
              </Typography>
            </form>
          </Container>
        </Box>
      </Stack>
    </FrameContainerWeb>
  );
}
