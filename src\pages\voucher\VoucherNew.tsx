import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Box, Typography, Button, Card, Grid } from "@mui/material";
import { AppDispatch, RootState } from "@/redux/store";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";

import { getProductListByCategory } from "@/redux/slices/product/productListSlice";
import { getListVoucherByUser } from "@/redux/slices/voucher/voucherSlice";
import { useConfigApp } from "@/hooks/useConfigApp";
import Banner from "@/components/home/<USER>";
import VoucherListSlide from "@/components/voucher/VoucherListSlide";
import ProductListGridSlice from "@/components/home/<USER>";
import VoucherListColumn from "@/components/voucher/VoucherListColumn";

interface TabsSectionProps {
  activeTab: "enter-code" | "rewards";
  onTabChange: (tab: "enter-code" | "rewards") => void;
  onRewardClick: () => void;
  onNavigateToVoucherTab: (tab: number) => void;
  onEnterCodeClick: () => void;
}

function TabsSection({
  onRewardClick,
  onNavigateToVoucherTab,
  onEnterCodeClick,
}: TabsSectionProps) {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { userVoucherCount } = useSelector((state: RootState) => state.vouchers);

  // Fetch user's vouchers when component mounts
  useEffect(() => {
    if (user && shopId) {
      dispatch(
        getListVoucherByUser({
          PageIndex: 0,
          PageSize: 1,
          shopId,
        })
      );
    }
  }, [user, shopId]);
  return (
    <Box
      sx={{
        mb: 2,
        mt: -4,
        bgcolor: "rgba(255, 255, 255, 0.95)",
        backdropFilter: "blur(8px)",
        borderRadius: 2,
        mx: 2,
        boxShadow:
          "rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;",
      }}
    >
      <Card sx={{ boxShadow: 0, bgcolor: "transparent", position: "relative" }}>
        <Grid container>
          <Grid item xs={6} sx={{ borderRight: "2px dashed", borderColor: "grey.300" }}>
            <Box
              onClick={onEnterCodeClick}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
                paddingBlock: 1,
                paddingInline: 2,
                cursor: "pointer",
                transition: "background-color 0.2s ease",
              }}
            >
              <img src="icons/voucher.png" height={30} width={30} alt="voucher" />
              <Box>
                <Typography variant="body2" fontWeight={600}>
                  Nhập mã
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.7, fontSize: 12 }}>
                  Mã ưu đãi
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={6}>
            <Box
              onClick={() => {
                onRewardClick();
                // Navigate to tab 1 (Voucher của tôi)
                onNavigateToVoucherTab(1);
              }}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
                paddingBlock: 1,
                paddingInline: 2,
                cursor: "pointer",
              }}
            >
              <img src="icons/gift.png" height={30} width={30} alt="gift" />
              <Box>
                <Typography variant="body2" fontWeight={600}>
                  Phần thưởng
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.7, fontSize: 12 }}>
                  Có {userVoucherCount} ưu đãi
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Card>
    </Box>
  );
}

// Main VoucherApp Component
export function VoucherApp() {
  const [activeTab, setActiveTab] = useState<"enter-code" | "rewards">("enter-code");
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { promotion } = useConfigApp();

  useEffect(() => {
    dispatch(
      getProductListByCategory({
        skip: 0,
        limit: 20,
        parentId: "root",
      })
    );
  }, []);

  const handleRewardClick = () => {
    // Navigate to voucher page with tab 1 (Voucher của tôi)
    navigate(`${Router.voucher.index}?tab=1`);
  };

  const handleNavigateToVoucherTab = (tab: number) => {
    // Navigate to voucher page with specific tab
    navigate(`${Router.voucher.index}?tab=${tab}`);
  };

  const handleEnterCodeClick = () => {
    // Navigate to voucher code entry page
    navigate(Router.voucher.enterCode);
  };

  const renderPromotionComponent = (item: any, index: number) => {
    if (!item.show && item.type !== "Header") return null;
    let com: React.ReactNode;
    switch (item.type) {
      case "VoucherHeader1":
        com = (
          <Box>
            <img src={item.backgroundImage} width={"100%"} />
            <TabsSection
              activeTab={activeTab}
              onTabChange={setActiveTab}
              onRewardClick={handleRewardClick}
              onNavigateToVoucherTab={handleNavigateToVoucherTab}
              onEnterCodeClick={handleEnterCodeClick}
            />
          </Box>
        );
        break;
      case "Voucher1":
        com = (
          <Box key={index}>
            {(() => {
              switch (item.layout) {
                case "slide":
                  return <VoucherListSlide tabIndex={0} config={item} limit={item.limit} />;
                case "column":
                  return <VoucherListColumn tabIndex={0} config={item} limit={item.limit} />;
                default:
                  return <VoucherListColumn tabIndex={0} config={item} limit={item.limit} />;
              }
            })()}
          </Box>
        );
        break;
      case "BannerWithBranch":
        com = (
          <Box key={index} pt={1}>
            <Banner item={item} />
          </Box>
        );
        break;
      case "ProductList1":
        com = (
          <Box key={index} px={2} pt={2}>
            <ProductListGridSlice
              title={item?.style?.title}
              item={item}
              onNavigateToProduction={() => {}}
            />
          </Box>
        );
        break;
      default:
        com = null;
    }
    return com;
  };
  return (
    <Box
      sx={{
        minHeight: "100vh",
      }}
    >
      {promotion?.map((item, index) => renderPromotionComponent(item, index))}
    </Box>
  );
}

export default VoucherApp;
