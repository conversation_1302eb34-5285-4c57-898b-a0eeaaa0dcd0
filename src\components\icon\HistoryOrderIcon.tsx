import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const HistoryOrderIcon: React.FC<IconCustomProps> = ({
  fillColor,
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
    >
      <path
        d="M9.17708 2.08325L5.40625 5.8645"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.8228 2.08325L19.5936 5.8645"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.08325 8.17708C2.08325 6.25 3.1145 6.09375 4.39575 6.09375H20.6041C21.8853 6.09375 22.9166 6.25 22.9166 8.17708C22.9166 10.4167 21.8853 10.2604 20.6041 10.2604H4.39575C3.1145 10.2604 2.08325 10.4167 2.08325 8.17708Z"
        fill={fillColor}
        stroke={fillColor}
        strokeWidth="1.5"
      />
      <path
        d="M3.64575 10.4167L5.1145 19.4167C5.44784 21.4376 6.24992 22.9167 9.22909 22.9167H15.5103C18.7499 22.9167 19.2291 21.5001 19.6041 19.5417L21.3541 10.4167"
        fill={fillColor}
      />
      <path
        d="M3.64575 10.4167L5.1145 19.4167C5.44784 21.4376 6.24992 22.9167 9.22909 22.9167H15.5103C18.7499 22.9167 19.2291 21.5001 19.6041 19.5417L21.3541 10.4167"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M10.1665 14.5835V18.2814"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M14.958 14.5835V18.2814"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default HistoryOrderIcon;
