import type { AxiosInstance, AxiosRequestConfig, Method } from "axios";

import axios from "axios";
import { Router } from "../constants/Route";
import { Platform } from "../config";
import { lang } from "moment";
import { getItem } from "./storage";
import { StorageKeys } from "../constants/storageKeys";

export const axiosInstance: AxiosInstance = axios.create({
  timeout: 12000,
  headers: {
    "Accept-Language": "vi",
  },
  // baseURL: ENDPOINTS._HOST,
});

axiosInstance.interceptors.request.use(
  async (config) => {
    const accessToken = await getItem(StorageKeys.AccessToken);
    if (accessToken) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

axiosInstance.interceptors.response.use(
  (response) => {
    if (response?.data?.message) {
      // $message.success(response.data.message)
    }

    return response?.data;
  },
  (error) => {
    let errorMessage = "";
    if (
      error?.response?.status === 401 ||
      error?.response?.data?.error?.message === "Missing or invalid credentials"
    ) {
      // localStorage.clear();
      // Platform === "web" && (window.location.href = Router.login);
    } else if (error?.response?.status === 400) {
      throw error?.response?.data;
    }
    if (error?.message?.includes("Network Error")) {
      errorMessage = "Network error, please check your connection";
    } else if (error?.response?.data?.error?.details?.moreDetails?.message) {
      errorMessage = error?.response?.data?.error?.details?.moreDetails?.message;
    } else if (error?.response?.data?.error) {
      errorMessage = error?.response?.data.error;
    } else if (error?.message) {
      errorMessage = error.message;
    } else {
      throw error?.response?.data;
    }

    // error.message && $message.error(errorMessage);

    if (errorMessage) {
      throw errorMessage;
    }
  }
);

export type Response<T = any> = {
  status: boolean;
  message: string;
  result: T;
};

export type MyResponse<T = any> = Promise<Response<T>>;

export enum HttpMethod {
  GET = "get",
  POST = "post",
  PUT = "put",
  DELETE = "delete",
}

export const request = <T = any>(
  method: Lowercase<Method>,
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): MyResponse<T> => {
  let prefix = import.meta.env.VITE_API_URL_SYSTEM;

  //Todo hard code demo
  const expose = ["/api/user/"];

  if (expose.some((v) => url.includes(v))) {
    prefix = import.meta.env.VITE_API_URL_SYSTEM;
  }

  url = prefix + url;

  if (method === HttpMethod.POST) {
    return axiosInstance.post(url, data, config);
  } else if (method === HttpMethod.GET) {
    return axiosInstance.get(url, { params: data, ...config });
  } else if (method === HttpMethod.PUT) {
    return axiosInstance.put(url, data, config);
  } else if (method === HttpMethod.DELETE) {
    return axiosInstance.delete(url, { params: data, ...config });
  } else {
    throw new Error(`Unsupported HTTP method: ${method}`);
  }
};

export const get = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): MyResponse<T> => {
  return request<T>(HttpMethod.GET, url, data, config);
};

export const post = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): MyResponse<T> => {
  return request<T>(HttpMethod.POST, url, data, config);
};

export const put = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): MyResponse<T> => {
  return request<T>(HttpMethod.PUT, url, data, config);
};

export const del = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): MyResponse<T> => {
  return request<T>(HttpMethod.DELETE, url, data, config);
};
