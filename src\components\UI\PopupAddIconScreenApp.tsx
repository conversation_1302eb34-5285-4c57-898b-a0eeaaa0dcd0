import React, { useEffect } from "react";
import { Stack, Typography } from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { createShortcut } from "zmp-sdk/apis";
import { COLORS, commonStyle } from "../../constants/themes";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getUrlBeImage } from "@/utils/common";

const createMiniAppShortcut = async () => {
  try {
    await createShortcut({
      params: {
        utm_source: "shortcut",
      },
    });
  } catch (error) {
    // xử lý khi gọi api thất bại
    console.log(error);
  }
};

export default function PopupAddIconScreenApp({
  isOpen,
  closePopup,
}: {
  isOpen?: boolean;
  closePopup: () => void;
}) {
  const appConfig = useConfigApp();

  const [open, setOpen] = React.useState(false);

  useEffect(() => {
    setOpen(isOpen === true);
  }, [isOpen]);

  const handleClose = () => {
    setOpen(false);
    closePopup();
  };
  return (
    <Dialog
      fullWidth
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title" style={commonStyle.headline18}>
        Thêm vào màn hình chờ
      </DialogTitle>
      <DialogContent>
        <Stack>
          <Typography>Chạm và giữ một biểu tượng hoặc chạm</Typography>
          <Typography>Thêm để thêm icon app vào màn hình chờ</Typography>
          <Stack sx={styles.logoContainer}>
            <img
              width={40}
              style={{ borderRadius: "50%" }}
              src={
                typeof appConfig.shopLogo === "string" && appConfig.shopLogo
                  ? appConfig.shopLogo
                  : appConfig.shopLogo?.link || "/images/logo.png"
              }
            />
            <Typography style={{ ...commonStyle.headline12, color: COLORS.neutral3 }}>
              {appConfig?.shopName}
            </Typography>
            <Typography style={{ fontSize: 12, color: COLORS.neutral3 }}>1x1</Typography>
          </Stack>
        </Stack>
      </DialogContent>
      <DialogActions>
        <Stack direction="row" justifyContent="space-around" width={"100%"}>
          <Button onClick={handleClose} style={{ color: COLORS.black }}>
            Thoát
          </Button>
          <Button onClick={createMiniAppShortcut} style={{ color: COLORS.black }}>
            Thêm
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
}

const styles: Record<string, React.CSSProperties> = {
  logoContainer: {
    background: COLORS.neutral10,
    alignItems: "center",
    paddingBlock: 4,
    marginTop: 2,
    borderRadius: 3,
  },
};
