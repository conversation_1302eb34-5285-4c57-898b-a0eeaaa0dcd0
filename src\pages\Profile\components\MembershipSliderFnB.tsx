import React, { useEffect, useState } from "react";
import Slider from "react-slick";
import { Box, Button, List, ListItem, ListItemText, Stack, TextField } from "@mui/material";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { COLORS } from "../../../constants/themes";
import { Icon } from "../../../constants/Assets";
import { useConfigApp } from "@/hooks/useConfigApp";

export default function MembershipSliderFnB() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { user } = useSelector((state: RootState) => state.auth);
  const { color, ...appConfig } = useConfigApp();

  const data =
    user?.membershipLevel || (user?.point && user?.point > 0)
      ? [
          // {
          //   title: "<PERSON>h<PERSON><PERSON> hàng thành viên",
          //   points: 100,
          // },
          // {
          //   title: "Khách hàng VIP",
          //   points: 500,
          // },
          // {
          //   title: "Khách hàng Platinum",
          //   points: 1000,
          //   backGroundColor: COLORS.primary,
          // },
          {
            title: user?.membershipLevel?.levelName ?? "Chưa có xếp hạng",
            points: user?.point,
            logo: user?.membershipLevel?.logo,
          },
        ]
      : [];
  const settings = {
    dots: false,
    infinite: false,
    arrows: false,
    speed: 500,
    slidesToShow: 1,
    centerMode: true,
    centerPadding: "20",
    afterChange: (index) => setCurrentIndex(index),
  };

  useEffect(() => {
    const slickList = document.querySelector(".slick-list") as HTMLElement;
    if (!slickList) return;
    if (currentIndex === 0) {
      slickList.style.paddingLeft = "0";
    } else if (currentIndex === data.length - 1) {
      slickList.style.paddingRight = "0";
    } else {
      slickList.style.padding = "0 20px";
    }
  }, [currentIndex, data]);

  return (
    <Stack>
      {/* <Slider {...settings}> */}
      {data.map((item, index) => (
        <Stack key={index}>
          <div
            style={{
              paddingLeft: index === 0 ? "0px" : "10px",
              paddingRight: index === data.length - 1 ? "0px" : "10px",
            }}
          >
            <div
              style={{
                backgroundColor: `${currentIndex === index ? color.primary : COLORS.neutral11}`,
                borderRadius: "5px",
                padding: "20px 15px 20px",
                color: COLORS.white,
              }}
            >
              <div
                style={{
                  marginBottom: 15,
                }}
              >
                <h3
                  style={{
                    fontSize: "20px",
                    fontWeight: 500,
                  }}
                >
                  {item.title}
                </h3>
              </div>
              <Stack flexDirection={"row"} justifyContent={"space-between"}>
                <Stack flexDirection={"column"} justifyContent={"end"}>
                  {/* <img src={Icon.qrcode} width={50} height={50} /> */}
                  <p>Điểm khả dụng: {item.points}</p>
                </Stack>
                <Stack>
                  {item.logo ? (
                    <img
                      src={item?.logo}
                      style={{
                        maxWidth: "100px",
                      }}
                    />
                  ) : (
                    <Box sx={{ width: "40px", height: "100px" }}></Box>
                  )}
                </Stack>
              </Stack>
            </div>
          </div>
        </Stack>
      ))}
      {/* </Slider> */}
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {};
