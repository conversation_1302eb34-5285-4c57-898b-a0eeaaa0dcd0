import { RootState } from "@/redux/store";
import React from "react";
import { useSelector } from "react-redux";
import Voucher from "../voucher/Voucher";
import Promotion from "./Promotion";

const PromotionIndex: React.FC = () => {
  const shopInfo = useSelector((state: RootState) => state.appInfo.shopInfo);
  const promotion = shopInfo?.template?.promotion?.filter((item: any) => item.type === "Voucher1");

  if (Array.isArray(promotion) && promotion.length > 0) {
    return (
      <>
        {promotion.map((item: any, idx: number) => (
          <Promotion key={idx} {...item} />
        ))}
      </>
    );
  }
  return <Voucher />;
};

export default PromotionIndex;
