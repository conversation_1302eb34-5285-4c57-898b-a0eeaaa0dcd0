import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { persistReducer, persistStore } from "redux-persist";
import storage from "redux-persist/lib/storage"; // sử dụng localStorage

import addressReducer from "./slices/address/addressSlice";
import advertisementReducer from "./slices/advertise/advertisementSlice";
import affiliationReducer from "./slices/affiliation/affiliationSlice";
import alertReducer from "./slices/alert/alert";
import appInfoReducer from "./slices/appInfo/appInfoSlice";
import authReducer from "./slices/authen/authSlice";
import branchSlice from "./slices/branch/branchSlice";
import campaignReducer from "./slices/campaign/campaignSlice";
import cartReducer from "./slices/cart/cartSlice";
import configReducer from "./slices/configSlice";
import membershiplevelReducer from "./slices/membershiplevel/membershiplevelSlice";
import newsListReducer from "./slices/news/newsListSlice";
import notificationReducer from "./slices/notification/notificationSlice";
import orderReducer from "./slices/order/orderSlice";
import paymentReducer from "./slices/paymentMethod/paymentMethodSlice";
import pointHistoryReducer from "./slices/point/pointHistorySlice";
import productListReducer from "./slices/product/productListSlice";
import productReducer from "./slices/product/productSlice";
import teamReducer from "./slices/team/team";
import termReducer from "./slices/term/termSlice";
import voucherReducer from "./slices/voucher/voucherSlice";
import gamificationReducer from "./slices/gamification/gamificationSlice";

// Kết hợp các reducer hiện có (không thay đổi cấu trúc)
const rootReducer = combineReducers({
  auth: authReducer,
  product: productReducer,
  address: addressReducer,
  productList: productListReducer,
  order: orderReducer,
  cart: cartReducer,
  team: teamReducer,
  vouchers: voucherReducer,
  newsList: newsListReducer,
  alert: alertReducer,
  notification: notificationReducer,
  term: termReducer,
  config: configReducer,
  campaign: campaignReducer,
  branch: branchSlice,
  paymentMethod: paymentReducer,
  appInfo: appInfoReducer,
  affiliation: affiliationReducer,
  advertisement: advertisementReducer,
  gamification: gamificationReducer,
  pointHistory: pointHistoryReducer,
  membershiplevel: membershiplevelReducer,
});

const persistConfig = {
  key: "root",
  storage,
  whitelist: ["cart"], // ví dụ: chỉ persist slice "cart"
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

// Ban đầu, kiểu trả về của store.getState() có thể bao gồm các key dưới dạng optional
type FullRootState = ReturnType<typeof store.getState>;

// Tạo một type mới bắt buộc tất cả các key (loại bỏ undefined)
export type RootState = {
  [K in keyof FullRootState]-?: FullRootState[K];
};

export type AppDispatch = typeof store.dispatch;
export const persistor = persistStore(store);

export default store;
