import PopupCommon from "@/components/common/PopupCommon";
import CheckIcon from "@/components/icon/CheckIcon";
import { ZaloIcon } from "@/constants/IconSvg";
import { StorageKeys } from "@/constants/storageKeys";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getAffiliateConfig } from "@/redux/slices/affiliation/affiliationSlice";
import { getTotalStatusOrder, setOrderSearchCondition } from "@/redux/slices/order/orderSlice";
import { IOrderSearchCondition } from "@/types/order";
import { mapError } from "@/utils/common";
import { getItem } from "@/utils/storage";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import {
  Badge,
  Box,
  Button,
  ButtonBase,
  CircularProgress,
  Container,
  Grid,
  <PERSON>ack,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import PopupAddIconScreenApp from "../../components/UI/PopupAddIconScreenApp";
import WarningIcon from "../../components/icon/WarningIcon";
import LayoutHomePage from "../../components/layout/LayoutHomepage";
import { AppEnv, Platform } from "../../config";
import { Icon } from "../../constants/Assets";
import {
  AccountItemType,
  AccountItems,
  IAccountItem,
  MyOrderItemItems,
  MyOrderItemType,
  OrderStatusType,
} from "../../constants/Const";
import { LogoutIcon, RightChevron } from "../../constants/IconSvg";
import { Router } from "../../constants/Route";
import { NumOfLine } from "../../constants/Style";
import { COLORS, commonStyle } from "../../constants/themes";
import { useAlert } from "../../redux/slices/alert/useAlert";
import { authZalo, getUser, getUserZalo, logout } from "../../redux/slices/authen/authSlice";
import { AppDispatch, RootState } from "../../redux/store";
import { useNavigate } from "../../utils/component-util";
import { openChat } from "../../utils/openChat";
import ProfileRetail from "./ProfileRetail";
import MembershipSlider from "./components/MembershipSliderFnB";

export default function ProfileFnB() {
  const { color, ...appConfig } = useConfigApp();
  const {
    orderSearchCondition,
  }: {
    orderSearchCondition: IOrderSearchCondition;
  } = useSelector((state: RootState) => state.order);
  const { user } = useSelector((state: RootState) => state.auth);
  const { isAffiliationConfigActive, affiliateConfig } = useSelector(
    (state: RootState) => state.affiliation
  );
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { showAlert } = useAlert();
  const [openPopup, setOpenPopup] = useState(false);
  const [orderItems, setOrderItems] = useState(MyOrderItemItems);
  const [loading, setLoading] = useState(false);
  const [openLoginPopup, setOpenLoginPopup] = useState(false);
  const calcCartAfterLogin = useCalcCartAfterLogin();

  const isShowAffiliate = isAffiliationConfigActive || user?.affiliationStatus === "Actived";
  const businessType = String(appConfig.businessType ?? "");

  useEffect(() => {
    const getUserInfo = async () => {
      await dispatch(getUser());
    };

    getUserInfo();
  }, []);

  useEffect(() => {
    if (user) {
      fetchOrder();
    }
  }, [user]);

  useEffect(() => {
    if (isShowAffiliate && user?.shopId) {
      dispatch(getAffiliateConfig(user.shopId));
    }
  }, [isShowAffiliate, user?.shopId]);

  const fetchOrder = async () => {
    const result = await dispatch(getTotalStatusOrder());

    const data = result.payload?.data || {};

    const quantityMap = {
      [MyOrderItemType.WaitingConfirm]: "pendding",
      [MyOrderItemType.WaitingForDelivery]: "waitingForDelivery",
      [MyOrderItemType.Delivering]: "delivering",
    };

    const updatedItems = MyOrderItemItems.map((item) => {
      const dataKey = quantityMap[item.id];
      const quantity = dataKey ? data[dataKey] || 0 : 0;
      return { ...item, quantity };
    });

    setOrderItems(updatedItems);
  };

  const onClickRegister = async () => {
    setLoading(true);
    const refCode = await getItem(StorageKeys.RefCode);
    const res = await dispatch(authZalo(refCode)).unwrap();
    if (res && res.idFor) {
      if (Platform == "zalo") await dispatch(getUserZalo());
      await dispatch(getUser());
      await calcCartAfterLogin();
      showAlert({
        icon: <CheckIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
        title: "Kích hoạt tài khoản thành công",
      });
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoading(false);
  };

  const onLogout = () => {
    showAlert({
      icon: <WarningIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
      title: "Đăng xuất",
      content: "Bạn chắc chắn muốn đăng xuất chứ?",
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "OK",
          action: () => {
            dispatch(logout());
            navigate(Router.login);
          },
        },
      ],
    });
  };

  const onClickToActiveProfile = () => {
    showAlert({
      title: appConfig.shopName,
      content: "Vui lòng kích hoạt tài khoản để tiếp tục!",
      icon: "⚠️",
      buttons: [
        {
          title: "Đóng",
          action: () => {
            // navigate(Router.profile.activeProfile);
          },
        },
        {
          title: "Kích hoạt",
          action: () => {
            Platform === "zalo" ? onClickRegister() : setOpenLoginPopup(true);
          },
        },
      ],
    });
  };

  const onClickToItem = (item: IAccountItem) => {
    switch (item.id) {
      case AccountItemType.Payment:
        navigate(Router.profile.payment);
        break;
      case AccountItemType.Address:
        if (!user) {
          onClickToActiveProfile();
          return;
        }
        navigate(Router.profile.address);
        break;
      case AccountItemType.Voucher:
        navigate(Router.voucher.index);
        break;
      case AccountItemType.AppIcon:
        setOpenPopup(true);
        break;
      case AccountItemType.Collab:
        navigate(Router.profile.teamList.index);
        break;
      case AccountItemType.ReferCode:
        navigate(Router.refer.index);
        break;
      case AccountItemType.UserInfo:
        if (!user) {
          onClickToActiveProfile();
          return;
        }
        navigate(Router.profile.info);
        break;
      case AccountItemType.Branch:
        navigate(Router.branch.index);
        break;
      case AccountItemType.Policy:
        navigate(Router.profile.policy.index);
        break;
      case AccountItemType.Support:
        openChat(appConfig.oaId || "");
        break;
    }
  };

  const onClickToShortCutItem = (item: IAccountItem) => {
    if (!user) {
      onClickToActiveProfile();
      return;
    }
    switch (item.id) {
      case MyOrderItemType.WaitingConfirm:
        dispatch(
          setOrderSearchCondition({
            ...orderSearchCondition,
            orderStatus: OrderStatusType.Pending,
          })
        );
        navigate(Router.order.index);
        break;
      case MyOrderItemType.WaitingForDelivery:
        dispatch(
          setOrderSearchCondition({
            ...orderSearchCondition,
            orderStatus: OrderStatusType.WaitingForDelivery,
          })
        );
        navigate(Router.order.index);
        break;
      case MyOrderItemType.Delivering:
        dispatch(
          setOrderSearchCondition({
            ...orderSearchCondition,
            orderStatus: OrderStatusType.Delivering,
          })
        );
        navigate(Router.order.index);
        break;
      case MyOrderItemType.Review:
        dispatch(
          setOrderSearchCondition({
            ...orderSearchCondition,
            orderStatus: OrderStatusType.Success,
          })
        );
        navigate(Router.order.index);
        break;
    }
  };

  const AccountItem = (item: IAccountItem) => {
    const customStyle =
      item.id === AccountItemType.Address || item.id === AccountItemType.Voucher
        ? { height: 65 }
        : {};

    return (
      <ButtonBase
        key={item.id}
        sx={{ ...styles.accountItem, ...customStyle }}
        onClick={() => onClickToItem(item)}
      >
        <Stack direction="row" alignItems="center" gap={1.5}>
          {item.icon()}
          <Stack textAlign={"left"}>
            <span style={{ fontWeight: 500, fontSize: "15px" }}>{item.title}</span>
            <Typography sx={NumOfLine(1)} style={{ color: COLORS.neutral4, fontSize: "11px" }}>
              {item.subTitle}
            </Typography>
          </Stack>
        </Stack>
        {!item.isFirst && <RightChevron width={12} height={21} />}
      </ButtonBase>
    );
  };

  const MenuAccountItem = ({ item, isLast }: { item: IAccountItem; isLast: boolean }) => (
    <ButtonBase
      key={item.id}
      sx={{
        ...styles.menuItem,
        borderBottom: !isLast ? "1px solid #ECECEC" : "none",
      }}
      onClick={() => onClickToItem(item)}
    >
      <Stack direction="row" alignItems="center" gap={1.5}>
        {item.icon()}
        <Stack textAlign={"left"}>
          <span style={{ fontWeight: 500, fontSize: "15px" }}>{item.title}</span>
          <Typography sx={NumOfLine(1)} style={{ color: COLORS.neutral4, fontSize: "11px" }}>
            {item.subTitle}
          </Typography>
        </Stack>
      </Stack>
      <RightChevron width={12} height={21} />
    </ButtonBase>
  );

  const ShortCutItem = (item: IAccountItem) => (
    <ButtonBase sx={styles.shortCutItem} onClick={() => onClickToShortCutItem(item)}>
      <Stack direction="column" alignItems="center" gap={1}>
        <Badge key={item.id} badgeContent={item.quantity} color="error">
          {item.icon()}
        </Badge>
        <Stack>
          <span
            style={{
              fontWeight: 400,
              fontSize: "11px",
              color: COLORS.neutral5,
            }}
          >
            {item.title}
          </span>
        </Stack>
      </Stack>
    </ButtonBase>
  );

  const MyOrderShortCut = () => {
    return (
      <>
        <Stack
          direction="row"
          justifyContent={"space-between"}
          alignItems={"center"}
          sx={{
            marginBottom: "25px",
          }}
        >
          <Typography
            style={{
              color: color.primary,
              fontSize: "18px",
              fontWeight: 700,
            }}
          >
            Đơn hàng của tôi
          </Typography>
          <ButtonBase
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 0.4,
            }}
            onClick={() => {
              if (!user) {
                onClickToActiveProfile();
                return;
              }
              navigate(Router.order.index);
            }}
          >
            <Typography
              style={{
                color: color.primary,
                fontSize: "11px",
                fontWeight: 400,
              }}
            >
              Xem lịch sử mua hàng
            </Typography>
            <RightChevron width={10} height={10} strokeColor={color.primary} />
          </ButtonBase>
        </Stack>
        <Grid
          container
          spacing={1.3}
          style={{
            marginBottom: "25px",
          }}
        >
          {orderItems.map((item, idx) => (
            <Grid key={item.id} item xs={3}>
              <ShortCutItem {...item} />
            </Grid>
          ))}
        </Grid>
        {isShowAffiliate && (
          <Button
            style={{
              width: "100%",
              padding: 12,
              borderRadius: 99,
              background: color.primary,
              color: COLORS.white,
            }}
            onClick={() => {
              if (!user) {
                onClickToActiveProfile();
                return;
              }
              if (user?.affiliationStatus === "Actived") {
                navigate(Router.collabhistory.index);
              } else {
                navigate(Router.profile.sigupPartner);
              }
            }}
          >
            {user?.affiliationStatus === "Actived" ? "Affiliate Hub" : "Đăng ký Affiliate"}
          </Button>
        )}
      </>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <Stack style={styles.activeAccount}>
          <CircularProgress />
        </Stack>
      );
    }
    return (
      <Box>
        {user && (user?.point > 0 || user?.membershipLevel) && (
          <Stack style={styles.profileContainer}>
            <MembershipSlider
              key={`${user?.point}-${user?.membershipLevel?.levelName}-${user?.membershipLevel?.logo}`}
            />
          </Stack>
        )}
        <Stack
          style={{
            ...styles.profileContainer,
            padding: "10px 20px 25px ",
            marginTop: "10px",
          }}
        >
          <MyOrderShortCut />
        </Stack>
        <Stack style={styles.profileContainer}>
          <div>
            <Grid container spacing={1.3}>
              {AccountItems.First.map((item, idx) => (
                <Grid key={item.id} item xs={6}>
                  <AccountItem {...item} />
                </Grid>
              ))}
            </Grid>
          </div>
          <Stack
            style={{
              ...styles.sectionContainer,
              border: "1px solid #ECECEC",
              borderRadius: "5px",
            }}
            mt={"10px"}
          >
            {AccountItems.Second.map((item, idx) => {
              const isLast = idx === AccountItems.Second.length - 1;
              return (
                <Box key={item.id}>
                  <MenuAccountItem item={item} isLast={isLast} />
                </Box>
              );
            })}
          </Stack>
          {Platform === "web" && user && (
            <Stack sx={styles.logoutBtn} direction="row" gap={1} onClick={onLogout}>
              <LogoutIcon />
              <Stack>
                <span style={{ fontWeight: 700, color: "red" }}>Đăng xuất</span>
              </Stack>
            </Stack>
          )}
          <Box
            sx={{
              color: "#8B8B8B",
              display: "flex",
              alignItems: "center",
              gap: "5px",
              padding: "20px 10px 0 10px",
              justifyContent: "center",
              fontWeight: "500",
              fontSize: "11px",
            }}
          >
            POWERED BY <img height={"15px"} src="demo/logo/logo-evotech.svg" />
          </Box>
          <PopupAddIconScreenApp isOpen={openPopup} closePopup={() => setOpenPopup(false)} />
        </Stack>
      </Box>
    );
  };

  const profileComponentMap: Record<string, React.ComponentType> = {
    Retail: ProfileRetail,
  };

  if (profileComponentMap?.[businessType]) {
    const Component = profileComponentMap[businessType];
    return <Component />;
  }

  return (
    <LayoutHomePage
      title={
        <Container style={{ margin: "15px auto 8px 0", paddingLeft: 10 }}>
          <Stack direction="row" gap={1} alignItems={"center"} paddingBottom={1}>
            <Stack
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "transparent",
                borderRadius: "50%",
                border: "1px solid white",
                padding: 1,
              }}
            >
              <img
                width={35}
                height={35}
                style={{ borderRadius: "50%", objectFit: "cover" }}
                src={
                  user?.avatar ??
                  (typeof appConfig.shopLogo === "string" && appConfig.shopLogo
                    ? appConfig.shopLogo
                    : appConfig.shopLogo?.link) ??
                  Icon.logo
                }
                alt="Logo"
              />
            </Stack>
            <Stack
              style={{
                ...commonStyle.headline13,
                color: color.accent,
              }}
            >
              <span style={{ ...styles.shopName, color: color.primary }}>
                {user?.fullname ?? user?.phoneNumber}
              </span>
            </Stack>
          </Stack>
        </Container>
      }
    >
      <Box>
        {renderContent()}
        {AppEnv === "dev" && (
          <Button
            variant="contained"
            color="primary"
            size="small"
            onClick={async () => {
              dispatch(logout());
            }}
          >
            Log out
          </Button>
        )}

        <PopupCommon
          open={openLoginPopup}
          setOpen={setOpenLoginPopup}
          content={
            <>
              <Typography fontSize={22} fontWeight={700} color={COLORS.black} mb={1} align="center">
                Yêu cầu đăng nhập
              </Typography>
              <Typography fontSize={14} align="center" style={{ color: "#A5A5A5" }} mb={3} px={2}>
                Để sử dụng chức năng này, bạn cần đăng nhập để sử dụng
              </Typography>
              <Box display="flex" flexDirection="column" gap={1}>
                {Platform == "web" && (
                  <Button
                    variant="contained"
                    startIcon={<AccountCircleIcon />}
                    color="primary"
                    style={{
                      color: color.accent,
                      backgroundColor: color.primary,
                      paddingBlock: 10,
                      height: 60,
                      fontWeight: 500,
                      fontSize: 14,
                    }}
                    fullWidth
                    onClick={() => navigate(`${Router.login}`)}
                  >
                    Đăng nhập với tài khoản
                  </Button>
                )}
                {Platform === "zalo" && (
                  <Button
                    variant="outlined"
                    startIcon={<ZaloIcon />}
                    style={{
                      color: color.primary,
                      backgroundColor: color.accent,
                      paddingBlock: 10,
                      height: 60,
                      fontWeight: 500,
                      fontSize: 14,
                    }}
                    fullWidth
                    onClick={onClickRegister}
                  >
                    Đăng nhập với Zalo
                  </Button>
                )}
              </Box>
              {Platform === "web" && (
                <Typography align="center" color="textSecondary" fontSize={11} mt={1} mb={3}>
                  Bạn chưa có tài khoản?{" "}
                  <Typography
                    component="span"
                    style={{
                      cursor: "pointer",
                      textDecoration: "underline",
                      color: color.primary,
                    }}
                    onClick={() => navigate(`${Router.register}`)}
                  >
                    Đăng ký
                  </Typography>
                </Typography>
              )}
            </>
          }
        />
      </Box>
    </LayoutHomePage>
  );
}

const styles: Record<string, React.CSSProperties> = {
  profileContainer: {
    background: COLORS.white,
    padding: "20px 20px 5px 20px",
    marginBottom: "10px",
  },
  sectionContainer: {
    background: COLORS.white,
    padding: 0,
    borderRadius: 10,
    color: COLORS.black,
  },
  accountItem: {
    width: "100%",
    display: "flex",
    padding: "14px",
    flexDirection: "row",
    gap: "4px",
    justifyContent: "space-between",
    alignItems: "center",
    borderRadius: "5px",
    border: "1px solid #ECECEC",
  },
  menuItem: {
    width: "100%",
    display: "flex",
    padding: "14px",
    flexDirection: "row",
    gap: "4px",
    justifyContent: "space-between",
    alignItems: "center",
  },
  logoutBtn: {
    fontWeight: 400,
    color: "#000",
    borderRadius: 99,
    background: "#fff",
    padding: "10px 16px",
    justifyContent: "center",
    alignItems: "center",
  },
  shopName: {
    fontWeight: 700,
    fontSize: "16px",
    width: `calc(100vw - 180px)`,
    display: "block",
    whiteSpace: "normal",
    overflow: "hidden",
    textOverflow: "ellipsis",
    lineHeight: 1.3,
  },
  shortCutItem: {
    textAlign: "center",
    width: "100%",
  },
  activeAccount: {
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    height: 500,
    borderRadius: 20,
  },
};
