import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const StoreIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
    >
      <path
        d="M3.13543 11.6875V16.3646C3.13543 21.0417 5.01043 22.9167 9.68751 22.9167H15.3021C19.9792 22.9167 21.8542 21.0417 21.8542 16.3646V11.6875"
        fill={fillColor}
      />
      <path
        d="M3.13543 11.6875V16.3646C3.13543 21.0417 5.01043 22.9167 9.68751 22.9167H15.3021C19.9792 22.9167 21.8542 21.0417 21.8542 16.3646V11.6875"
        stroke={fillColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 12.5002C14.4062 12.5002 15.8125 10.9481 15.625 9.04183L14.9375 2.0835H10.0729L9.37499 9.04183C9.18749 10.9481 10.5937 12.5002 12.5 12.5002Z"
        fill={fillColor}
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.073 12.5002C21.1771 12.5002 22.7188 10.7918 22.5105 8.69808L22.2188 5.8335C21.8438 3.12516 20.8021 2.0835 18.073 2.0835H14.8959L15.625 9.38558C15.8021 11.1043 17.3542 12.5002 19.073 12.5002Z"
        fill={fillColor}
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.87488 12.5002C7.59363 12.5002 9.14571 11.1043 9.31238 9.38558L9.54154 7.0835L10.0415 2.0835H6.86446C4.13529 2.0835 3.09363 3.12516 2.71863 5.8335L2.43738 8.69808C2.22904 10.7918 3.77071 12.5002 5.87488 12.5002Z"
        fill={fillColor}
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 17.7085C10.7604 17.7085 9.89584 18.5731 9.89584 20.3127V22.9168H15.1042V20.3127C15.1042 18.5731 14.2396 17.7085 12.5 17.7085Z"
        fill="white"
      />
    </svg>
  );
};

export default StoreIcon;
