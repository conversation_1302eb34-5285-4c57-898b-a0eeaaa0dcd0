import React, { useState } from "react";
import { Box, Button, Divider, FormControlLabel, Radio, Stack, Typography } from "@mui/material";
import { COLORS, commonStyle } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import BottomSheet from "@/components/common/BottomSheet";

interface RegisterPartnerSheetProps {
  open: boolean;
  onClose: () => void;
  onOpen: () => void;
  onRegister: () => void;
}

export default function RegisterPartnerSheet({
  open,
  onClose,
  onOpen,
  onRegister,
}: RegisterPartnerSheetProps) {
  const { color, ...appConfig } = useConfigApp();
  const { recruitmentPage } = useSelector((state: RootState) => state.affiliation);
  const { user } = useSelector((state: RootState) => state.auth);
  const [checked, setChecked] = useState(false);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
  };

  const loyaltyPolicy = recruitmentPage?.content;
  const handleClosePopup = () => {
    setChecked(false);
    onClose();
  };

  const sheetContent = (
    <Stack gap={1.5}>
      <Box>
        <img
          style={styles.imageContainer}
          src={recruitmentPage?.bannerFilePath || "/demo/images/banner.png"}
        />
      </Box>
      <Box style={styles.contentBox}>
        <Typography
          style={{
            ...commonStyle.headline18,
            textAlign: "center",
            color: color.primary,
            marginBottom: 20,
          }}
        >
          {recruitmentPage?.title ||
            `Chào mừng bạn đến với chương trình Affiliate ${appConfig?.shopName}`}
        </Typography>
        <Box
          dangerouslySetInnerHTML={{
            __html: loyaltyPolicy,
          }}
          style={styles.contentBoxStyle}
        />
      </Box>
    </Stack>
  );

  const sheetFooter = (
    <Box sx={{ paddingInline: 2, paddingBlock: 1, background: COLORS.white, textAlign: "center" }}>
      <FormControlLabel
        sx={{
          paddingInline: 2,
          marginRight: 0,
          "& .MuiRadio-root": {
            padding: 0,
            marginRight: 1,
          },
          "& .MuiFormControlLabel-label": {
            fontSize: 13,
          },
        }}
        control={<Radio checked={checked} onChange={handleChange} />}
        label={`Tôi đã hiểu và đồng ý với chính sách Affiliate của ${appConfig?.shopName}`}
      />
      <Button
        style={{
          ...styles.btnReg,
          background: checked ? color.primary : COLORS.neutral6,
        }}
        onClick={() => {
          onRegister();
          setChecked(false);
        }}
        disabled={!checked}
      >
        {recruitmentPage?.navBarContent || "Đăng ký Affiliate"}
      </Button>
    </Box>
  );

  return (
    <>
      <BottomSheet
        open={open}
        onOpen={onOpen}
        onClose={handleClosePopup}
        sx={{ bgcolor: "white" }}
        footer={sheetFooter}
        disableSwipeToOpen
      >
        {sheetContent}
      </BottomSheet>
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  imageContainer: {
    display: "flex",
    borderRadius: 5,
    overflow: "hidden",
    width: "100%",
    backgroundSize: "cover",
    backgroundPosition: "center",
  },
  contentBox: {
    background: COLORS.white,
    borderRadius: "5px",
    paddingInline: 20,
  },
  contentBoxStyle: {},
  btnReg: {
    width: "100%",
    padding: 12,
    borderRadius: 99,
    color: COLORS.white,
    marginTop: "11px",
  },
};
