import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";
import ProfileFB from "./ProfileFnB";
import ProfileRetail from "./ProfileRetail";

const componentMap = {
  FB: ProfileFB,
  Retail: ProfileRetail,
  Other: ProfileRetail,
};

export default function Profile(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FB");
  const Component = componentMap[businessType] || componentMap.FB;
  return <Component {...props} />;
}
