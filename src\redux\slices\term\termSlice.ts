import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ITerm } from "../../../types/term";
import { request } from "../../../utils/request";

interface TermState {
  termList: ITerm[];
  termDetail: ITerm | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: TermState = {
  termList: [],
  termDetail: null,
  isLoading: true,
  error: null,
};

export const getTermList = createAsyncThunk(
  "termList/getTermList",
  async () => {
    const response = await request("get", "/api/terms");
    return response;
  },
);

export const getTermDetail = createAsyncThunk(
  "termList/getTermDetail",
  async (id: string) => {
    const response = await request("get", `/api/terms/${id}`);
    return response;
  },
);
const termSlice = createSlice({
  name: "termList",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder;
  },
});
export default termSlice.reducer;
