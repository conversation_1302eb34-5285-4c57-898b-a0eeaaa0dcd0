import React from "react";
import { IconCustomProps } from "@/components/icon/type";

const LocationIcon: React.FC<IconCustomProps> = ({
  primaryColor,
  secondaryColor,
  className,
}) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.52512 10.6125C6.98762 -0.212497 23.0251 -0.199996 25.4751 10.625C26.9126 16.975 22.9626 22.35 19.5001 25.675C16.9876 28.1 13.0126 28.1 10.4876 25.675C7.03762 22.35 3.08762 16.9625 4.52512 10.6125Z"
        fill={secondaryColor}
        stroke={primaryColor}
        strokeWidth="1.5"
      />
      <path
        d="M18.15 12.8873C18.15 14.627 16.7397 16.0373 15 16.0373C13.2603 16.0373 11.85 14.627 11.85 12.8873C11.85 11.1476 13.2603 9.7373 15 9.7373C16.7397 9.7373 18.15 11.1476 18.15 12.8873Z"
        fill={primaryColor}
        stroke={primaryColor}
        strokeWidth="1.5"
      />
    </svg>
  );
};

export default LocationIcon;
