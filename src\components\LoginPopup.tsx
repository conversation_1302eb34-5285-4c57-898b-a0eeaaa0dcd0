import React from "react";

import { Box, Button, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import { ZaloIcon } from "@/constants/IconSvg";
import { Platform } from "@/config";
import PopupCommon from "@/components/common/PopupCommon";
import { COLORS } from "@/constants/themes";
import { Router } from "@/constants/Route";
import { useConfigApp } from "@/hooks/useConfigApp";

interface LoginPopupProps {
  open: boolean;
  onClose: () => void;
  onClickRegister: () => void;
  loading?: boolean;
}

const LoginPopup = ({ open, onClose, onClickRegister, loading }: LoginPopupProps) => {
  const navigate = useNavigate();
  const { color } = useConfigApp();

  return (
    <PopupCommon
      open={open}
      setOpen={onClose}
      content={
        <>
          <Typography fontSize={22} fontWeight={700} color={COLORS.black} mb={1} align="center">
            <PERSON><PERSON><PERSON> c<PERSON>u đăng nhập
          </Typography>
          <Typography fontSize={14} align="center" style={{ color: "#A5A5A5" }} mb={3} px={2}>
            Để sử dụng chức năng này, bạn cần đăng nhập để sử dụng
          </Typography>
          <Box display="flex" flexDirection="column" gap={1}>
            {Platform === "web" && (
              <Button
                variant="contained"
                startIcon={<AccountCircleIcon />}
                color="primary"
                style={{
                  color: color.accent,
                  backgroundColor: color.primary,
                  paddingBlock: 10,
                  height: 60,
                  fontWeight: 500,
                  fontSize: 14,
                }}
                fullWidth
                onClick={() => navigate(Router.login)}
              >
                Đăng nhập với tài khoản
              </Button>
            )}
            {Platform === "zalo" && (
              <Button
                variant="outlined"
                startIcon={<ZaloIcon />}
                style={{
                  color: color.primary,
                  backgroundColor: color.accent,
                  paddingBlock: 10,
                  height: 60,
                  fontWeight: 500,
                  fontSize: 14,
                }}
                fullWidth
                onClick={onClickRegister}
                disabled={loading}
              >
                Đăng nhập với Zalo
              </Button>
            )}
          </Box>
          {Platform === "web" && (
            <Typography align="center" color="textSecondary" fontSize={11} mt={1} mb={3}>
              Bạn chưa có tài khoản?{" "}
              <Typography
                component="span"
                style={{
                  cursor: "pointer",
                  textDecoration: "underline",
                  color: color.primary,
                }}
                onClick={() => navigate(Router.register)}
              >
                Đăng ký
              </Typography>
            </Typography>
          )}
        </>
      }
    />
  );
};

export default LoginPopup;
